package com.cowell.iscm.enums;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * 退仓业务类型枚举
 * <AUTHOR>
 */

public enum ReturnWarehouseBusinessTypeEnum {

    /**
     业务类型 1:未调拨退仓 2:大库存退仓 3:滞销退仓 4:大库存&滞销退仓 5:库存成本金额退仓
     */
    NO_ALLOT_RETURN((byte)1, "未调拨退仓"),
    STOCK_RETURN((byte)2, "大库存退仓"),
    UNSALABLE_RETURN((byte)3, "滞销退仓"),
    UNSALABLE_STOCK_RETURN((byte)4, "大库存&滞销退仓"),
    STOCK_AMOUNT_TOP_RETURN((byte)5, "库存成本金额退仓"),
    IMPORT_RETURN((byte)6, "手工导入退仓"),
    DEFECTIVE_RETURN((byte)7, "不良品处理"),
    UNMANAGE_GOODS_RETURN((byte)8, "不经营品退仓"),
    ;

    ReturnWarehouseBusinessTypeEnum(byte code, String name) {
        this.code = code;
        this.name = name;
    }

    private byte code;
    private String name;

    public byte getCode() {
        return code;
    }

    public void setCode(byte code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    /**
     * 根据code获取name
     * @return name
     */
    public static String getNameByCode(byte code) {
        for (ReturnWarehouseBusinessTypeEnum returnWarehouseBusinessTypeEnum : ReturnWarehouseBusinessTypeEnum.values()) {
            if (returnWarehouseBusinessTypeEnum.getCode() == code) {
                return returnWarehouseBusinessTypeEnum.getName();
            }
        }
        return null;
    }

    /**
     * 根据code获取enum
     * @return enum
     */
    public static ReturnWarehouseBusinessTypeEnum getEnumByCode(byte code) {
        for (ReturnWarehouseBusinessTypeEnum returnWarehouseBusinessTypeEnum : ReturnWarehouseBusinessTypeEnum.values()) {
            if (returnWarehouseBusinessTypeEnum.getCode() == code) {
                return returnWarehouseBusinessTypeEnum;
            }
        }
        return null;
    }

    /**
     * 根据name获取enum
     * @return enum
     */
    public static ReturnWarehouseBusinessTypeEnum getEnumByName(String name) {
        for (ReturnWarehouseBusinessTypeEnum returnWarehouseBusinessTypeEnum : ReturnWarehouseBusinessTypeEnum.values()) {
            if (returnWarehouseBusinessTypeEnum.getName().equals(name)) {
                return returnWarehouseBusinessTypeEnum;
            }
        }
        return null;
    }

    public static List<RegisterDataTypeEnum> getDataTypeByCode (byte code) {
        if (code == ReturnWarehouseBusinessTypeEnum.STOCK_RETURN.code) {
            return Lists.newArrayList(RegisterDataTypeEnum.STOCK);
        }
        if (code == ReturnWarehouseBusinessTypeEnum.UNSALABLE_RETURN.code) {
            return Lists.newArrayList(RegisterDataTypeEnum.UNSALABLE);
        }
        if (code == ReturnWarehouseBusinessTypeEnum.UNSALABLE_STOCK_RETURN.code) {
            return Lists.newArrayList(RegisterDataTypeEnum.STOCK, RegisterDataTypeEnum.UNSALABLE);
        }
        return null;
    }

    public static ReturnWarehouseBusinessTypeEnum getEnumByDataTypeCode (byte code) {
        if (code == RegisterDataTypeEnum.STOCK.getCode()) {
            return ReturnWarehouseBusinessTypeEnum.STOCK_RETURN;
        }
        if (code == RegisterDataTypeEnum.UNSALABLE.getCode()) {
            return ReturnWarehouseBusinessTypeEnum.UNSALABLE_RETURN;
        }
        if (code == RegisterDataTypeEnum.UNSALABLE_STOCK.getCode()) {
            return ReturnWarehouseBusinessTypeEnum.UNSALABLE_STOCK_RETURN;
        }
        return null;
    }
}

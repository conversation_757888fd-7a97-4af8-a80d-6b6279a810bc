package com.cowell.iscm.enums;

public enum  ApplyParamCodeEnum {
    ON_WAY_STOCK_CALCULATION("ISCM-APPLY-0001", "在途库存", ""),
    UNABLE_STOCK_CALCULATION("ISCM-APPLY-0002", "不可用库存", ""),
    GOODS_LEVEL("ISCM-APPLY-0003", "商品等级", ""),
    GOODS_LEVEL_LIMIT_DAYS("ISCM-APPLY-0004", "商品等级上下限天数", ""),
    SPECIAL_CTRL_GOODS_SWITCH("ISCM-APPLY-0005", "是否开启特管商品请货量管控", "GJ0181"),
    SPECIAL_GOODS_ONCE_APPLY_LIMIT("ISCM-APPLY-0006", "特管商品单次请货上限", "GJ0182"),
    SPECIAL_GOODS_PER_THIRTY_DAYS_APPLY_LIMIT("ISCM-APPLY-0007", "特管商品前推30天请货上限", "GJ0183"),
    STORE_AUTO_APPLY_TIME("ISCM-APPLY-0008", "门店自动请货单生成时间", ""),
    MIDDLE_DEAL_SWITCH("ISCM-APPLY-0009", "自动请货数量,是否启用中包装处理", "GJ0269"),
    REMAINDER_DEAL("ISCM-APPLY-0010", "一个以上中包装商品,余数处理方式", "GJ0269,GJ9084"),

    NON_AUTOMATIC_APPLY_NEW_STORE_MONTH("ISCM-APPLY-0011", "不自动请货新店月份定义", ""),
    NON_AUTOMATIC_APPLY_NEW_GOODS_DAY("ISCM-APPLY-0012", "不自动请货新品天数定义", ""),
    NON_AUTOMATIC_APPLY_STORE_PROPERTY("ISCM-APPLY-0013", "不自动请货门店属性", ""),
    NON_AUTOMATIC_APPLY_GOODS_CLASS("ISCM-APPLY-0014", "不自动请货商品类型", ""),
    NON_AUTOMATIC_APPLY_GOODS_MANAGEMENT_ATTR("ISCM-APPLY-0015", "不自动请货商品经营属性", "GJ9327"),
    NON_AUTOMATIC_APPLY_GOODS_SIGN("ISCM-APPLY-0016", "不自动请货商品标记", ""),

    GOODS_CATALOG_LIMIT("ISCM-APPLY-0017", "请货上下限商品目录", ""),
    GOODS_UPPER_LIMIT("ISCM-APPLY-0018", "请货上限控制", ""),
    GOODS_UPPER_LIMIT_ITEM("ISCM-APPLY-0019", "请货行数上限控制", ""),

    NON_AUTOMATIC_APPLY_STORE("ISCM-APPLY-0020", "不自动请货门店", ""),
    STORE_AUTO_APPLY_FLOAT("ISCM-APPLY-0021", "门店自动请货上浮", ""),
    CAN_MODIFY_ITEM_RATIO("ISCM-APPLY-0022", "企业请货可修改条目比例（%）", "GJ0103"),
    CAN_MODIFY_QTY_UPPER_RATIO("ISCM-APPLY-0023", "企业请货数量可上调比例（%）", "GJ0025"),
    CAN_MODIFY_QTY_LOWER_RATIO("ISCM-APPLY-0024", "企业请货数量可下调比例（%）", "GJ9568"),
    GOODS_BLACKLIST_LIMIT("ISCM-APPLY-0025", "请货门店商品黑名单", ""),
    GOODS_WHITELIST_LIMIT("ISCM-APPLY-0026", "请货门店商品白名单", ""),

    CAN_MODIFY_ITEM_RATIO_ZY("ISCM-APPLY-0027", "企业请货可修改条目比例（%）-中药饮片", "GJ0103"),
    CAN_MODIFY_QTY_UPPER_RATIO_ZY("ISCM-APPLY-0028", "企业请货数量可上调比例（%）-中药饮片", "GJ0025"),
    CAN_MODIFY_QTY_LOWER_RATIO_ZY("ISCM-APPLY-0029", "企业请货数量可下调比例（%）-中药饮片", "GJ9568"),
    COMBINED_CODE_RANGE("ISCM-APPLY-0030", "启用组合码范围逻辑", ""),
    ZY_MIDDLE_CATEGORY("ISCM-APPLY-0031", "直营门店 启用中码组逻辑的商品类型", "GJ0432"),
    JM_MIDDLE_CATEGORY("ISCM-APPLY-0032", "加盟门店 启用中码组逻辑的商品类型", "GJ0433"),
    CUT_APPLY_GOODS_TYPE("ISCM-APPLY-0033", "非必要请货建议削减", ""),

    ;
    private final String code;
    private final String name;
    private final String hdCode;

    ApplyParamCodeEnum(String code, String name, String hdCode) {
        this.code = code;
        this.name = name;
        this.hdCode = hdCode;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getHdCode() {
        return hdCode;
    }

    public static ApplyParamCodeEnum getEnumByCode(String code) {
        if (null == code) {
            return null;
        }
        for (ApplyParamCodeEnum item : ApplyParamCodeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        for (ApplyParamCodeEnum item : ApplyParamCodeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item.getName();
            }
        }
        return "";
    }

}

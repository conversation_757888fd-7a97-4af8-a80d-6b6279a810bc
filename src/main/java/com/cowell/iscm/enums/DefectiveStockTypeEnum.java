package com.cowell.iscm.enums;

/**
 * 不良库存类型
 */
public enum DefectiveStockTypeEnum {
    EXPIRY(1, "效期品"),
    SUBSCRIPTION(2, "定淘品"),
    UNMANAGE(3, "不经营品"),
    ;

    private final Integer code;
    private final String name;

    DefectiveStockTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static DefectiveStockTypeEnum getEnumByCode(Integer code) {
        if (null == code) {
            return null;
        }
        for (DefectiveStockTypeEnum item : DefectiveStockTypeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }

    public static DefectiveStockTypeEnum getEnumByName(String name) {
        if (null == name) {
            return null;
        }
        for (DefectiveStockTypeEnum item : DefectiveStockTypeEnum.values()) {
            if (item.getName().equals(name)) {
                return item;
            }
        }
        return null;
    }

    public static String getNameByCode(Integer code) {
        for (DefectiveStockTypeEnum item : DefectiveStockTypeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item.getName();
            }
        }
        return "";
    }

}

package com.cowell.iscm.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.iscm.rest.errors.BusinessErrorException;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.Charset;
import java.util.*;

@Component
public class MBUtils<T> {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private RestTemplate restTemplate;
    /**
     * 获取url
     * @param json
     * @return
     */
    public static String getMbUrl(String json) {
        JSONObject jsonObject = JSONObject.parseObject(json);
        return jsonObject.get("url").toString();
    }

    /**
     * 获取userName
     * @param json
     * @return
     */
    public static String getUserName(String json) {
        JSONObject jsonObject = JSONObject.parseObject(json);
        return jsonObject.get("userName").toString();
    }

    /**
     * 获取password
     * @param json
     * @return
     */
    public static String getPassword(String json) {
        JSONObject jsonObject = JSONObject.parseObject(json);
        return jsonObject.get("password").toString();
    }

    /**
     * MB 配置
     * @return
     */
    public static Map<String, Object> getMbConfig(String json) {
        JSONObject jsonObject = JSONObject.parseObject(json);
        Map<String, Object> mbConfig = new HashMap<>();
        mbConfig.put("bdatetime", DateUtils.conventDateStrByDate(new Date()));
        mbConfig.put("bdestination", jsonObject.get("destination"));
        mbConfig.put("bguid", "ISCM" + UUID.randomUUID().toString());
        mbConfig.put("bsource", jsonObject.get("source"));
        mbConfig.put("bstatus", jsonObject.get("status"));
        if (jsonObject.containsKey("type")) {
            mbConfig.put("btype", jsonObject.get("type"));
        }
        mbConfig.put("bversion", jsonObject.get("version"));
        return mbConfig;
    }

    /**
     * 组装请求数据
     * @param mbConfig mb配置map
     * @param data 请求参数
     * @return
     * @throws JsonProcessingException
     */
    public static String assembleParam(Map<String, Object> mbConfig, String data) throws JsonProcessingException {
        mbConfig.put("bdata", data);
        List<Map<String, Object>> mbTable = new ArrayList<>();
        mbTable.add(mbConfig);
        Map<String, List<Map<String, Object>>> mbMsg = new HashMap<>();
        mbMsg.put("Table", mbTable);
        return JacksonUtil.getObjectMapper().writeValueAsString(mbMsg);
    }

    public static String assembleParam(Map<String, Object> mbConfig, String data, String compId) throws JsonProcessingException {
        if (StringUtils.isBlank(compId)) {
            throw new BusinessErrorException("MbUtils.assembleParam: 参数compId不能为空!");
        }
        mbConfig.put("bdata", data);
        List<Map<String, Object>> mbTable = new ArrayList<>();
        mbTable.add(mbConfig);
        Map<String, Object> mbMsg = new HashMap<>();
        mbMsg.put("Table", mbTable);
        // 增加SOPCompanyCode by lxc
        mbMsg.put("compId", compId);
        return JacksonUtil.getObjectMapper().writeValueAsString(mbMsg);
    }

//    /**
//     * 组装请求数据 table为数组
//     * @param mbConfig mb配置map
//     * @param data 请求参数
//     * @return
//     * @throws JsonProcessingException
//     */
//    public static String assembleParam(Map<String, Object> mbConfig, Object data) throws JsonProcessingException {
//        mbConfig.put("bdata", data);
//        List<Map<String, Object>> mbTable = new ArrayList<>();
//        mbTable.add(mbConfig);
//        Map<String, List<Map<String, Object>>> mbMsg = new HashMap<>();
//        mbMsg.put("Table", mbTable);
//        return JacksonUtil.getObjectMapper().writeValueAsString(mbMsg);
//    }

    /**
     * 组装请求数据 table为数组
     * @param mbConfig mb配置map
     * @param data 请求参数
     * @return
     * @throws JsonProcessingException
     */
    public static String assembleParam2Obj(Map<String, Object> mbConfig, String data) throws JsonProcessingException {
        mbConfig.put("bdata", data);
        Map<String, Object> mbMsg = new HashMap<>();
        mbMsg.put("Table", mbConfig);
        return JacksonUtil.getObjectMapper().writeValueAsString(mbMsg);
    }

    /**
     * 组装请求数据 table为对象
     * @param mbConfig mb配置map
     * @param data 请求参数
     * @return
     * @throws JsonProcessingException
     */
    public static String assembleParam2Obj(Map<String, Object> mbConfig, Object data) throws JsonProcessingException {
        mbConfig.put("bdata", data);
        Map<String, Object> mbMsg = new HashMap<>();
        mbMsg.put("Table", mbConfig);
        return JacksonUtil.getObjectMapper().writeValueAsString(mbMsg);
    }

    /**
     * 推送至MB
     * @param mbUrl url地址
     * @param jsonParam 参数
     * @return
     * @throws Exception
     */
    @Retryable(value = RestClientException.class, maxAttempts = 3,
            backoff = @Backoff(delay = 5000L, multiplier = 1))
    public String pushToMB(String mbUrl, String jsonParam) throws Exception {
        try {
//            logger.info("MBUtils|发送消息到MB: {}", jsonParam);
            logger.info("MBUtils|MB address: {}", mbUrl);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> request = new HttpEntity<>(jsonParam, headers);
            restTemplate.getMessageConverters().forEach(v -> {
                if (v instanceof StringHttpMessageConverter) {
                    ((StringHttpMessageConverter) v).setDefaultCharset(Charset.forName("UTF-8"));
                }
            });
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(mbUrl, request, String.class);
            logger.info("MBUtils|pushToMB|HttpStatus: {}", responseEntity.getStatusCode());
            // 判断请求是否发生异常
            if (!responseEntity.getStatusCode().is2xxSuccessful()) {
                logger.info("MBUtils|pushToMB|请求失败 -> {}", jsonParam);
                throw new RestClientException(responseEntity.getBody());
            }
            if (HttpStatus.OK.equals(responseEntity.getStatusCode()) || HttpStatus.ACCEPTED.equals(responseEntity.getStatusCode())) {
                logger.info("MBUtils|pushToMB成功");
//                logger.info("MBUtils|pushToMB成功 -> {}", jsonParam);
            }
            return responseEntity.getBody();
        } catch (Exception e) {
            if (e instanceof RestClientException) {
                logger.error("MBUtils|pushToMB|请求异常,即将进行重试 -> {}", jsonParam, e);
                throw e;
            }
            logger.error("MBUtils|pushToMB|发生异常", e);
            throw e;
        }
    }

    /**
     * 推送至MB
     * @param mbUrl url地址
     * @param jsonParam 参数
     * @return
     * @throws Exception
     */
    @Retryable(value = RestClientException.class, maxAttempts = 3,
            backoff = @Backoff(delay = 5000L, multiplier = 1))
    public String pushToMBByUserInfo(String mbUrl, String jsonParam, String userName, String password) throws Exception {
        try {
            logger.info("MBUtils|发送消息到MB: {}", jsonParam);
            logger.info("MBUtils|MB address: {}", mbUrl);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            if (StringUtils.isNotEmpty(userName) && StringUtils.isNotEmpty(password)) {
                String byteArrayToBase64 = new String(Base64.encodeBase64((userName + ":" + password).getBytes()));
                headers.add("Authorization", "Basic " + byteArrayToBase64);
            }

            HttpEntity<String> request = new HttpEntity<>(jsonParam, headers);
            restTemplate.getMessageConverters().forEach(v -> {
                if (v instanceof StringHttpMessageConverter) {
                    ((StringHttpMessageConverter) v).setDefaultCharset(Charset.forName("UTF-8"));
                }
            });
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(mbUrl, request, String.class);
            logger.info("MBUtils|pushToMB|HttpStatus: {}", responseEntity.getStatusCode());
            // 判断请求是否发生异常
            if (!responseEntity.getStatusCode().is2xxSuccessful()) {
                logger.info("MBUtils|pushToMB|请求失败 -> {}", jsonParam);
                throw new RestClientException(responseEntity.getBody());
            }
            if (HttpStatus.OK.equals(responseEntity.getStatusCode())
                    || HttpStatus.ACCEPTED.equals(responseEntity.getStatusCode())) {
                logger.info("MBUtils|pushToMB成功 -> {}", responseEntity);
            }
            String body = responseEntity.getBody();
            logger.info("responseEntity.getBody() -> {}", responseEntity.getBody());
            if (StringUtils.isBlank(responseEntity.getBody()) || "\"\"".equals(responseEntity.getBody())) {
                logger.info("MBUtils|pushToMB成功 but response is empty, return");
                return "";
            }
            JSONObject resultJson = JSONObject.parseObject(responseEntity.getBody());
            if (!resultJson.containsKey("DATA")) {
                logger.info("MBUtils|pushToMB|请求失败,返回值没有DATA -> {}", resultJson);
                throw new RestClientException(responseEntity.getBody());
            }
            logger.info("MBUtils|pushToMB成功 resultJson -> {}", resultJson);
            return JSON.toJSONString(resultJson.get("DATA"));
        } catch (Exception e) {
            if (e instanceof RestClientException) {
                logger.error("MBUtils|pushToMB|请求异常,即将进行重试 -> {}", jsonParam, e);
                throw e;
            }
            logger.error("MBUtils|pushToMB|发生异常", e);
            throw e;
        }
    }

}

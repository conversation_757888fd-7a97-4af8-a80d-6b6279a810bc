package com.cowell.iscm.mapper.extend;

import com.cowell.iscm.service.dto.returnWarehouse.ReturnWarehouseExecuteParam;
import com.cowell.iscm.service.dto.returnWarehouse.WarehouseDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Set;

public interface IscmStoreReturnExecuteOrderExtendMapper {
    Set<WarehouseDTO> selectWarehouseInfos(@Param("month") Integer month);

    Set<Long> selectCheckedIds(ReturnWarehouseExecuteParam param);

    Set<String> selectCheckedOrderNos(ReturnWarehouseExecuteParam param);
}

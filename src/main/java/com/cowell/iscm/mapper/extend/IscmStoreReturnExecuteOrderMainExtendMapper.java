package com.cowell.iscm.mapper.extend;


import com.cowell.iscm.entity.IscmStoreReturnExecuteOrderMain;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface IscmStoreReturnExecuteOrderMainExtendMapper {

    int batchUpdate(@Param(value = "list") List<IscmStoreReturnExecuteOrderMain> mainList, @Param("createdMonth") Integer createdMonth);

    int batchInsert(List<IscmStoreReturnExecuteOrderMain> mainList);

    int updateProcessStatusAll(@Param(value = "ids") List<Long> ids, @Param("month") Integer month);
}

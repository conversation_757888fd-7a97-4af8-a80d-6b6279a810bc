package com.cowell.iscm.rest;

import com.cowell.iscm.entity.BdpAvgDailySales;
import com.cowell.iscm.entity.IscmRecalculationRecord;
import com.cowell.iscm.rest.errors.FailRequestAlertException;
import com.cowell.iscm.security.oauth2.TokenAuthenticationManager;
import com.cowell.iscm.service.AvgSalesRecalculationService;
import com.cowell.iscm.service.dto.AvgSalesQueryDTO;
import com.cowell.iscm.service.dto.BdpAvgDailySalesDTO;
import com.cowell.iscm.service.dto.ProcessResponse;
import com.cowell.iscm.service.dto.StoreApplyInfoDTO;
import com.cowell.iscm.service.dto.applyParam.CommonRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 第三方服务resource
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api")
@Api(value = "日均销复算接口", tags = "日均销复算接口")
public class AvgSalesRecalculationResource {
    private final Logger logger = LoggerFactory.getLogger(AvgSalesRecalculationResource.class);

    @Resource
    private TokenAuthenticationManager tokenAuthenticationManager;

    @Resource
    private AvgSalesRecalculationService avgSalesRecalculationService;

    @ApiOperation(value = "根据企业编码使用日均销数据进行复算", notes = "根据企业编码使用日均销数据进行复算")
    @GetMapping("/avg/sales/avgRecalculationByCompany")
    public ResponseEntity avgRecalculationByCompany(HttpServletRequest request, @RequestParam(value = "companyCode") String companyCode) {
        try {
            avgSalesRecalculationService.avgRecalculationByCompany(companyCode);
            return new ResponseEntity<>(HttpStatus.OK);
        } catch (Exception e) {
            throw new FailRequestAlertException("400", e.getMessage());
        }
    }

    @ApiOperation(value = "根据门店编码使用日均销数据进行复算", notes = "根据门店编码使用日均销数据进行复算")
    @GetMapping("/avg/sales/avgRecalculationByCompanyAndStores")
    public ResponseEntity avgRecalculationByCompanyCodeAndStoreCodes(HttpServletRequest request, @RequestParam(value = "companyCode") String companyCode, @RequestParam(value = "storeCodes", required = false) String storeCodes) {
        try {
            avgSalesRecalculationService.avgRecalculationByStores(companyCode, storeCodes);
            return new ResponseEntity<>(HttpStatus.OK);
        } catch (Exception e) {
            throw new FailRequestAlertException("400", e.getMessage());
        }
    }

    @ApiOperation(value = "bdp回调接口-消费bdp数据推送的增量日均销数据", notes = "bdp回调接口-消费bdp数据推送的增量日均销数据")
    @GetMapping(value = {"/avg/sales/bdpCallbackDealAvg", "/intranet/avg/sales/bdpCallbackDealAvg"})
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name="companyCode",value="企业编码"),
            @ApiImplicitParam(name="storeCodes",value="门店编码集合,多个用,分割")
    })
    public ResponseEntity bdpCallbackDealAvg(@RequestParam(value = "companyCode", required = false) String companyCode, @RequestParam(value = "storeCodes", required = false) String storeCodes) {
        try {
            avgSalesRecalculationService.bdpCallbackDealAvg(companyCode, storeCodes);
            return new ResponseEntity<>(HttpStatus.OK);
        } catch (Exception e) {
            throw new FailRequestAlertException("400", e.getMessage());
        }
    }

    @ApiOperation(value = "bdp回调接口-bdp回调请货门店接口", notes = "bdp回调接口-bdp回调请货门店接口")
    @GetMapping(value = {"/avg/sales/bdpCallbackStoreApplyDate", "/intranet/avg/sales/bdpCallbackStoreApplyDate"})
    public ResponseEntity bdpCallbackStoreApplyDate() {
        try {
            avgSalesRecalculationService.bdpCallbackStoreApplyDate();
            return new ResponseEntity<>(HttpStatus.OK);
        } catch (Exception e) {
            throw new FailRequestAlertException("400", e.getMessage());
        }
    }
    @ApiOperation(value = "根据门店与商品列表获取日销列表", notes = "根据门店与商品列表获取日销列表")
    @PostMapping(value = {"/avg/sales/getList", "/intranet/avg/sales/getList"})
    public ResponseEntity<List<BdpAvgDailySalesDTO>> getList(@RequestBody AvgSalesQueryDTO param) {
        try {
            return new ResponseEntity<>(avgSalesRecalculationService.getList(param), HttpStatus.OK);
        } catch (Exception e) {
            throw new FailRequestAlertException("400", e.getMessage());
        }
    }
    @ApiOperation(value = "手工请货", notes = "手工请货")
    @GetMapping(value = {"/avg/sales/manualStoreApply", "/intranet/avg/sales/manualStoreApply"})
    public CommonRes<String> manualStoreApply(@RequestParam("companyCode") String companyCode, @RequestParam("storeNo") String storeNo) {
        try {
            return avgSalesRecalculationService.manualStoreApply(companyCode, storeNo);
        } catch (Exception e) {
            throw new FailRequestAlertException("400", e.getMessage());
        }
    }
}

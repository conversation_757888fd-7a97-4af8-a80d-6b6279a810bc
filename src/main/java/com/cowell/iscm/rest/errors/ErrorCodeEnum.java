package com.cowell.iscm.rest.errors;

public enum ErrorCodeEnum {
    SUCCESS_AMIS("0", "成功"),
    SUCCESS("200", "成功"),
    PROPERTY_EXISTS("400","主键ID已存在"),
    INVALID_TOKEN("401", "invalid_token"),
    SYSTEM_ERROR("500", "系统错误"),
    PROPERTY_PARAMETER_NULL("401","参数为空"),
    PROPERTY_NAME_EXCEPTION("402", "该属性名已存在"),
    PROPERTY_FORMAT_EXCEPTION("403", "输入格式不规范"),
    PROPERTY_OPERATE_EXCEPTION("404", "操作异常"),
    PROPERTY_OPERATE_FAILURE("405", "操作失败"),
    PARAM_ERROR_EXCEPTION("10001", "入参未通过校验"),
    ORDER_NOT_EXIST("40001","订单信息不存在"),
    PARAM_ORDER_ID_NULL("40002","订单id不能为空"),
    ORDER_REFUND_NOT_EXIST("41001","订单退款信息不存在"),
    USER_CANCEL_REFUND_FAIL("41002","用户撤销退款申请失败"),
    MERC_AUDIT_REFUND_PASS_FAIL("41003","商家审核退款通过失败"),
    REFUND_FAIL("41004","发起退款失败"),
    MERC_AUDIT_REFUND_REJECT_FAIL("41005","商家审核退款不通过失败"),
    USER_CANCEL_FAIL("41006","用户取消失败"),
    USER_REFUND_FAIL("41007","用户发起退款申请失败"),
    WX_REFUND_FAIL("41008","调用微信退款失败"),
    USER_NOT_PAY_OVERTIME_FAIL("41009","订单状态修改为用户未支付超时取消失败"),
    MERC_NOT_RECEIPT_OVERTIME_FAIL("41010","订单状态修改为商家未接单超时取消失败"),
    COMPLETE_OVERTIME_FAIL("41011","订单状态修改为已完成失败"),
    THIRD_SERVER_STORE_FAIL("41012","查询门店服务异常"),
    THIRD_SERVER_UAA_FAIL("41013","查询用户信息失败"),
    PARAM_STORE_ID_NULL("41014","门店不能为空"),
    SELF_PICKUP_CODE_INCONFORMITY("41015","自提码不正确"),
    MERC_SELFPICKUP_VERIFICATION_FAIL("41016","商家自提核销失败"),
    MERC_SELFPICKUP_VERIFICATION_STATUS_FAIL("41017","订单状态修改为商家自提核销后状态失败"),
    THIRD_SERVER_ITEMCENTER_FAIL("41018","查询商品中心异常"),
    ITEM_DETAIL_IS_NULL("41019","商品详情为空"),
    THIRD_SERVER_ITEMCENTER_RETURN_ERROR("41020","查询不到商品信息"),
    THIRD_SERVER_ITEMCENTER_RETURN_NULL("41021","查询不到商品信息"),
    THIRD_SERVER_FUND_FAIL("41022","查询积分服务异常"),
    THIRD_SERVER_FUND_RETURN_ERROR("41023","查询积分服务异常"),
    THIRD_SERVER_FUND_RETURN_NULL("41024","查询不到积分规则"),
    SELF_CODE_EXPIRED("41025", "兑换码已过期"),
    SELF_CODE_CHANGE_COMPLETE("41041", "该订单已兑换"),
    SELF_CODE_CHANGE_CANCELED("41040", "该订单已取消"),
    SELF_CODE_NULL("41026", "请输入兑换码"),
    SELF_CODE_CHANGE_EXCEPTION("41038", "请输入正确的兑换码"),
    SELF_CODE_CHANGE_FAILED("41039", "验证或兑换失败"),
    THIRD_SERVER_SEARCHAPI_RETURN_NULL("41027", "订单搜索服务异常"),
    THIRD_SERVER_SEARCHAPI_FAILED("41028", "订单搜索服务异常"),
    NO_EFFECT_NUM("41029", "无效操作"),
    BUSINESS_CANCEL_FAIL("41030","商家取消失败"),
    BUSINESS_CANCEL_REASON_NULL("41031","商家取消原因为空"),
    CHANGE_TIMEOUT_CANCEL_FAIL("41032","兑换超时取消失败"),
    USER_CHANGED("41033","用户已兑换"),
    ORDER_CANCEL_FAIL("41034","订单取消失败"),
    BUSINESS_CANCELED("41035","商家已取消"),
    USER_CANCELED("41036","用户已取消"),
    CHANGE_TIMEOUT_CANCELED("41037","兑换码已失效"),
    ORDER_DETAIL_NOT_EXIST("41038","订单扩展信息不存在"),


    USER_POINTS_NOT_ENOUGH("41500","用户积分不足"),
    USER_CREATE_ORDER_POINTS_DISCREPANCY("41501","您的订单应付积分与实际总积分不符"),
    USER_CHANGE_TIME_NOT_ENOUGH("41502","用户兑换次数不足"),
    WX_PAY_POINTS_FAIL("41503","调用积分支付失败"),
    WX_REFUND_POINTS_FAIL("41503","调用积分退款失败"),
    PARAM_SKU_ID_NULL("41504","商品不能为空"),
    PARAM_POINTS_MORE_THAN_ZERO("41505","支付积分额必须大于0"),
    ERP_STOCK_NOT_ENOUGH("41506","ERP库存不足"),
    PLEASE_SELECT_STORE_ID("41507","请选择自提门店"),
    DEDUCT_SKU_STOCK_FAIL("41508","商品库存不足"),
    CONFIRM_SKU_STOCK_FAIL("41509","核销库存失败"),
    ORDER_POINTS_NOT_EXIST("41510","订单积分扩展信息不存在"),
    ORDER_O2O_NOT_EXIST("41511","订单扩展信息不存在"),
    PARAM_MORE_THAN_ZERO("41512","支付金额必须大于0"),

    BUSINESS_ID_NULL("41301","连锁id为空"),
    USER_ID_NULL("41302","用户id为空"),
    ORDER_AMOUNT_NULL("41303","订单金额为空"),
    COUPON_ID_NULL("41304","优惠劵id为空"),
    COUPON_STATUS_EXCEPTION("41305","优惠劵状态异常"),
    CURRENT_COUPON_AMOUNT_NULL("41306","当前优惠劵金额为空"),
    ITEMS_EMPTY("41307","商品列表为空"),
    GET_COUPON_DISABLE_TIME_EXCEPTION("41308","获取优惠劵失效时间失败"),
    COUPON_DISABLE_TIME_NULL("41309","优惠劵失效时间为空"),
    COUPON_CODE_NULL("41310","优惠劵码为空"),
    GET_RECEIVE_COUPONS_EXCEPTION("41311","获取可领取优惠劵失败"),
    GET_USABLE_COUPONS_EXCEPTION("41312","获取可用优惠劵列表失败"),
    RECEIVE_COUPON_EXCEPTION("41313","领取优惠劵失败"),
    COUPON_CANCLE_AFTER_VERIFICATION_EXCEPTION("41314","优惠劵核销失败"),
    GET_COUPON_STATUS_EXCEPTION("41315","获取优惠劵状态失败"),
    UPDATE_COUPON_STATUS_EXCEPTION("41316","更新优惠劵状态失败"),
    GET_COUPON_DETAIL_EXCEPTION("41317","获取优惠劵详情失败"),
    GET_COUPON_CONFIG_EXCEPTION("41318","获取优惠劵配置信息失败"),
    GET_COUPON_TYPE_EXCEPTION("41319","获取优惠劵类型失败"),
    COUPON_AMOUNT_CHECK_EXCEPTION("41320","优惠金额校验失败"),
    UNKNOWN_COUPON_TYPE_EXCEPTION("41321","未知的优惠劵类型"),
    COUPON_AMOUNT_DEVIDE_EXCEPTION("41322","优惠劵金额分摊失败"),
    COUPON_ALREADY_DISABLE("41323","优惠劵已经失效"),
    GET_COUPON_CONDITION_EXCEPTION("41324","获取优惠劵条件失败"),
    DISSATISFIED_COUPON("41325","商品降价啦，当前使用的优惠券不满足满减条件，请重新选择"),
    COUPON_TYPE_NULL("41326","优惠劵类型不能为空"),
    GET_COUPON_PAR_EXCEPTION("41327","满减金额获取失败"),
    GET_COUPON_DISCOUNT_EXCEPTION("41328","折扣获取失败"),
    CALCULATE_ORDER_COUPON_AMOUNT_EXCEPTION("41329","计算订单优惠金额失败"),
    COUPON_DISABLE_TIME_CHECK_EXCEPTION("41330","优惠劵失效时间校验失败"),
    GET_COUPON_HIGHEST_EXCEPTION("41331","折扣获取失败"),
    STORE_NAME_NULL("41332","商店名称获取失败"),
    ITEM_LIST_NULL("41333","商品列表获取失败"),
    GET_REAL_COUPON_AMOUNT_PARAMS_EXCEPTION("41334","获取实际优惠金额参数错误"),
    GET_COUPON_SPECIES_NULL("41335","优惠劵商品类型获取失败"),
    GET_COUPON_GOODS_SPUID_NULL("41336","优惠劵商品SPUID获取失败"),
    PARAM_IS_NULL("41337","参数不能为空"),
    SINGLE_PRODUCT_COUPON_SHARE_ITEM_IS_NULL("41338","单品优惠券没有找到要分摊的商品"),
    COUPON_IS_NOT_USED("41339","所选优惠券暂不能使用，请更换其他优惠券"),

    POS_RECORD_NOT_EXIST("41101", "特管药品登记信息获取失败"),

    UPLOAD_FILE_FAILED("10015", "文件上传失败"),
    DELETE_FILE_FAILED("10016", "文件删除失败"),
    UPLOAD_FILE_NOT_EXISTS("10017", "上传文件不存在"),

    COULD_NOT_QUOTE("10018", "该单据已报价或者报价已结束"),
    ENQUIRY_DATA_NULL("10019", "询价数据为空，无法维护拓展数据"),
    ENQUIRY_GROUP_FAIL("10020", "该询价单对应的询价组为空"),
    ENQUIRY_SUPPLIER_FAIL("10021", "维护供应商和询价单关系失败"),
    ENQUIRY_RESULT_FAIL("10022", "维护询价结果失败"),
    FAIL_FINAL("10023", "维护失败"),
    ENQUIRY_RESULT_UPDATED("10024", "询价结果已更新，请刷新页面"),
    ENQUIRY_ORDER_DETAIL_UPDATED("10025", "商品明细已更新，请刷新页面"),
    ENQUIRY_ORDER_AND_DETAIL_DIFFER("10026", "询价单与明细状态不一致，请检查"),
    USER_SUPPLIER_NULL("10027", "该用户没有供应商，请维护"),
    ENQUIRY_RESULT_NULL("10028", "询价结果为空"),
    ENQUIRY_DETAIL_NULL("10029", "询价商品明细为空"),
    ENQUIRY_SUPPLIER_NULL("10030", "询价单供应商为空"),
    ENQUIRY_ATLEAST_ONE_QUOTE("10031", "请您至少报价一次"),
    DOWN_ENQUIRY_ORDER_UNCOMPLETE("10032", "该询价单未完成，不能进行导出操作"),
    UN_CONFIRM_SELECT("10033", "非确认中，不能做此操作"),
    PLEASE_SELECT_SUPPLIER("10034", "请选择供应商"),
    ENQUIRY_GROUP_NAME_REPEAT("10035", "询价组名称重复"),
    ENQUIRY_GROUP_NAME_NULL("10036", "询价组名称为空"),
    ENQUIRY_GROUP_NAME_LENGTH("10037", "询价组名称长度超长"),
    ENQUIRY_ORDER_UPDATE_SUCCESS("10038", "更新成功"),
    ENQUIRY_ORDER_UPDATE_FAIL("10039", "更新失败，查询单据为空"),
    SECURE_ENQUIRY_ORDER_NOT("10040", "您无权操作此业务"),
    CHECK_ACCOUNT_NULL("10041", "对账信息为空"),
    CHECK_ACCOUNT_CANCEL_FAIL("10042", "这些商品中有已经生成结算单的商品，无法取消对账，请检查。"),
    CREATE_iscm_ORDER_SUCCESS("10043", "生成采购订单成功"),
    CREATE_iscm_ORDER_PERM("10044", "生成采购订单成功，但perm调用有误"),
    SETTLE_ORDER_SUBMIT_TIPS("10045", "结算单中必须要包含进货或退货单并且返利单只能与进退货单开相同类型的发票。"),

    RESPONSE_TIME_OUT("10046", "响应超时"),
    DATA_NULL("10047", "数据为空"),

    PLAN_DATA_NULL("10048", "必须项有空值"),
    PLAN_DATE_FORMAT("10049", "日期格式有误，正确格式为YYYY-MM-DD"),
    PLAN_COUNT_THREE("10050", "确认数量不可为负数/确认数量只支持小数后3位"),
    PLAN_PRICE_FIVE("10051", "确认含税单价不可为负数/确认含税单价只支持小数后5位"),
    PLAN_TAXRATE_ERROR("10052", "税率错误"),
    PLAN_GOODS_DIFFER("10053", "采购计划单号和商品不符"),
    PLAN_PRICE_GREATER("10054", "确认含税单价超过含税单价"),
    PLAN_COUNT_GREATER("10055", "确认数量超过订购数量"),
    PLAN_PRICE_NUMBER("10056", "确认含税单价必须为数字"),
    PLAN_COUNT_NUMBER("10057", "确认数量必须为数字"),
    PLAN_RATE_NUMBER("10058", "确认税率必须为百分比"),

    GET_PERM_ERROR("10059", "获取权限异常"),
    CALL_TOC_ERROR("10060", "调用分发器异常"),

    AUDIT_PLAT_NULL("10061", "用户无平台权限"),
    AUDIT_COMPANY_NULL("10062", "用户【%s】无平台【%s】下的连锁权限"),
    AUDIT_STORE_NULL("10063", "用户【%s】无公司【%s】下的门店权限"),
    AUDIT_STORE_EXIT("10064", "【%s】该门店已存在，不能重复录入"),
    STORE_NOT_MANUAL("10065", "门店未开启手工请货"),
    STORE_APPLY_CALCULATING("10066", "门店的补货建议正在运算中，请稍后…"),
    ;


    ;

    private String code;
    private String msg;

    private ErrorCodeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}

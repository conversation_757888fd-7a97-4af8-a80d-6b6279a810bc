package com.cowell.iscm.rest;

import com.alibaba.fastjson.JSON;
import com.codahale.metrics.annotation.Timed;
import com.cowell.iscm.rest.errors.FailRequestAlertException;
import com.cowell.iscm.security.oauth2.TokenAuthenticationManager;
import com.cowell.iscm.service.ReturnWarehouseExecuteGenService;
import com.cowell.iscm.service.ReturnWarehouseExecuteService;
import com.cowell.iscm.service.dto.CommonProcessDTO;
import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.returnWarehouse.*;
import com.cowell.iscm.service.feign.response.PageResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Set;

/**
 * 退仓执行
 *
 * <AUTHOR>
 */
@Api(value = "退仓执行接口api", tags = "退仓执行接口api")
@RestController
@RequestMapping("/api/returnWarehouse/execute")
public class ReturnWarehouseExecuteResource {
    private final Logger logger = LoggerFactory.getLogger(ReturnWarehouseExecuteResource.class);

    @Autowired
    private TokenAuthenticationManager tokenAuthenticationManager;

    @Autowired
    private ReturnWarehouseExecuteService returnWarehouseExecuteService;
    @Autowired
    private ReturnWarehouseExecuteGenService returnWarehouseExecuteGenService;

    @ApiOperation(value = "获取退仓接收仓库", notes = "获取退仓接收仓库")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "month", value = "月份[yyyyMM]", required = true)
    })
    @GetMapping("/getWarehouseInfos")
    public ResponseEntity<Set<WarehouseDTO>> getWarehouseInfos(HttpServletRequest request, @RequestParam("month") Integer month) {
        try {
            TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
            logger.info("用户={},操作={},参数={}", userDTO, "ReturnWarehouseExecuteResource.getWarehouseInfos", JSON.toJSONString(month));
            return new ResponseEntity<>(returnWarehouseExecuteService.getWarehouseInfos(month), HttpStatus.OK);
        } catch (Exception e) {
            throw new FailRequestAlertException("400", e.getMessage());
        }
    }

    @ApiOperation(value = "获取全选Id集合", notes = "获取全选Id集合")
    @PostMapping("/getCheckedIds")
    public ResponseEntity<Set<Long>> getCheckedIds(HttpServletRequest request, @RequestBody ReturnWarehouseExecuteParam param) {
        try {
            TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
            logger.info("用户={},操作={},参数={}", userDTO, "ReturnWarehouseExecuteResource.getCheckedIds", JSON.toJSONString(param));
            return new ResponseEntity<>(returnWarehouseExecuteService.getCheckedIds(userDTO, param), HttpStatus.OK);
        } catch (Exception e) {
            throw new FailRequestAlertException("400", e.getMessage());
        }
    }

    @ApiOperation(value = "分页获取退仓执行列表", notes = "分页获取退仓执行列表")
    @PostMapping("/pageExecuteList")
    public PageResponse<List<StoreReturnExecuteOrderDTO>> pageExecuteList(HttpServletRequest request, @RequestBody ReturnWarehouseExecuteParam param) {
        try {
            TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
            logger.info("用户={},操作={},参数={}", userDTO, "ReturnWarehouseExecuteResource.pageExecuteList", JSON.toJSONString(param));
            return returnWarehouseExecuteService.pageExecuteList(userDTO, param);
        } catch (Exception e) {
            throw new FailRequestAlertException("400", e.getMessage());
        }
    }

    @ApiOperation(value = "获取退仓执行明细", notes = "获取退仓执行明细")
    @PostMapping("/getExecuteDetail")
    public ResponseEntity<StoreReturnExecuteOrderDetailDTO> getExecuteDetail(HttpServletRequest request, @RequestBody ReturnWarehouseExecuteIdParam param) {
        try {
            TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
            logger.info("用户={},操作={},参数={}", userDTO, "ReturnWarehouseExecuteResource.getExecuteDetail", param);
            return new ResponseEntity<>(returnWarehouseExecuteService.getExecuteDetail(userDTO, param), HttpStatus.OK);
        } catch (Exception e) {
            throw new FailRequestAlertException("400", e.getMessage());
        }
    }

    @ApiOperation(value = "分页获取退仓执行明细列表", notes = "分页获取退仓执行明细列表")
    @PostMapping("/pageExecuteDetailList")
    public PageResponse<List<StoreReturnExecuteOrderDetailListDTO>> pageExecuteDetailList(HttpServletRequest request, @RequestBody ReturnWarehouseExecuteDetailParam param) {
        try {
            TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
            logger.info("用户={},操作={},参数={}", userDTO, "ReturnWarehouseExecuteResource.pageExecuteDetailList", JSON.toJSONString(param));
            return returnWarehouseExecuteService.pageExecuteDetailList(userDTO, param);
        } catch (Exception e) {
            throw new FailRequestAlertException("400", e.getMessage());
        }
    }

    @ApiOperation(value = "删除退仓执行", notes = "删除退仓执行")
    @PostMapping("/deleteExecute")
    public ResponseEntity<String> deleteExecute(HttpServletRequest request, @RequestBody ReturnWarehouseExecuteIdParam param) {
        try {
            TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
            logger.info("用户={},操作={},参数={}", userDTO, "ReturnWarehouseExecuteResource.deleteExecute", JSON.toJSONString(param));
            returnWarehouseExecuteService.deleteExecute(userDTO, param);
            return new ResponseEntity<>("删除成功", HttpStatus.OK);
        } catch (Exception e) {
            throw new FailRequestAlertException("400", e.getMessage());
        }
    }

    @ApiOperation(value = "删除退仓执行(批量删除)", notes = "删除退仓执行(批量删除)")
    @PostMapping("/deleteExecuteBatch")
    public ResponseEntity<String> deleteExecuteBatch(HttpServletRequest request, @RequestBody ReturnWarehouseExecuteIdBatchParam param) {
        try {
            TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
            logger.info("用户={},操作={},参数={}", userDTO, "ReturnWarehouseExecuteResource.deleteExecuteBatch", JSON.toJSONString(param));
            returnWarehouseExecuteService.deleteExecute(userDTO, param);
            return new ResponseEntity<>("删除成功", HttpStatus.OK);
        } catch (Exception e) {
            throw new FailRequestAlertException("400", e.getMessage());
        }
    }

    @ApiOperation(value = "删除退仓执行明细", notes = "删除退仓执行明细")
    @PostMapping("/deleteExecuteDetail")
    public ResponseEntity<String> deleteExecuteDetail(HttpServletRequest request, @RequestBody ReturnWarehouseExecuteIdParam param) {
        try {
            TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
            logger.info("用户={},操作={},参数={}", userDTO, "ReturnWarehouseExecuteResource.deleteExecuteDetail", JSON.toJSONString(param));
            returnWarehouseExecuteService.deleteExecuteDetail(userDTO, param);
            return new ResponseEntity<>("删除成功", HttpStatus.OK);
        } catch (Exception e) {
            throw new FailRequestAlertException("400", e.getMessage());
        }
    }

    @ApiOperation(value = "删除退仓执行明细(批量删除)", notes = "删除退仓执行明细(批量删除)")
    @PostMapping("/deleteExecuteDetailBatch")
    public ResponseEntity<String> deleteExecuteDetailBatch(HttpServletRequest request, @RequestBody ReturnWarehouseExecuteIdBatchParam param) {
        try {
            TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
            logger.info("用户={},操作={},参数={}", userDTO, "ReturnWarehouseExecuteResource.deleteExecuteDetailBatch", JSON.toJSONString(param));
            returnWarehouseExecuteService.deleteExecuteDetail(userDTO, param);
            return new ResponseEntity<>("删除成功", HttpStatus.OK);
        } catch (Exception e) {
            throw new FailRequestAlertException("400", e.getMessage());
        }
    }

    @ApiOperation(value = "导出退仓执行列表", notes = "导出退仓执行列表")
    @PostMapping("/exportExecuteList")
    public ResponseEntity<String> exportExecuteList(HttpServletRequest request, @RequestBody ReturnWarehouseExecuteParam param) {
        try {
            TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
            logger.info("用户={},操作={},参数={}", userDTO, "ReturnWarehouseExecuteResource.exportExecuteList", JSON.toJSONString(param));
            returnWarehouseExecuteService.exportExecuteList(userDTO, param);
            return new ResponseEntity<>("正在导出,请稍后去下载中心查看", HttpStatus.OK);
        } catch (Exception e) {
            throw new FailRequestAlertException("400", e.getMessage());
        }
    }

    @ApiOperation(value = "提审并下发门店", notes = "提审并下发门店")
    @PostMapping("/issue")
    public ResponseEntity<String> issue(HttpServletRequest request, @RequestBody ReturnWarehouseExecuteIssueParam param) {
        try {
            TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
            logger.info("用户={},操作={},参数={}", userDTO, "ReturnWarehouseExecuteResource.issue", JSON.toJSONString(param));
            returnWarehouseExecuteService.beforeIssue(userDTO, param, false);
            returnWarehouseExecuteService.issue(userDTO, param);
            return new ResponseEntity<>("下发中", HttpStatus.OK);
        } catch (Exception e) {
            throw new FailRequestAlertException("400", e.getMessage());
        }
    }

    @ApiOperation(value = "重新下发", notes = "重新下发")
    @PostMapping("/reIssue")
    public ResponseEntity<String> reIssue(HttpServletRequest request, @RequestBody ReturnWarehouseExecuteIssueParam param) {
        try {
            TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
            logger.info("用户={},操作={},参数={}", userDTO, "ReturnWarehouseExecuteResource.reIssue", JSON.toJSONString(param));
            returnWarehouseExecuteService.beforeIssue(userDTO, param, true);
            returnWarehouseExecuteService.reIssue(userDTO, param);
            return new ResponseEntity<>("下发中", HttpStatus.OK);
        } catch (Exception e) {
            throw new FailRequestAlertException("400", e.getMessage());
        }
    }

    @ApiOperation(value = "确认加入退仓执行清单", notes = "确认加入退仓执行清单")
    @PostMapping("/genReturnWarehouseExec")
    public ResponseEntity<String> genReturnWarehouseExec(HttpServletRequest request, @RequestBody GenExecParam param) {
        try {
            TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
            logger.info("用户={},操作={},参数={}", userDTO, "ReturnWarehouseResource.genReturnWarehouseExec",JSON.toJSONString(param));
            return new ResponseEntity<>(returnWarehouseExecuteGenService.genReturnWarehouseExec(userDTO, param), HttpStatus.OK);
        } catch (Exception e) {
            throw new FailRequestAlertException("400", e.getMessage());
        }
    }
    @ApiOperation(value = "导入退仓执行清单", notes = "导入退仓执行清单")
    @PostMapping("/importReturnWarehouseExec")
    public ResponseEntity importReturnWarehouseExec(HttpServletRequest request, @RequestParam("file") MultipartFile file) throws Exception{
        try {
            TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
            logger.info("用户={},操作={},参数={}", userDTO, "ReturnWarehouseResource.importReturnWarehouseExec");
            return new ResponseEntity<>(returnWarehouseExecuteGenService.importReturnWarehouseExec(userDTO, file), HttpStatus.OK);
        } catch (Exception e) {
            throw new FailRequestAlertException("400", e.getMessage());
        }
    }

    @ApiOperation(value = "获取导入退仓执行清单", notes = "获取导入退仓执行清单")
    @Timed
    @GetMapping("/getImportExecProcess")
    public ResponseEntity<CommonProcessDTO> getImportExecProcess(HttpServletRequest request, HttpServletResponse response, @RequestParam("bizType") Integer bizType){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={}", userDTO, "ReturnWarehouseResource.getImportExecProcess");
        try {
            return new ResponseEntity<>(returnWarehouseExecuteGenService.getImportExecProcess(userDTO, bizType, response), HttpStatus.OK);
        } catch (Exception e) {
            throw new FailRequestAlertException("400", e.getMessage());
        }
    }
    @ApiOperation(value = "删除", notes = "获取导入退仓执行清单")
    @Timed
    @PostMapping("/delete")
    public ResponseEntity delete(HttpServletRequest request, @RequestBody DelExecParam param){
        TokenUserDTO userDTO = tokenAuthenticationManager.getUserInfobyToken(request);
        logger.info("用户={},操作={},param={}", userDTO, "ReturnWarehouseResource.delete", JSON.toJSONString(param));
        try {
            returnWarehouseExecuteGenService.delete(userDTO, param);
            return new ResponseEntity<>(HttpStatus.OK);
        } catch (Exception e) {
            throw new FailRequestAlertException("400", e.getMessage());
        }
    }

}

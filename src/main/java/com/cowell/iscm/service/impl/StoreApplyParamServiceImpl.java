package com.cowell.iscm.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.iscm.config.Constants;
import com.cowell.iscm.entity.*;
import com.cowell.iscm.enums.*;
import com.cowell.iscm.mapper.*;
import com.cowell.iscm.mapper.extend.*;
import com.cowell.iscm.rest.errors.BusinessErrorException;
import com.cowell.iscm.service.*;
import com.cowell.iscm.service.dto.TagDto;
import com.cowell.iscm.service.dto.TagPageParam;
import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.applyParam.*;
import com.cowell.iscm.service.dto.configcenter.ExcelDomain;
import com.cowell.iscm.service.feign.PermissionService;
import com.cowell.iscm.service.feign.TagGoodsService;
import com.cowell.iscm.service.feign.TagService;
import com.cowell.iscm.service.feign.dto.OrgInfoBaseCache;
import com.cowell.iscm.service.feign.dto.OrgSimpleDTO;
import com.cowell.iscm.service.feign.dto.StoreComponentQueryParam;
import com.cowell.iscm.service.thread.HandlerDataExportService;
import com.cowell.iscm.utils.DateUtils;
import com.cowell.iscm.utils.HutoolUtil;
import com.cowell.permission.dto.OrgDTO;
import com.cowell.iscm.service.feign.dto.EmployeeInfoVO;
import com.cowell.permission.vo.OrgTreeVO;
import com.cowell.permission.vo.OrgVO;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class StoreApplyParamServiceImpl implements StoreApplyParamService {

    private final Logger logger = LoggerFactory.getLogger(StoreApplyParamServiceImpl.class);

    @Autowired
    private IscmStoreApplyParamOrgMapper iscmStoreApplyParamOrgMapper;

    @Autowired
    private IscmStoreApplyParamOrgExtendMapper iscmStoreApplyParamOrgExtendMapper;

    @Autowired
    private IscmStoreApplyParamGoodsLimitOrgMapper iscmStoreApplyParamGoodsLimitOrgMapper;

    @Autowired
    private IscmStoreApplyParamGoodsLimitOrgExtendMapper iscmStoreApplyParamGoodsLimitOrgExtendMapper;

    @Autowired
    private IscmStoreApplyParamGoodsLevelOrgMapper iscmStoreApplyParamGoodsLevelOrgMapper;

    @Autowired
    private IscmStoreApplyParamGoodsLevelOrgExtendMapper iscmStoreApplyParamGoodsLevelOrgExtendMapper;

    @Autowired
    private IscmStoreApplyParamGoodsUpperLimitOrgMapper iscmStoreApplyParamGoodsUpperLimitOrgMapper;
    @Autowired
    private IscmStoreApplyParamGoodsUpperLimitItemOrgMapper iscmStoreApplyParamGoodsUpperLimitItemOrgMapper;

    @Autowired
    private IscmStoreApplyParamGoodsCatalogLimitOrgMapper iscmStoreApplyParamGoodsCatalogLimitOrgMapper;

    @Autowired
    private IscmStoreApplyParamGoodsCatalogLimitOrgExtendMapper iscmStoreApplyParamGoodsCatalogLimitOrgExtendMapper;

    @Autowired
    private StoreApplyParamNonAutomaticService storeApplyParamNonAutomaticService;
    @Autowired
    private StoreApplyParamGoodsUpperLimitService storeApplyParamGoodsUpperLimitService;

    @Autowired
    private StoreApplyParamPushService storeApplyParamPushService;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private IscmStoreApplyParamNoAutoApplyStoreMapper iscmStoreApplyParamNoAutoApplyStoreMapper;

    @Autowired
    private IscmStoreApplyChooseStoreMapper iscmStoreApplyChooseStoreMapper;

    @Autowired
    private IscmStoreApplyAutoFloatMapper iscmStoreApplyAutoFloatMapper;

    @Autowired
    private IscmStoreApplyAutoFloatExtendMapper iscmStoreApplyAutoFloatExtendMapper;

    @Autowired
    private StoreApplyParamOrgService storeApplyParamOrgService;

    @Autowired
    private StoreApplyParamGoodsCatalogLimitServiceImpl storeApplyParamGoodsCatalogLimitService;

    @Autowired
    private TagGoodsService tagGoodsService;

    @Autowired
    private StoreApplyParamPushHDService storeApplyParamPushHDService;

    @Autowired
    private TagService tagService;

    @Autowired
    private AsyncExportFileService asyncExportFileService;

    @Autowired
    @Qualifier("taskExecutor")
    private AsyncTaskExecutor executor;
    @Value("${iscm.storeGroup.tagBizType:17}")
    private Integer tagBizType;

    @Override
    public StoreApplyParamDTO getAutoApplyList(Long orgId, Integer orgType, Integer paramType, Integer paramScope, TokenUserDTO userDTO) throws Exception {
        try {
            Optional.ofNullable(orgId).orElseThrow(() -> new BusinessErrorException("请选择组织机构"));
            ApplyParamTypeEnum paramTypeEnum = Optional.ofNullable(ApplyParamTypeEnum.getEnumByCode(paramType)).orElseThrow(() -> new BusinessErrorException("非法的参数类型"));
            StoreApplyParamDTO storeApplyParamDTO = new StoreApplyParamDTO();
            if (orgType != null && orgType.equals(OrgTypeEnum.ORG_TYPE_STORE_GROUP.getCode())) {
                List<String> storeOrgIdList = tagEntityCodeListQuery(orgId);
                if (CollectionUtils.isEmpty(storeOrgIdList)) {
                    throw new BusinessErrorException("门店信息为空");
                }
                switch (paramTypeEnum) {
                    case AUTO_APPLY:
                        storeApplyParamDTO = getStoreApplyParamDTO(orgId);
                        return storeApplyParamDTO;
                    case AUTO_APPLY_TIME:
                        List<IscmStoreApplyParamOrg> paramOrgs = getIscmStoreApplyParamOrgs(orgId);
                        if (CollectionUtils.isNotEmpty(paramOrgs)) {
                            AutoApplyParamDTO autoApplyTime = new AutoApplyParamDTO();
                            BeanUtils.copyProperties(paramOrgs.get(0), autoApplyTime);
                            autoApplyTime.setGmtCreate(DateUtils.conventDateStrByDate(paramOrgs.get(0).getGmtCreate(), DateUtils.DATETIME_PATTERN));
                            autoApplyTime.setGmtUpdate(DateUtils.conventDateStrByDate(paramOrgs.get(0).getGmtUpdate(), DateUtils.DATETIME_PATTERN));
                            storeApplyParamDTO.setAutoApplyTimeDTO(autoApplyTime);
                            return storeApplyParamDTO;
                        }
                        return storeApplyParamDTO;
                    case MIDDLE_PACKAGE_GOODS_APPLY:
                        StoreApplyParamDTO storeApplyParamDTO1 = getStoreApplyParamDTO(orgId, storeApplyParamDTO, orgId);
                        return storeApplyParamDTO1;
                    case NON_AUTOMATIC_APPLY:
                        StoreApplyParamNonAutomaticDTO storeApplyParamNonAutomaticDTO = storeApplyParamNonAutomaticService.getApplyList(userDTO, orgId);
                        BeanUtils.copyProperties(storeApplyParamNonAutomaticDTO, storeApplyParamDTO);
                        return storeApplyParamDTO;
                    case APPLY_GOODS_UPPER_LIMIT:
                        StoreApplyParamGoodsUpperLimitDTO storeApplyParamGoodsUpperLimitDTO = storeApplyParamGoodsUpperLimitService.getApplyList(userDTO, orgId, paramScope);
                        BeanUtils.copyProperties(storeApplyParamGoodsUpperLimitDTO, storeApplyParamDTO);
                        return storeApplyParamDTO;
                }
            }
            OrgVO orgVO = permissionService.getOrgInfoById(orgId);
            List<OrgDTO> orgDTOS = permissionService.listDirectParentOrgByOrgId(orgId).stream()
                    .filter(v -> v.getType().equals(OrgTypeEnum.ORG_TYPE_COMPANY.getCode())
                            || v.getType().equals(OrgTypeEnum.ORG_TYPE_REGIONAL_PLATFORM.getCode())
                            || v.getType().equals(OrgTypeEnum.ORG_TYPE_BUSINESS.getCode())).filter(v -> v.getType() <= orgVO.getType()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(orgDTOS)) {
                throw new BusinessErrorException("未知的机构");
            }
            TreeMap<Integer, OrgDTO> orgDTOMap = new TreeMap<>(orgDTOS.stream().collect(Collectors.toMap(OrgDTO::getType, Function.identity(), (k1, k2) -> k1)));
            NavigableMap<Integer, OrgDTO> descMap = orgDTOMap.descendingMap();

            for (Map.Entry<Integer, OrgDTO> entry : descMap.entrySet()) {
                switch (paramTypeEnum) {
                    case AUTO_APPLY:
                        storeApplyParamDTO = getStoreApplyParamDTO(entry.getValue().getId());
                        if (StringUtils.isNotBlank(storeApplyParamDTO.getCombinedCodeRange().getParamValue())
                                || StringUtils.isNotBlank(storeApplyParamDTO.getOnwayStockDTO().getParamValue())
                                || StringUtils.isNotBlank(storeApplyParamDTO.getUnAbleStockDTO().getParamValue())
                                || Objects.nonNull(storeApplyParamDTO.getSpecialGoodsCtrlDTOS().stream().filter(p -> StringUtils.isNotBlank(p.getParamValue())).findAny().orElse(null))
                                || Objects.nonNull(storeApplyParamDTO.getGoodsLevelParamDTOS().stream().filter(p -> p.getStartValue() != null && p.getEndValue() != null).findAny().orElse(null))
                                || CollectionUtils.isNotEmpty(storeApplyParamDTO.getGoodsLimitParamDTOS())) {
                            if (!entry.getValue().getId().equals(orgId)) {
                                storeApplyParamDTO.setSuperInfo("当前参数继承“" + entry.getValue().getShortName() + "组织”数据");
                            }
                            return storeApplyParamDTO;
                        }
                        break;
                    case AUTO_APPLY_TIME: {
                        List<IscmStoreApplyParamOrg> paramOrgs = getIscmStoreApplyParamOrgs(entry.getValue().getId());
                        if (CollectionUtils.isNotEmpty(paramOrgs)) {
                            AutoApplyParamDTO autoApplyTime = new AutoApplyParamDTO();
                            BeanUtils.copyProperties(paramOrgs.get(0), autoApplyTime);
                            autoApplyTime.setGmtCreate(DateUtils.conventDateStrByDate(paramOrgs.get(0).getGmtCreate(), DateUtils.DATETIME_PATTERN));
                            autoApplyTime.setGmtUpdate(DateUtils.conventDateStrByDate(paramOrgs.get(0).getGmtUpdate(), DateUtils.DATETIME_PATTERN));
                            storeApplyParamDTO.setAutoApplyTimeDTO(autoApplyTime);
                            if (!entry.getValue().getId().equals(orgId)) {
                                storeApplyParamDTO.setSuperInfo("当前参数继承“" + entry.getValue().getShortName() + "组织”数据");
                            }
                            return storeApplyParamDTO;
                        }
                        break;
                    }
                    case MIDDLE_PACKAGE_GOODS_APPLY: {
                        StoreApplyParamDTO storeApplyParamDTO1 = getStoreApplyParamDTO(orgId, storeApplyParamDTO, entry.getValue().getId());
                        if (!entry.getValue().getId().equals(orgId)) {
                            storeApplyParamDTO.setSuperInfo("当前参数继承“" + entry.getValue().getShortName() + "组织”数据");
                        }
                        if (storeApplyParamDTO1 != null) return storeApplyParamDTO1;
                        break;
                    }
                    case NON_AUTOMATIC_APPLY: {
                        StoreApplyParamNonAutomaticDTO storeApplyParamNonAutomaticDTO = storeApplyParamNonAutomaticService.getApplyList(userDTO, entry.getValue().getId());
                        BeanUtils.copyProperties(storeApplyParamNonAutomaticDTO, storeApplyParamDTO);
                        logger.info("storeApplyParamNonAutomaticDTO:{}", JSON.toJSONString(storeApplyParamNonAutomaticDTO));
                        if (storeApplyParamNonAutomaticDTO.nonNull()) {
                            if (!entry.getValue().getId().equals(orgId)) {
                                storeApplyParamDTO.setSuperInfo("当前参数继承“" + entry.getValue().getShortName() + "组织”数据");
                            }
                            return storeApplyParamDTO;
                        }
                        break;
                    }
                    case APPLY_GOODS_UPPER_LIMIT: {
                        StoreApplyParamGoodsUpperLimitDTO storeApplyParamGoodsUpperLimitDTO = storeApplyParamGoodsUpperLimitService.getApplyList(userDTO, entry.getValue().getId(), paramScope);
                        BeanUtils.copyProperties(storeApplyParamGoodsUpperLimitDTO, storeApplyParamDTO);
                        if (storeApplyParamGoodsUpperLimitDTO.nonNull()) {
                            if (!entry.getValue().getId().equals(orgId)) {
                                storeApplyParamDTO.setSuperInfo("当前参数继承“" + entry.getValue().getShortName() + "组织”数据");
                            }
                            return storeApplyParamDTO;
                        }
                        break;
                    }
                    case APPLY_MIDDLE_CATEGORY: {
                        Map<String, IscmStoreApplyParamOrg> map = getIscmStoreApplyParamMiddleCategory(entry.getValue().getId());
                        if (MapUtils.isNotEmpty(map)) {
                            IscmStoreApplyParamOrg zy = map.get(ApplyParamCodeEnum.ZY_MIDDLE_CATEGORY.getCode());
                            if (zy != null && StringUtils.isNotBlank(zy.getParamValue())) {
                                storeApplyParamDTO.setZyStoreMiddleCategorys(Arrays.stream(StringUtils.split(zy.getParamValue(), ",")).map(Long::parseLong).collect(Collectors.toList()));
                            }
                            IscmStoreApplyParamOrg jm = map.get(ApplyParamCodeEnum.JM_MIDDLE_CATEGORY.getCode());
                            if (jm != null && StringUtils.isNotBlank(jm.getParamValue())) {
                                storeApplyParamDTO.setJmStoreMiddleCategorys(Arrays.stream(StringUtils.split(jm.getParamValue(), ",")).map(Long::parseLong).collect(Collectors.toList()));
                            }
                            if (!entry.getValue().getId().equals(orgId)) {
                                storeApplyParamDTO.setSuperInfo("当前参数继承“" + entry.getValue().getShortName() + "组织”数据");
                            }
                            return storeApplyParamDTO;
                        }
                        break;
                    }
                    default:
                        throw new BusinessErrorException("非法的参数类型");
                }
                if (entry.getValue().equals(orgDTOMap.firstEntry().getValue())) {
                    return storeApplyParamDTO;
                }
            }
            return new StoreApplyParamDTO();
        } catch (BusinessErrorException e) {
            logger.warn("获取自动请货参数列表失败", e);
            throw e;
        } catch (Exception e) {
            logger.error("获取自动请货参数列表失败", e);
            throw e;
        }
    }

    private StoreApplyParamDTO getStoreApplyParamDTO(Long orgId, StoreApplyParamDTO storeApplyParamDTO, Long id) {
        IscmStoreApplyParamOrgExample orgExample = new IscmStoreApplyParamOrgExample();
        orgExample.createCriteria().andOrgIdEqualTo(id).andParamCodeIn(Lists.newArrayList(ApplyParamCodeEnum.MIDDLE_DEAL_SWITCH.getCode(), ApplyParamCodeEnum.REMAINDER_DEAL.getCode()));
        Map<String, IscmStoreApplyParamOrg> paramOrgMap = iscmStoreApplyParamOrgMapper.selectByExample(orgExample).stream().collect(Collectors.toMap(IscmStoreApplyParamOrg::getParamCode, Function.identity(), (k1, k2) -> k1));
        if (MapUtils.isNotEmpty(paramOrgMap)) {
            AutoApplyParamDTO middleDealSwitchDTO = new AutoApplyParamDTO();
            BeanUtils.copyProperties(paramOrgMap.get(ApplyParamCodeEnum.MIDDLE_DEAL_SWITCH.getCode()), middleDealSwitchDTO);
            middleDealSwitchDTO.setGmtCreate(DateUtils.conventDateStrByDate(paramOrgMap.get(ApplyParamCodeEnum.MIDDLE_DEAL_SWITCH.getCode()).getGmtCreate(), DateUtils.DATETIME_PATTERN));
            middleDealSwitchDTO.setGmtUpdate(DateUtils.conventDateStrByDate(paramOrgMap.get(ApplyParamCodeEnum.MIDDLE_DEAL_SWITCH.getCode()).getGmtUpdate(), DateUtils.DATETIME_PATTERN));
            storeApplyParamDTO.setMiddleDealSwitchDTO(middleDealSwitchDTO);
            if (paramOrgMap.containsKey(ApplyParamCodeEnum.REMAINDER_DEAL.getCode())) {
                AutoApplyRemainderDealDTO remainderDealDTO = new AutoApplyRemainderDealDTO();
                IscmStoreApplyParamOrg remainderDeal = paramOrgMap.get(ApplyParamCodeEnum.REMAINDER_DEAL.getCode());
                BeanUtils.copyProperties(remainderDeal, remainderDealDTO);
                ApplyParamRemainderDealEnum remainderDealEnum = Optional.ofNullable(ApplyParamRemainderDealEnum.getEnumByCode(remainderDeal.getParamValue())).orElse(ApplyParamRemainderDealEnum.SELF);
                if (ApplyParamRemainderDealEnum.SELF.equals(remainderDealEnum)) {
                    remainderDealDTO.setParamValue(remainderDealEnum.getCode());
                    remainderDealDTO.setSelf(new BigDecimal(remainderDeal.getParamValue()));
                } else {
                    remainderDealDTO.setParamValue(remainderDeal.getParamValue());
                }
                remainderDealDTO.setGmtCreate(DateUtils.conventDateStrByDate(remainderDeal.getGmtCreate(), DateUtils.DATETIME_PATTERN));
                remainderDealDTO.setGmtUpdate(DateUtils.conventDateStrByDate(remainderDeal.getGmtUpdate(), DateUtils.DATETIME_PATTERN));
                storeApplyParamDTO.setRemainderDealDTO(remainderDealDTO);
            }
            return storeApplyParamDTO;
        }
        return storeApplyParamDTO;
    }

    private List<IscmStoreApplyParamOrg> getIscmStoreApplyParamOrgs(Long orgId) {
        IscmStoreApplyParamOrgExample orgExample = new IscmStoreApplyParamOrgExample();
        orgExample.createCriteria().andOrgIdEqualTo(orgId).andParamCodeEqualTo(ApplyParamCodeEnum.STORE_AUTO_APPLY_TIME.getCode());
        orgExample.setLimit(1);
        List<IscmStoreApplyParamOrg> paramOrgs = iscmStoreApplyParamOrgMapper.selectByExample(orgExample);
        return paramOrgs;
    }

    private Map<String, IscmStoreApplyParamOrg> getIscmStoreApplyParamMiddleCategory(Long orgId) {
        IscmStoreApplyParamOrgExample orgExample = new IscmStoreApplyParamOrgExample();
        orgExample.createCriteria().andOrgIdEqualTo(orgId).andParamCodeIn(Lists.newArrayList(ApplyParamCodeEnum.ZY_MIDDLE_CATEGORY.getCode(), ApplyParamCodeEnum.JM_MIDDLE_CATEGORY.getCode()));
        Map<String, IscmStoreApplyParamOrg> paramOrgs = iscmStoreApplyParamOrgMapper.selectByExample(orgExample).stream().collect(Collectors.toMap(IscmStoreApplyParamOrg::getParamCode, Function.identity(), (k1, k2) -> k1));
        return paramOrgs;
    }

    public List<String> tagEntityCodeListQuery(Long tagId) {
        TagEntityQueryServerParam queryServerParam = new TagEntityQueryServerParam();
        queryServerParam.setManagerFlag(YNEnum.YES.getType());
        queryServerParam.setResultType(ResultTypeEnums.ENTITYCODE.getType());
        queryServerParam.setTagBizType(tagBizType);
        queryServerParam.setTagId(tagId);
        queryServerParam.setEntityType(EntityTypeEnum.STORE.getType());
        List<String> storeOrgList = tagService.tagEntityCodeListQuery(queryServerParam);
        return storeOrgList;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAutoApplyList(StoreApplyParamDTO param, TokenUserDTO userDTO) throws Exception {
        ApplyParamTypeEnum paramTypeEnum = ApplyParamTypeEnum.AUTO_APPLY;
        try {
            Optional.ofNullable(param.getOrgId()).orElseThrow(() -> new BusinessErrorException("请选择机构"));
            Optional.ofNullable(param.getOrgType()).orElseThrow(() -> new BusinessErrorException("机构级别为空"));
            OrgTypeEnum orgTypeEnum = Optional.ofNullable(OrgTypeEnum.getEnumByCode(param.getOrgType())).orElseThrow(() -> new BusinessErrorException("非法的参数级别"));
            if (!(orgTypeEnum.equals(OrgTypeEnum.ORG_TYPE_COMPANY) || orgTypeEnum.equals(OrgTypeEnum.ORG_TYPE_REGIONAL_PLATFORM) || orgTypeEnum.equals(OrgTypeEnum.ORG_TYPE_BUSINESS) || orgTypeEnum.equals(OrgTypeEnum.ORG_TYPE_STORE_GROUP))) {
                throw new BusinessErrorException("仅支持总部/平台/连锁/门店组级别的参数");
            }
//            List<OrgTreeVO> userDataOrgInTypesList = permissionService.listUserDataScopeTreesByTypes(userDTO.getUserId(), null, Lists.newArrayList(orgTypeEnum.getCode()), orgTypeEnum.getCode());
//            OrgTreeVO orgTreeVO = userDataOrgInTypesList.stream().filter(v -> v.getId().equals(param.getOrgId())).findAny().orElseThrow(() -> new BusinessErrorException("所选组织超出您的数据权限范围"));
            OrgSimpleDTO orgSimpleDTO = null;
            List<OrgDTO> parents = new ArrayList<>();
            if (!orgTypeEnum.equals(OrgTypeEnum.ORG_TYPE_STORE_GROUP)) {
                orgSimpleDTO = storeApplyParamOrgService.checkUserAllPermByOrgId(userDTO.getUserId(), param.getOrgId());
                parents = permissionService.listDirectParentOrgByOrgId(orgSimpleDTO.getId());
            }
            storeApplyParamPushService.pushParams(userDTO, param.getOrgId(), param.getOrgType(),paramTypeEnum.getCode(),Constants.HD_PUSH_DEL_DATA_STATUS, null);
            if (Objects.nonNull(param.getGoodsLevelParamDTOS())) {
                saveGoodsLevel(param.getGoodsLevelParamDTOS(), userDTO, orgSimpleDTO, parents, param.getOrgId());
            }
            if (Objects.nonNull(param.getGoodsLimitParamDTOS())) {
                saveGoodsLimit(param.getGoodsLimitParamDTOS(), userDTO, orgSimpleDTO, parents, param.getOrgId());
            }
            if (Objects.nonNull(param.getCombinedCodeRange())) {
                AutoApplyParamDTO combinedCodeRange = param.getCombinedCodeRange();
                combinedCodeRange.setParamCode(ApplyParamCodeEnum.COMBINED_CODE_RANGE.getCode());
                saveAutoApply(Lists.newArrayList(combinedCodeRange), userDTO, orgSimpleDTO, parents, param.getOrgId());
            }
            if (Objects.nonNull(param.getOnwayStockDTO())) {
                AutoApplyParamDTO onwayStockDTO = param.getOnwayStockDTO();
                onwayStockDTO.setParamCode(ApplyParamCodeEnum.ON_WAY_STOCK_CALCULATION.getCode());
                saveAutoApply(Lists.newArrayList(onwayStockDTO), userDTO, orgSimpleDTO, parents, param.getOrgId());
            }
            if (Objects.nonNull(param.getUnAbleStockDTO())) {
                AutoApplyParamDTO unAbleStockDTO = param.getUnAbleStockDTO();
                unAbleStockDTO.setParamCode(ApplyParamCodeEnum.UNABLE_STOCK_CALCULATION.getCode());
                saveAutoApply(Lists.newArrayList(unAbleStockDTO), userDTO, orgSimpleDTO, parents, param.getOrgId());
            }
            if (CollectionUtils.isNotEmpty(param.getSpecialGoodsCtrlDTOS())) {
                saveAutoApply(param.getSpecialGoodsCtrlDTOS(), userDTO, orgSimpleDTO, parents, param.getOrgId());
            }
            if (Objects.nonNull(param.getAutoApplyTimeDTO())) {
                paramTypeEnum = ApplyParamTypeEnum.AUTO_APPLY_TIME;
                AutoApplyParamDTO autoApplyTimeDTO = param.getAutoApplyTimeDTO();
                autoApplyTimeDTO.setParamCode(ApplyParamCodeEnum.STORE_AUTO_APPLY_TIME.getCode());
                saveAutoApply(Lists.newArrayList(autoApplyTimeDTO), userDTO, orgSimpleDTO, parents, param.getOrgId());
            }
            if (Objects.nonNull(param.getMiddleDealSwitchDTO())) {
                paramTypeEnum = ApplyParamTypeEnum.MIDDLE_PACKAGE_GOODS_APPLY;
                AutoApplyParamDTO autoApplyTimeDTO = param.getMiddleDealSwitchDTO();
                autoApplyTimeDTO.setParamCode(ApplyParamCodeEnum.MIDDLE_DEAL_SWITCH.getCode());
                saveAutoApply(Lists.newArrayList(autoApplyTimeDTO), userDTO, orgSimpleDTO, parents, param.getOrgId());
                if ("true".equals(autoApplyTimeDTO.getParamValue())) {
                    if (Objects.isNull(param.getRemainderDealDTO())) {
                        throw new BusinessErrorException("余数处理方式不能为空");
                    }
                    AutoApplyRemainderDealDTO remainderDealDTO = param.getRemainderDealDTO();
                    AutoApplyParamDTO autoApplyTimeDTO1 = new AutoApplyParamDTO();
                    BeanUtils.copyProperties(remainderDealDTO, autoApplyTimeDTO1);
                    if (ApplyParamRemainderDealEnum.SELF.getCode().equals(remainderDealDTO.getParamValue())) {
                        autoApplyTimeDTO1.setParamValue(remainderDealDTO.getSelf().toPlainString());
                    }
                    autoApplyTimeDTO1.setParamCode(ApplyParamCodeEnum.REMAINDER_DEAL.getCode());
                    saveAutoApply(Lists.newArrayList(autoApplyTimeDTO1), userDTO, orgSimpleDTO, parents, param.getOrgId());
                }
            }
            if (null != param.getZyStoreMiddleCategorys()) {
                List<Long> zy = CollectionUtils.isEmpty(param.getZyStoreMiddleCategorys()) ? Lists.newArrayList() : param.getZyStoreMiddleCategorys().stream().filter(v -> v.toString().length() == 4).distinct().collect(Collectors.toList());
                paramTypeEnum = ApplyParamTypeEnum.APPLY_MIDDLE_CATEGORY;
                AutoApplyParamDTO middleCategory = new AutoApplyParamDTO();
                middleCategory.setParamCode(ApplyParamCodeEnum.ZY_MIDDLE_CATEGORY.getCode());
                middleCategory.setParamValue(zy.stream().map(v -> v.toString()).collect(Collectors.joining(",")));
                saveAutoApply(Lists.newArrayList(middleCategory), userDTO, orgSimpleDTO, parents, param.getOrgId());
            }
            if (null != param.getJmStoreMiddleCategorys()) {
                List<Long> jm = CollectionUtils.isEmpty(param.getJmStoreMiddleCategorys()) ? Lists.newArrayList() : param.getJmStoreMiddleCategorys().stream().filter(v -> v.toString().length() == 4).distinct().collect(Collectors.toList());
                paramTypeEnum = ApplyParamTypeEnum.APPLY_MIDDLE_CATEGORY;
                AutoApplyParamDTO middleCategory = new AutoApplyParamDTO();
                middleCategory.setParamCode(ApplyParamCodeEnum.JM_MIDDLE_CATEGORY.getCode());
                middleCategory.setParamValue(jm.stream().map(v -> v.toString()).collect(Collectors.joining(",")));
                saveAutoApply(Lists.newArrayList(middleCategory), userDTO, orgSimpleDTO, parents, param.getOrgId());
            }
            if (!param.getOrgType().equals(OrgTypeEnum.ORG_TYPE_STORE_GROUP.getCode())) {
                storeApplyParamPushService.pushParams(userDTO, param.getOrgId(), param.getOrgType(), null, paramTypeEnum.getCode());
            } else {
                storeApplyParamPushService.pushParams(userDTO, param.getOrgId(), param.getOrgType(), paramTypeEnum.getCode(),Constants.HD_PUSH_INSERT_DATA_STATUS,null);
            }
        } catch (BusinessErrorException e) {
            logger.warn("保存自动请货参数失败", e);
            throw e;
        } catch (Exception e) {
            logger.error("保存自动请货参数失败", e);
            throw e;
        }
    }

    @Override
    public List<DropDownBoxDTO> dropDownBoxList(Integer type) throws Exception {
        switch (type) {
            case 1:
                return Arrays.stream(ApplyParamOnWayStockEnum.values()).map(v -> {
                    DropDownBoxDTO dto = new DropDownBoxDTO();
                    dto.setCode(v.getCode());
                    dto.setName(v.getName());
                    return dto;
                }).collect(Collectors.toList());
            case 2:
                return Arrays.stream(ApplyParamUnableStockEnum.values()).map(v -> {
                    DropDownBoxDTO dto = new DropDownBoxDTO();
                    dto.setCode(v.getCode());
                    dto.setName(v.getName());
                    return dto;
                }).collect(Collectors.toList());
            case 3:
                return Arrays.stream(ApplyParamGoodsLevelEnum.values()).map(v -> {
                    DropDownBoxDTO dto = new DropDownBoxDTO();
                    dto.setCode(String.valueOf(v.getCode()));
                    dto.setName(v.getName());
                    return dto;
                }).collect(Collectors.toList());
            case 4:
                return Arrays.stream(ApplyParamStoreDistrCircleEnum.values()).map(v -> {
                    DropDownBoxDTO dto = new DropDownBoxDTO();
                    dto.setCode(v.getCode());
                    dto.setName(v.getName());
                    return dto;
                }).collect(Collectors.toList());
            case 5:
                return Arrays.stream(ApplyParamStorePropertyEnum.values()).map(v -> {
                    DropDownBoxDTO dto = new DropDownBoxDTO();
                    dto.setCode(String.valueOf(v.getCode()));
                    dto.setName(v.getName());
                    return dto;
                }).collect(Collectors.toList());
            case 6:
                return Arrays.stream(ApplyParamGoodsClassEnum.values()).map(v -> {
                    DropDownBoxDTO dto = new DropDownBoxDTO();
                    dto.setCode(String.valueOf(v.getCode()));
                    dto.setName(v.getName());
                    return dto;
                }).collect(Collectors.toList());
            case 7:
                return Arrays.stream(ApplyParamGoodsManagementAttrEnum.values()).map(v -> {
                    DropDownBoxDTO dto = new DropDownBoxDTO();
                    dto.setCode(String.valueOf(v.getCode()));
                    dto.setName(v.getName());
                    return dto;
                }).collect(Collectors.toList());
            case 8:
                return Arrays.stream(ApplyParamGoodsSignEnum.values()).map(v -> {
                    DropDownBoxDTO dto = new DropDownBoxDTO();
                    dto.setCode(String.valueOf(v.getCode()));
                    dto.setName(v.getName());
                    return dto;
                }).collect(Collectors.toList());
            case 9:
                return Arrays.stream(ApplyParamGoodsTypeEnum.values()).map(v -> {
                    DropDownBoxDTO dto = new DropDownBoxDTO();
                    dto.setCode(String.valueOf(v.getCode()));
                    dto.setName(v.getName());
                    return dto;
                }).collect(Collectors.toList());
            case 10:
                return Arrays.stream(ApplyParamGoodsPushLevelEnum.values()).map(v -> {
                    DropDownBoxDTO dto = new DropDownBoxDTO();
                    dto.setCode(v.getName());
                    dto.setName(v.getName());
                    return dto;
                }).collect(Collectors.toList());
            case 11:
                return Arrays.stream(ApplyParamGoodsSpecialEnum.values()).map(v -> {
                    DropDownBoxDTO dto = new DropDownBoxDTO();
                    dto.setCode(v.getName());
                    dto.setName(v.getName());
                    return dto;
                }).collect(Collectors.toList());
            case 12:
                return Arrays.stream(ParamConfigScopeEnum.values()).map(v -> {
                    DropDownBoxDTO dto = new DropDownBoxDTO();
                    dto.setCode(String.valueOf(v.getCode()));
                    dto.setName(v.getName());
                    return dto;
                }).collect(Collectors.toList());
            case 13 :
                return Arrays.stream(RegisterTypeEnum.values()).map(v -> {
                    DropDownBoxDTO dto = new DropDownBoxDTO();
                    dto.setCode(String.valueOf(v.getCode()));
                    dto.setName(v.getName());
                    return dto;
                }).collect(Collectors.toList());
            case 14 :
                return Arrays.stream(ApplyParamCutGoodsTypeEnum.values()).map(v -> {
                    DropDownBoxDTO dto = new DropDownBoxDTO();
                    dto.setCode(String.valueOf(v.getCode()));
                    dto.setName(v.getName());
                    return dto;
                }).collect(Collectors.toList());
            case 15 :
                return Arrays.stream(ReturnTypeEnum.values()).map(v -> {
                    DropDownBoxDTO dto = new DropDownBoxDTO();
                    dto.setCode(String.valueOf(v.getType()));
                    dto.setName(v.getDesc());
                    return dto;
                }).collect(Collectors.toList());
            default:
                throw new BusinessErrorException("非法的类型");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void superParentParam(Long orgId, Integer orgType, Integer paramType, Integer paramScope, TokenUserDTO userDTO) throws Exception {
        try {
            Optional.ofNullable(orgId).orElseThrow(() -> new BusinessErrorException("请选择机构"));
            Optional.ofNullable(orgType).orElseThrow(() -> new BusinessErrorException("机构级别为空"));
            OrgTypeEnum orgTypeEnum = Optional.ofNullable(OrgTypeEnum.getEnumByCode(orgType)).orElseThrow(() -> new BusinessErrorException("非法的参数级别"));
            ApplyParamTypeEnum paramTypeEnum = Optional.ofNullable(ApplyParamTypeEnum.getEnumByCode(paramType)).orElseThrow(() -> new BusinessErrorException("非法的参数类型"));
            if (!(orgTypeEnum.equals(OrgTypeEnum.ORG_TYPE_REGIONAL_PLATFORM) || orgTypeEnum.equals(OrgTypeEnum.ORG_TYPE_BUSINESS))) {
                throw new BusinessErrorException("仅支持平台/连锁级别的参数");
            }
            List<OrgTreeVO> userDataOrgInTypesList = permissionService.listUserDataScopeTreesByTypes(userDTO.getUserId(), null, Lists.newArrayList(orgType), orgType);
            OrgTreeVO orgTreeVO = userDataOrgInTypesList.stream().filter(v -> orgId.equals(v.getId())).findAny().orElseThrow(() -> new BusinessErrorException("所选组织超出数据权限范围"));
            Map<Long, OrgDTO> parentMap = permissionService.listDirectParentOrgByOrgId(orgTreeVO.getId()).stream()
                    .filter(v -> v.getType().equals(OrgTypeEnum.ORG_TYPE_REGIONAL_PLATFORM.getCode()) || v.getType().equals(OrgTypeEnum.ORG_TYPE_COMPANY.getCode()))
                    .filter(v -> v.getType() < orgTreeVO.getType()).collect(Collectors.toMap(OrgDTO::getId, Function.identity(), (k1, k2) -> k1));
            if (MapUtils.isEmpty(parentMap)) {
                throw new BusinessErrorException("没有查询到上级信息");
            }
            deleteParamByType(Lists.newArrayList(orgId), paramTypeEnum, YNEnum.YES.getType(), paramScope, orgId, userDTO);

            storeApplyParamPushService.pushParams(userDTO, orgId, orgType, paramTypeEnum.getCode(), paramScope);

            // 下发海典
            List<EmployeeInfoVO> employeeInfoVOS = permissionService.getEmployeeInfoByUserIdList(Lists.newArrayList(userDTO.getUserId()));
            EmployeeInfoVO employeeInfoVO = CollectionUtils.isNotEmpty(employeeInfoVOS) ? employeeInfoVOS.get(0) : new EmployeeInfoVO();
            executor.execute(() -> {
                try {
                    storeApplyParamPushHDService.sendHDNonATMCByExtend(orgId, employeeInfoVO.getEmpCode(), parentMap);
                } catch (Exception e) {
                    logger.error("不自动请货商品参数下发海典失败: " + e.getMessage(), e);
                }
            });

        } catch (BusinessErrorException e) {
            logger.warn("继承上级参数失败", e);
            throw e;
        } catch (Exception e) {
            logger.error("继承上级参数失败", e);
            throw e;
        }
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void deleteParamByType(List<Long> orgIds, ApplyParamTypeEnum paramTypeEnum, Integer superType, Integer paramScope, Long sourceOrgId, TokenUserDTO userDTO) throws Exception {
        switch (paramTypeEnum) {
            case AUTO_APPLY:
                IscmStoreApplyParamOrgExample paramOrgExample = new IscmStoreApplyParamOrgExample();
                paramOrgExample.createCriteria().andOrgIdIn(orgIds).andParamCodeIn(Lists.newArrayList(ApplyParamCodeEnum.COMBINED_CODE_RANGE.getCode(), ApplyParamCodeEnum.ON_WAY_STOCK_CALCULATION.getCode(), ApplyParamCodeEnum.UNABLE_STOCK_CALCULATION.getCode(), ApplyParamCodeEnum.SPECIAL_CTRL_GOODS_SWITCH.getCode(), ApplyParamCodeEnum.SPECIAL_GOODS_PER_THIRTY_DAYS_APPLY_LIMIT.getCode(), ApplyParamCodeEnum.SPECIAL_GOODS_ONCE_APPLY_LIMIT.getCode()));;
                iscmStoreApplyParamOrgMapper.deleteByExample(paramOrgExample);
                IscmStoreApplyParamGoodsLimitOrgExample limitOrgExample = new IscmStoreApplyParamGoodsLimitOrgExample();
                limitOrgExample.createCriteria().andOrgIdIn(orgIds).andParamCodeEqualTo(ApplyParamCodeEnum.GOODS_LEVEL_LIMIT_DAYS.getCode());
                iscmStoreApplyParamGoodsLimitOrgMapper.deleteByExample(limitOrgExample);
                IscmStoreApplyParamGoodsLevelOrgExample levelOrgExample = new IscmStoreApplyParamGoodsLevelOrgExample();
                levelOrgExample.createCriteria().andOrgIdIn(orgIds).andParamCodeEqualTo(ApplyParamCodeEnum.GOODS_LEVEL.getCode());
                iscmStoreApplyParamGoodsLevelOrgMapper.deleteByExample(levelOrgExample);
                break;
            case AUTO_APPLY_TIME:
                IscmStoreApplyParamOrgExample paramOrgExample1 = new IscmStoreApplyParamOrgExample();
                paramOrgExample1.createCriteria().andOrgIdIn(orgIds).andParamCodeIn(Lists.newArrayList(ApplyParamCodeEnum.STORE_AUTO_APPLY_TIME.getCode()));
                iscmStoreApplyParamOrgMapper.deleteByExample(paramOrgExample1);
                break;
            case MIDDLE_PACKAGE_GOODS_APPLY:
                IscmStoreApplyParamOrgExample paramOrgExample2 = new IscmStoreApplyParamOrgExample();
                paramOrgExample2.createCriteria().andOrgIdIn(orgIds).andParamCodeIn(Lists.newArrayList(ApplyParamCodeEnum.MIDDLE_DEAL_SWITCH.getCode(), ApplyParamCodeEnum.REMAINDER_DEAL.getCode()));
                iscmStoreApplyParamOrgMapper.deleteByExample(paramOrgExample2);
                break;
            case NON_AUTOMATIC_APPLY:
                IscmStoreApplyParamOrgExample paramOrgExample3 = new IscmStoreApplyParamOrgExample();
                // 先删除门店
                IscmStoreApplyParamOrgExample.Criteria criteria = paramOrgExample3.createCriteria();
                criteria.andOrgIdIn(orgIds).andParamCodeEqualTo(ApplyParamCodeEnum.NON_AUTOMATIC_APPLY_STORE.getCode());
                List<IscmStoreApplyParamOrg> paramOrgs = iscmStoreApplyParamOrgMapper.selectByExample(paramOrgExample3);
                if (CollectionUtils.isNotEmpty(paramOrgs)) {
                    Iterator<IscmStoreApplyParamOrg> iterator = paramOrgs.iterator();
                    while (iterator.hasNext()) {
                        IscmStoreApplyParamOrg next = iterator.next();
                        StoreComponentQueryParam param = new StoreComponentQueryParam();
                        param.setId(Long.valueOf(next.getParamValue()));
                        param.setBizCode("ISCM");
                        // 业务类型：1001(数字化商品),1002(供应链-不请货门店)
                        param.setBizType("1002");
                        if (tagGoodsService.delRule(param)) {
                            iterator.remove();
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(paramOrgs)) {
                    // 不为空 则有没删除的,需排除这些删除
                    criteria.andIdNotIn(paramOrgs.stream().map(v -> Long.valueOf(v.getParamValue())).collect(Collectors.toList()));
                }
                iscmStoreApplyParamOrgMapper.deleteByExample(paramOrgExample3);
                IscmStoreApplyParamNoAutoApplyStoreExample example = new IscmStoreApplyParamNoAutoApplyStoreExample();
                example.createCriteria().andOrgIdIn(orgIds);
                iscmStoreApplyParamNoAutoApplyStoreMapper.deleteByExample(example);
                IscmStoreApplyChooseStoreExample chooseExample = new IscmStoreApplyChooseStoreExample();
                chooseExample.createCriteria().andOrgIdIn(orgIds).andParamCodeEqualTo(ApplyParamCodeEnum.NON_AUTOMATIC_APPLY_STORE.getCode());
                iscmStoreApplyChooseStoreMapper.deleteByExample(chooseExample);
                paramOrgExample3.clear();
                paramOrgExample3.createCriteria().andOrgIdIn(orgIds).andParamCodeIn(Lists.newArrayList(ApplyParamCodeEnum.NON_AUTOMATIC_APPLY_NEW_STORE_MONTH.getCode(), ApplyParamCodeEnum.NON_AUTOMATIC_APPLY_NEW_GOODS_DAY.getCode(), ApplyParamCodeEnum.NON_AUTOMATIC_APPLY_STORE_PROPERTY.getCode(), ApplyParamCodeEnum.NON_AUTOMATIC_APPLY_GOODS_CLASS.getCode(), ApplyParamCodeEnum.NON_AUTOMATIC_APPLY_GOODS_MANAGEMENT_ATTR.getCode(), ApplyParamCodeEnum.NON_AUTOMATIC_APPLY_GOODS_SIGN.getCode(), ApplyParamCodeEnum.CUT_APPLY_GOODS_TYPE.getCode()));
                iscmStoreApplyParamOrgMapper.deleteByExample(paramOrgExample3);
                break;
            case APPLY_GOODS_STOCK_LIMIT:
//                OrgVO source = permissionService.getOrgInfoById(sourceOrgId);
//                if (Objects.isNull(source)) {
//                    throw new BusinessErrorException("请选择" + (YNEnum.YES.getType().equals(superType) ? "继承" : "下发") + "的组织Id");
//                }
                IscmStoreApplyParamGoodsCatalogLimitOrgExample catalogLimitOrgExample = new IscmStoreApplyParamGoodsCatalogLimitOrgExample();
                catalogLimitOrgExample.createCriteria().andOrgIdIn(orgIds).andParamCodeEqualTo(ApplyParamCodeEnum.GOODS_CATALOG_LIMIT.getCode());
                iscmStoreApplyParamGoodsCatalogLimitOrgMapper.deleteByExample(catalogLimitOrgExample);
//                if (YNEnum.NO.getType().equals(superType)) {
//                    List<OrgDTO> orgDTOS = permissionService.listOrgInfoByIds(orgIds, false);
//                    for (OrgDTO orgDTO : orgDTOS) {
//                        List<OrgSimpleDTO> storeDTOS = permissionService.getUserDataScopeChildOrgByOrgId(userDTO.getUserId(), Lists.newArrayList(orgDTO.getId())).stream().filter(v -> v.getType().equals(OrgTypeEnum.ORG_TYPE_STORE.getCode())).collect(Collectors.toList());
//                        if (CollectionUtils.isEmpty(storeDTOS)) {
//                            continue;
//                        }
//                        catalogLimitOrgExample.clear();
//                        catalogLimitOrgExample.createCriteria().andOrgIdEqualTo(sourceOrgId).andStoreIdIn(storeDTOS.stream().map(OrgSimpleDTO::getId).collect(Collectors.toList())).andParamCodeEqualTo(ApplyParamCodeEnum.GOODS_CATALOG_LIMIT.getCode());
//                        List<IscmStoreApplyParamGoodsCatalogLimitOrg> catalogLimitOrgs = iscmStoreApplyParamGoodsCatalogLimitOrgMapper.selectByExample(catalogLimitOrgExample);
//                        if (CollectionUtils.isEmpty(catalogLimitOrgs)){
//                            continue;
//                        }
//                        catalogLimitOrgs = catalogLimitOrgs.stream().map(v -> {
//                            v.setCreatedBy(userDTO.getUserId());
//                            v.setCreatedName(userDTO.getName());
//                            v.setUpdatedBy(userDTO.getUserId());
//                            v.setUpdatedName(userDTO.getName());
//                            v.setOrgId(orgDTO.getId());
//                            v.setOrgName(orgDTO.getShortName());
//                            v.setParamLevel(orgDTO.getType());
//                            v.setParentOrgId(source.getId());
//                            v.setParentOrgName(source.getShortName());
//                            return v;
//                        }).collect(Collectors.toList());
//                        iscmStoreApplyParamGoodsCatalogLimitOrgExtendMapper.batchInsert(catalogLimitOrgs);
//                        }
//
//                } else {
//                    Map<Long, List<OrgDTO>> parentMap = permissionService.listDirectParentOrgByOrgIdBatch(orgIds);
//                    for (Map.Entry<Long, List<OrgDTO>> entry : parentMap.entrySet()){
//                        List<OrgDTO> superOrgDTOS = entry.getValue().stream().filter(v -> OrgTypeEnum.ORG_TYPE_REGIONAL_PLATFORM.getCode() == v.getType() || OrgTypeEnum.ORG_TYPE_COMPANY.getCode() == v.getType())
//                                .filter(v -> v.getType() < source.getType()).collect(Collectors.toList());
//                        if (CollectionUtils.isEmpty(superOrgDTOS)) {
//                            logger.info("没有需要继承的数据");
//                            continue;
//                        }
//                        catalogLimitOrgExample.clear();
//                        catalogLimitOrgExample.createCriteria().andOrgIdIn(superOrgDTOS.stream().map(OrgDTO::getId).collect(Collectors.toList())).andParamCodeEqualTo(ApplyParamCodeEnum.GOODS_CATALOG_LIMIT.getCode());
//                        Map<Long, List<IscmStoreApplyParamGoodsCatalogLimitOrg>> map = iscmStoreApplyParamGoodsCatalogLimitOrgMapper.selectByExample(catalogLimitOrgExample).stream().collect(Collectors.groupingBy(IscmStoreApplyParamGoodsCatalogLimitOrg::getOrgId));
//                        TreeMap<Integer, OrgDTO> orgDTOTreeMap = new TreeMap<>(superOrgDTOS.stream().collect(Collectors.toMap(OrgDTO::getType, Function.identity(), (k1, k2) -> k1)));
//                        NavigableMap<Integer, OrgDTO> descendingMap = orgDTOTreeMap.descendingMap();
//                        for(NavigableMap.Entry<Integer, OrgDTO> dtoEntry : descendingMap.entrySet()) {
//                            List<IscmStoreApplyParamGoodsCatalogLimitOrg> limitOrgs = map.get(dtoEntry.getValue().getId());
//                            if (CollectionUtils.isNotEmpty(limitOrgs)) {
//                                limitOrgs = limitOrgs.stream().map(v -> {
//                                    v.setCreatedBy(userDTO.getUserId());
//                                    v.setCreatedName(userDTO.getName());
//                                    v.setUpdatedBy(userDTO.getUserId());
//                                    v.setUpdatedName(userDTO.getName());
//                                    v.setOrgId(dtoEntry.getValue().getId());
//                                    v.setOrgName(dtoEntry.getValue().getShortName());
//                                    v.setParamLevel(dtoEntry.getValue().getType());
//                                    if (dtoEntry.getValue().getType() > OrgTypeEnum.ORG_TYPE_COMPANY.getCode()) {
//                                        OrgDTO parentOrg = descendingMap.lastEntry().getValue();
//                                        v.setParentOrgId(parentOrg.getId());
//                                        v.setParentOrgName(parentOrg.getShortName());
//                                    }
//                                    return v;
//                                }).collect(Collectors.toList());
//                                iscmStoreApplyParamGoodsCatalogLimitOrgExtendMapper.batchInsert(limitOrgs);
//                                break;
//                            } else {
//                                continue;
//                            }
//                        }
//                    }
//                }
                break;
            case APPLY_GOODS_UPPER_LIMIT:
                ApplyUpperLimitScopeEnum scopeEnum = Optional.ofNullable(ApplyUpperLimitScopeEnum.getEnumByCode(paramScope)).orElseThrow(() -> new BusinessErrorException("请选择请货上限参数类型"));
                if (scopeEnum.equals(ApplyUpperLimitScopeEnum.NORMAL)) {
                    IscmStoreApplyParamGoodsUpperLimitOrgExample upperLimitOrgExample = new IscmStoreApplyParamGoodsUpperLimitOrgExample();
                    upperLimitOrgExample.createCriteria().andOrgIdIn(orgIds).andParamCodeEqualTo(ApplyParamCodeEnum.GOODS_UPPER_LIMIT.getCode());
                    iscmStoreApplyParamGoodsUpperLimitOrgMapper.deleteByExample(upperLimitOrgExample);
                }
                IscmStoreApplyParamGoodsUpperLimitItemOrgExample upperLimitItemOrgExample = new IscmStoreApplyParamGoodsUpperLimitItemOrgExample();
                upperLimitItemOrgExample.createCriteria().andOrgIdIn(orgIds).andParamCodeEqualTo(ApplyParamCodeEnum.GOODS_UPPER_LIMIT_ITEM.getCode()).andParamScopeEqualTo(paramScope);
                iscmStoreApplyParamGoodsUpperLimitItemOrgMapper.deleteByExample(upperLimitItemOrgExample);
                IscmStoreApplyParamOrgExample orgExample = new IscmStoreApplyParamOrgExample();
                orgExample.createCriteria().andOrgIdIn(orgIds).andParamCodeIn(scopeEnum.equals(ApplyUpperLimitScopeEnum.NORMAL) ?
                        Lists.newArrayList(ApplyParamCodeEnum.CAN_MODIFY_ITEM_RATIO.getCode(), ApplyParamCodeEnum.CAN_MODIFY_QTY_UPPER_RATIO.getCode(), ApplyParamCodeEnum.CAN_MODIFY_QTY_LOWER_RATIO.getCode()) :
                        Lists.newArrayList(ApplyParamCodeEnum.CAN_MODIFY_ITEM_RATIO_ZY.getCode(), ApplyParamCodeEnum.CAN_MODIFY_QTY_UPPER_RATIO_ZY.getCode(), ApplyParamCodeEnum.CAN_MODIFY_QTY_LOWER_RATIO_ZY.getCode()));
                iscmStoreApplyParamOrgMapper.deleteByExample(orgExample);
                break;
            case APPLY_GOODS_AUTO_FLOAT:
                IscmStoreApplyAutoFloatExample floatExample = new IscmStoreApplyAutoFloatExample();
                floatExample.createCriteria().andOrgIdIn(orgIds).andParamCodeEqualTo(ApplyParamCodeEnum.STORE_AUTO_APPLY_FLOAT.getCode());
                long count = iscmStoreApplyAutoFloatMapper.countByExample(floatExample);
                if (count <= 0L) {
                    break;
                }
                for (int i = 0;; i++) {
                    floatExample.setLimit(Constants.BATCH_DELETE_ONCE_MAX_VALUE);
                    iscmStoreApplyAutoFloatExtendMapper.deleteByExample(floatExample);
                    if ((long) ((i + 1) * Constants.BATCH_DELETE_ONCE_MAX_VALUE) >= count) {
                        break;
                    }
                }
                break;
            case APPLY_MIDDLE_CATEGORY:
                IscmStoreApplyParamOrgExample paramOrgExample4 = new IscmStoreApplyParamOrgExample();
                paramOrgExample4.createCriteria().andOrgIdIn(orgIds).andParamCodeIn(Lists.newArrayList(ApplyParamCodeEnum.ZY_MIDDLE_CATEGORY.getCode(), ApplyParamCodeEnum.JM_MIDDLE_CATEGORY.getCode()));
                iscmStoreApplyParamOrgMapper.deleteByExample(paramOrgExample4);
                break;
            default:
                throw new BusinessErrorException("非法的参数类型");
        }
    }

    @Override
    public List<Long> getNoDefaultChooseOrgs(Long orgId, Integer orgType, Integer paramType, Integer paramScope, TokenUserDTO userDTO) throws Exception {
        try {
            Optional.ofNullable(orgType).orElseThrow(() -> new BusinessErrorException("机构级别为空"));
            OrgTypeEnum orgTypeEnum = Optional.ofNullable(OrgTypeEnum.getEnumByCode(orgType)).orElseThrow(() -> new BusinessErrorException("非法的参数级别"));
            if (!(orgTypeEnum.equals(OrgTypeEnum.ORG_TYPE_COMPANY) || orgTypeEnum.equals(OrgTypeEnum.ORG_TYPE_REGIONAL_PLATFORM))) {
                throw new BusinessErrorException("仅支持平台/总部级别进行下发");
            }
            ApplyParamTypeEnum paramTypeEnum = Optional.ofNullable(ApplyParamTypeEnum.getEnumByCode(paramType)).orElseThrow(() -> new BusinessErrorException("非法的参数类型"));
            Set<Long> orgIdSet = new HashSet<>();
            switch (paramTypeEnum) {
                case AUTO_APPLY:
                    IscmStoreApplyParamOrgExample paramOrgExample = new IscmStoreApplyParamOrgExample();
                    paramOrgExample.createCriteria().andParentOrgIdEqualTo(orgId).andParamCodeIn(Lists.newArrayList(ApplyParamCodeEnum.ON_WAY_STOCK_CALCULATION.getCode(), ApplyParamCodeEnum.UNABLE_STOCK_CALCULATION.getCode(), ApplyParamCodeEnum.SPECIAL_CTRL_GOODS_SWITCH.getCode(), ApplyParamCodeEnum.SPECIAL_GOODS_PER_THIRTY_DAYS_APPLY_LIMIT.getCode(), ApplyParamCodeEnum.SPECIAL_GOODS_ONCE_APPLY_LIMIT.getCode()));
                    orgIdSet.addAll(iscmStoreApplyParamOrgMapper.selectByExample(paramOrgExample).stream().map(IscmStoreApplyParamOrg::getOrgId).collect(Collectors.toSet()));
                    IscmStoreApplyParamGoodsLimitOrgExample limitOrgExample = new IscmStoreApplyParamGoodsLimitOrgExample();
                    limitOrgExample.createCriteria().andParentOrgIdEqualTo(orgId).andParamCodeEqualTo(ApplyParamCodeEnum.GOODS_LEVEL_LIMIT_DAYS.getCode());
                    orgIdSet.addAll(iscmStoreApplyParamGoodsLimitOrgMapper.selectByExample(limitOrgExample).stream().map(IscmStoreApplyParamGoodsLimitOrg::getOrgId).collect(Collectors.toSet()));
                    IscmStoreApplyParamGoodsLevelOrgExample levelOrgExample = new IscmStoreApplyParamGoodsLevelOrgExample();
                    levelOrgExample.createCriteria().andParentOrgIdEqualTo(orgId).andParamCodeEqualTo(ApplyParamCodeEnum.GOODS_LEVEL.getCode());
                    orgIdSet.addAll(iscmStoreApplyParamGoodsLevelOrgMapper.selectByExample(levelOrgExample).stream().map(IscmStoreApplyParamGoodsLevelOrg::getOrgId).collect(Collectors.toSet()));
                    return Lists.newArrayList(orgIdSet);
                case AUTO_APPLY_TIME:
                    IscmStoreApplyParamOrgExample paramOrgExample1 = new IscmStoreApplyParamOrgExample();
                    paramOrgExample1.createCriteria().andParentOrgIdEqualTo(orgId).andParamCodeEqualTo(ApplyParamCodeEnum.STORE_AUTO_APPLY_TIME.getCode());
                    orgIdSet.addAll(iscmStoreApplyParamOrgMapper.selectByExample(paramOrgExample1).stream().map(IscmStoreApplyParamOrg::getOrgId).collect(Collectors.toSet()));
                    return Lists.newArrayList(orgIdSet);
                case MIDDLE_PACKAGE_GOODS_APPLY:
                    IscmStoreApplyParamOrgExample paramOrgExample2 = new IscmStoreApplyParamOrgExample();
                    paramOrgExample2.createCriteria().andParentOrgIdEqualTo(orgId).andParamCodeIn(Lists.newArrayList(ApplyParamCodeEnum.MIDDLE_DEAL_SWITCH.getCode(), ApplyParamCodeEnum.REMAINDER_DEAL.getCode()));
                    orgIdSet.addAll(iscmStoreApplyParamOrgMapper.selectByExample(paramOrgExample2).stream().map(IscmStoreApplyParamOrg::getOrgId).collect(Collectors.toSet()));
                    return Lists.newArrayList(orgIdSet);
                case NON_AUTOMATIC_APPLY:
                    IscmStoreApplyParamOrgExample paramOrgExample3 = new IscmStoreApplyParamOrgExample();
                    paramOrgExample3.createCriteria().andParentOrgIdEqualTo(orgId).andParamCodeIn(Lists.newArrayList(ApplyParamCodeEnum.NON_AUTOMATIC_APPLY_NEW_STORE_MONTH.getCode(), ApplyParamCodeEnum.NON_AUTOMATIC_APPLY_NEW_GOODS_DAY.getCode(), ApplyParamCodeEnum.NON_AUTOMATIC_APPLY_STORE_PROPERTY.getCode(), ApplyParamCodeEnum.NON_AUTOMATIC_APPLY_GOODS_CLASS.getCode(), ApplyParamCodeEnum.NON_AUTOMATIC_APPLY_GOODS_MANAGEMENT_ATTR.getCode(), ApplyParamCodeEnum.NON_AUTOMATIC_APPLY_GOODS_SIGN.getCode()));
                    orgIdSet.addAll(iscmStoreApplyParamOrgMapper.selectByExample(paramOrgExample3).stream().map(IscmStoreApplyParamOrg::getOrgId).collect(Collectors.toSet()));
                    return Lists.newArrayList(orgIdSet);
                case APPLY_GOODS_UPPER_LIMIT:
                    ApplyUpperLimitScopeEnum scopeEnum = Optional.ofNullable(ApplyUpperLimitScopeEnum.getEnumByCode(paramScope)).orElseThrow(() -> new BusinessErrorException("请选择请货上限参数类型"));
                    IscmStoreApplyParamOrgExample paramOrgExample4 = new IscmStoreApplyParamOrgExample();
                    IscmStoreApplyParamOrgExample.Criteria criteria = paramOrgExample4.createCriteria();
                    criteria.andParentOrgIdEqualTo(orgId);
                    if (ApplyUpperLimitScopeEnum.NORMAL.equals(scopeEnum)) {
                        IscmStoreApplyParamGoodsUpperLimitOrgExample upperLimitOrgExample = new IscmStoreApplyParamGoodsUpperLimitOrgExample();
                        upperLimitOrgExample.createCriteria().andParentOrgIdEqualTo(orgId).andParamCodeEqualTo(ApplyParamCodeEnum.GOODS_UPPER_LIMIT.getCode());
                        orgIdSet.addAll(iscmStoreApplyParamGoodsUpperLimitOrgMapper.selectByExample(upperLimitOrgExample).stream().map(IscmStoreApplyParamGoodsUpperLimitOrg::getOrgId).collect(Collectors.toSet()));
                        criteria.andParamCodeIn(Lists.newArrayList(ApplyParamCodeEnum.CAN_MODIFY_ITEM_RATIO.getCode(), ApplyParamCodeEnum.CAN_MODIFY_QTY_UPPER_RATIO.getCode(), ApplyParamCodeEnum.CAN_MODIFY_QTY_LOWER_RATIO.getCode()));
                    } else {
                        criteria.andParamCodeIn(Lists.newArrayList(ApplyParamCodeEnum.CAN_MODIFY_ITEM_RATIO_ZY.getCode(), ApplyParamCodeEnum.CAN_MODIFY_QTY_UPPER_RATIO_ZY.getCode(), ApplyParamCodeEnum.CAN_MODIFY_QTY_LOWER_RATIO_ZY.getCode()));
                    }
                    orgIdSet.addAll(iscmStoreApplyParamOrgMapper.selectByExample(paramOrgExample4).stream().map(IscmStoreApplyParamOrg::getOrgId).collect(Collectors.toSet()));
                    IscmStoreApplyParamGoodsUpperLimitItemOrgExample upperLimitItemOrgExample = new IscmStoreApplyParamGoodsUpperLimitItemOrgExample();
                    upperLimitItemOrgExample.createCriteria().andParentOrgIdEqualTo(orgId).andParamCodeEqualTo(ApplyParamCodeEnum.GOODS_UPPER_LIMIT_ITEM.getCode()).andParamScopeEqualTo(scopeEnum.getCode());
                    orgIdSet.addAll(iscmStoreApplyParamGoodsUpperLimitItemOrgMapper.selectByExample(upperLimitItemOrgExample).stream().map(IscmStoreApplyParamGoodsUpperLimitItemOrg::getOrgId).collect(Collectors.toSet()));
                    return Lists.newArrayList(orgIdSet);
                case APPLY_GOODS_AUTO_FLOAT:
                    IscmStoreApplyAutoFloatExample floatExample = new IscmStoreApplyAutoFloatExample();
                    floatExample.createCriteria().andParentOrgIdEqualTo(orgId).andParamCodeEqualTo(ApplyParamCodeEnum.STORE_AUTO_APPLY_FLOAT.getCode());
                    orgIdSet.addAll(iscmStoreApplyAutoFloatMapper.selectByExample(floatExample).stream().map(IscmStoreApplyAutoFloat::getOrgId).collect(Collectors.toSet()));
                    return Lists.newArrayList(orgIdSet);
                case APPLY_GOODS_STOCK_LIMIT:
                    IscmStoreApplyParamGoodsCatalogLimitOrgExample catalogLimitOrgExample = new IscmStoreApplyParamGoodsCatalogLimitOrgExample();
                    catalogLimitOrgExample.createCriteria().andParentOrgIdEqualTo(orgId).andParamCodeEqualTo(ApplyParamCodeEnum.STORE_AUTO_APPLY_FLOAT.getCode());
                    orgIdSet.addAll(iscmStoreApplyParamGoodsCatalogLimitOrgMapper.selectByExample(catalogLimitOrgExample).stream().map(IscmStoreApplyParamGoodsCatalogLimitOrg::getOrgId).collect(Collectors.toSet()));
                    return Lists.newArrayList(orgIdSet);
                case APPLY_MIDDLE_CATEGORY:
                    IscmStoreApplyParamOrgExample paramOrgExample5 = new IscmStoreApplyParamOrgExample();
                    paramOrgExample5.createCriteria().andParentOrgIdEqualTo(orgId).andParamCodeIn(Lists.newArrayList(ApplyParamCodeEnum.ZY_MIDDLE_CATEGORY.getCode(), ApplyParamCodeEnum.JM_MIDDLE_CATEGORY.getCode()));
                    orgIdSet.addAll(iscmStoreApplyParamOrgMapper.selectByExample(paramOrgExample5).stream().map(IscmStoreApplyParamOrg::getOrgId).collect(Collectors.toSet()));
                    return Lists.newArrayList(orgIdSet);
                default:
                    throw new BusinessErrorException("非法的参数级别");
            }
        } catch (BusinessErrorException e) {
            logger.warn("获取不可下发组织失败", e);
            throw e;
        } catch (Exception e) {
            logger.error("获取不可下发组织失败", e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pushParam(PushParamParam param, TokenUserDTO userDTO) throws Exception {
        try {
            Optional.ofNullable(param.getSourceOrgId()).orElseThrow(() -> new BusinessErrorException("来源机构不能为空"));
            if (CollectionUtils.isEmpty(param.getOrgIds())) {
                throw new BusinessErrorException("请选择下发机构");
            }
            OrgVO orgVO = permissionService.getOrgInfoById(param.getSourceOrgId());
            if (!(orgVO.getType().equals(OrgTypeEnum.ORG_TYPE_COMPANY.getCode()) || orgVO.getType().equals(OrgTypeEnum.ORG_TYPE_REGIONAL_PLATFORM.getCode()))) {
                throw new BusinessErrorException("下发操作仅支持平台或总部");
            }
            Integer orgType = orgVO.getType().equals(OrgTypeEnum.ORG_TYPE_COMPANY.getCode()) ? OrgTypeEnum.ORG_TYPE_REGIONAL_PLATFORM.getCode() : OrgTypeEnum.ORG_TYPE_BUSINESS.getCode();
            List<OrgTreeVO> userDataChildOrgList = permissionService.listUserDataScopeTreesByTypes(userDTO.getUserId(), null, Lists.newArrayList(orgType), orgType);
            Map<Long, OrgTreeVO> userDataOrgMap = userDataChildOrgList.stream().filter(v -> param.getOrgIds().contains(v.getId())).collect(Collectors.toMap(OrgTreeVO::getId, Function.identity(), (k1, k2) -> k1));
            if (MapUtils.isEmpty(userDataOrgMap)) {
                throw new BusinessErrorException("选择的下发机构不在数据权限范围内");
            }
            ApplyParamTypeEnum paramTypeEnum = Optional.ofNullable(ApplyParamTypeEnum.getEnumByCode(param.getParamType())).orElseThrow(() -> new BusinessErrorException("非法的参数类型"));
            deleteParamByType(Lists.newArrayList(userDataOrgMap.keySet()), paramTypeEnum, YNEnum.NO.getType(), param.getParamScope(), param.getSourceOrgId(), userDTO);

            storeApplyParamPushService.pushParams(userDTO, param.getSourceOrgId(), orgVO.getType(), paramTypeEnum.getCode(), param.getParamScope());

            //下发海典
            List<EmployeeInfoVO> employeeInfoVOS = permissionService.getEmployeeInfoByUserIdList(Lists.newArrayList(userDTO.getUserId()));
            EmployeeInfoVO employeeInfoVO = CollectionUtils.isNotEmpty(employeeInfoVOS) ? employeeInfoVOS.get(0) : new EmployeeInfoVO();
            executor.execute(() -> {
                try {
                    storeApplyParamPushHDService.sendHDNonATMCByPush(orgVO.getId(), employeeInfoVO.getEmpCode());
                } catch (Exception e) {
                    logger.error("不自动请货商品参数下发海典失败: " + e.getMessage(), e);
                }
            });

        } catch (BusinessErrorException e) {
            logger.warn("下发参数失败", e);
            throw e;
        } catch (Exception e) {
            logger.error("下发参数失败", e);
            throw e;
        }
    }

    @Override
    public GoodsLimitImportRes importGoodsLimit(MultipartFile file, TokenUserDTO userDTO) throws Exception {
        try {
            GoodsLimitImportRes res = new GoodsLimitImportRes();
            List<GoodsLimitParamDTO> paramDTOS = new ArrayList<>();
            List<GoodsLimitImportDTO> errorList = new ArrayList<>();
            List<GoodsLimitImportDTO> importDTOS = HutoolUtil.excelToList(file.getInputStream(), GoodsLimitImportDTO.getFieldMap(), GoodsLimitImportDTO.class, 2);
            if (CollectionUtils.isEmpty(importDTOS)) {
                throw new BusinessErrorException("导入文件不能为空");
            }
            Set<String> distrCricleSet = new HashSet<>();
            for (GoodsLimitImportDTO importDTO : importDTOS) {
                int perSize = distrCricleSet.size();
                ApplyParamStoreDistrCircleEnum distrCircleEnum = ApplyParamStoreDistrCircleEnum.getEnumByName(importDTO.getStoreDistrCircleDesc());
                if (Objects.isNull(distrCircleEnum)) {
                    importDTO.setErrorMsg("没有匹配到门店类型/配送周期");
                    errorList.add(importDTO);
                    continue;
                }
                ApplyParamGoodsLevelEnum goodsLevelEnum = ApplyParamGoodsLevelEnum.getEnumByName(importDTO.getGoodsLevelDesc());
                if (Objects.isNull(goodsLevelEnum) /*|| goodsLevelEnum.equals(ApplyParamGoodsLevelEnum.NEW_FIRST) || goodsLevelEnum.equals(ApplyParamGoodsLevelEnum.NEW_GOODS)*/) {
                    importDTO.setErrorMsg("没有匹配到商品等级");
                    errorList.add(importDTO);
                    continue;
                }
                if (StringUtils.isBlank(importDTO.getUpperLimit())) {
                    importDTO.setErrorMsg("上限天数为空");
                    errorList.add(importDTO);
                    continue;
                }
                if (StringUtils.isBlank(importDTO.getLowLimit())) {
                    importDTO.setErrorMsg("下限天数为空");
                    errorList.add(importDTO);
                    continue;
                }
                Integer upperLimit, lowLimit;
                try {
                    upperLimit = Integer.valueOf(importDTO.getUpperLimit());
                } catch (Exception e) {
                    importDTO.setErrorMsg("上限天数不是数字");
                    errorList.add(importDTO);
                    continue;
                }
                try {
                    lowLimit = Integer.valueOf(importDTO.getLowLimit());
                } catch (Exception e) {
                    importDTO.setErrorMsg("下限天数不是数字");
                    errorList.add(importDTO);
                    continue;
                }
                if (upperLimit <= lowLimit) {
                    importDTO.setErrorMsg("上限天数需大于下限天数");
                    errorList.add(importDTO);
                    continue;
                }
                distrCricleSet.add(distrCircleEnum.getCode() + "-" + goodsLevelEnum.getCode());
                if (perSize == distrCricleSet.size()) {
                    importDTO.setErrorMsg(distrCircleEnum.getName() + "-" + goodsLevelEnum.getName() + "重复");
                    errorList.add(importDTO);
                    continue;
                }

                GoodsLimitParamDTO paramDTO = new GoodsLimitParamDTO();
                paramDTO.setStoreDistrCircle(distrCircleEnum.getCode());
                paramDTO.setStoreDistrCircleDesc(distrCircleEnum.getName());
                paramDTO.setGoodsLevel(goodsLevelEnum.getCode());
                paramDTO.setGoodsLevelDesc(goodsLevelEnum.getName());
                paramDTO.setUpperLimit(upperLimit);
                paramDTO.setLowLimit(lowLimit);
                paramDTOS.add(paramDTO);
            }
            res.setErrorList(errorList);
            res.setSuccessList(paramDTOS.stream().sorted(Comparator.comparing(v -> ApplyParamStoreDistrCircleEnum.getEnumByCode(v.getStoreDistrCircle()).ordinal() + ApplyParamGoodsLevelEnum.getEnumByCode(v.getGoodsLevel()).ordinal())).collect(Collectors.toList()));
            res.setSuccessNums(paramDTOS.size());
            res.setErrorNums(errorList.size());
            return res;
        } catch (BusinessErrorException e) {
            logger.warn("导入商品等级上下限天数失败", e);
            throw e;
        } catch (Exception e) {
            logger.error("导入商品等级上下限天数失败", e);
            throw e;
        }
    }

    @Override
    public void batchDelGoodsLimit(List<Long> idList, Long orgId, TokenUserDTO userDTO) throws Exception {
        try {
            storeApplyParamOrgService.checkUserAllPermByOrgId(userDTO.getUserId(), orgId);
            IscmStoreApplyParamGoodsLimitOrgExample goodsLimitOrgExample = new IscmStoreApplyParamGoodsLimitOrgExample();
            goodsLimitOrgExample.createCriteria().andIdIn(idList);
            iscmStoreApplyParamGoodsLimitOrgMapper.deleteByExample(goodsLimitOrgExample);
        } catch (Exception e) {
            logger.warn("删除商品等级上下限天数失败", e);
            throw e;
        }
    }

    @Override
    public void exportStoreGroup(TokenUserDTO userDTO, List<Long> groupIds) {
        try {
            String fileName = DateUtils.conventDateStrByDate(new Date(), DateUtils.DATETIME_PATTERN_COMPACT) + ".xls";
            TagPageParam tagParam = new TagPageParam();
            tagParam.setBizType(tagBizType);
            if (CollectionUtils.isNotEmpty(groupIds)) {
                tagParam.setTagIdList(groupIds);
            }
            Map<Long, TagDto> tagMap = tagService.queryByPage(tagParam).stream().collect(Collectors.toMap(TagDto::getId, Function.identity(), (k1,k2) -> k1));
            logger.info("tagMap:{}", JSON.toJSONString(tagMap));
            if (MapUtils.isEmpty(tagMap)) {
                throw new BusinessErrorException("没有需要导出的数据");
            }
            if (CollectionUtils.isEmpty(groupIds)) {
                groupIds.addAll(tagMap.keySet());
            }
            asyncExportFileService.asyncExportToCos(fileName, AsyncExportActionEnum.STORE_GROUP_LIST, userDTO, new HandlerDataExportService() {
                @Override
                public List getDataToExport() {
                    return null;
                }
                @Override
                public List getDataToExport(Integer page, Integer pageSize) {
                    try {
                        logger.info("page:{},groupIds:{}",page,groupIds);
                        if (page >= groupIds.size()) {
                            return Lists.newArrayList();
                        }
                        Long tagId = groupIds.get(page);
                        TagDto tag = tagMap.get(tagId);
                        if (null == tag) {
                            logger.info("标签:{}不存在",tagId);
                            return Lists.newArrayList();
                        }
                        return getStoreEntityIdList(tagId, tag);
                    } catch (Exception e) {
                        logger.warn("getAllotDetailList error", e);
                    }
                    return Lists.newArrayList();
                }
                @Override
                public boolean isPageable() {
                    return true;
                }

                @Override
                public LinkedHashMap<String, String> getFieldMap() {
                    return ExcelDomain.getStoreGroupMap();
                }
            });
        } catch (Exception e) {
            logger.warn("StoreApplyParamServiceImpl.exportStoreGroup error", e);
            throw e;
        }

    }
    private List<StoreGroupExportDTO> getStoreEntityIdList(Long tagId, TagDto tag) throws Exception {
        TagEntityQueryServerParam param = new TagEntityQueryServerParam();
        param.setEntityType(EntityTypeEnum.STORE.getType());
        param.setTagBizType(tagBizType);
        param.setManagerFlag(YNEnum.YES.getType());
        param.setResultType(ResultTypeEnums.ENTITYCODE.getType());
        param.setTagId(tagId);
        param.setInternal(true);
        List<String> entityCode = tagService.tagEntityCodeListQuery(param);
        if (CollectionUtils.isEmpty(entityCode)) {
            return new ArrayList<>();
        }
        Map<Long, List<OrgDTO>> orgMap = permissionService.listDirectParentOrgByOrgIdBatch(entityCode.stream().map(Long::valueOf).collect(Collectors.toList()));
        return entityCode.stream().map(v -> {
            List<OrgDTO> store = orgMap.get(Long.valueOf(v));
            StoreGroupExportDTO exportDTO = new StoreGroupExportDTO();
            exportDTO.setTitle(tag.getTitle());
            exportDTO.setTagId(tagId);
            exportDTO.setCreator(tag.getCreator());
            exportDTO.setGmtCreate(tag.getGmtCreate());
            if (CollectionUtils.isNotEmpty(store)) {
                for (OrgDTO orgDTO : store) {
                    if (OrgTypeEnum.ORG_TYPE_STORE.getCode() == orgDTO.getType().intValue()) {
                        exportDTO.setStoreOrgId(orgDTO.getId());
                        exportDTO.setStoreCode(orgDTO.getSapcode());
                        exportDTO.setStoreName(StringUtils.isNotBlank(orgDTO.getShortName()) ? orgDTO.getShortName() : orgDTO.getName());
                    } else if (OrgTypeEnum.ORG_TYPE_BUSINESS.getCode() == orgDTO.getType().intValue()) {
                        exportDTO.setCompanyName(StringUtils.isNotBlank(orgDTO.getShortName()) ? orgDTO.getShortName() : orgDTO.getName());
                    } else if (OrgTypeEnum.ORG_TYPE_REGIONAL_PLATFORM.getCode() == orgDTO.getType().intValue()) {
                        exportDTO.setPlatformName(StringUtils.isNotBlank(orgDTO.getShortName()) ? orgDTO.getShortName() : orgDTO.getName());
                    }
                }
            }
            return exportDTO;
        }).collect(Collectors.toList());
    }
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void pushApplyParamOrg(List<String> paramCodes, PushParamParam param, OrgVO orgVO, Map<Long, OrgTreeVO> userDataOrgMap) {
        IscmStoreApplyParamOrgExample paramOrgExample = new IscmStoreApplyParamOrgExample();
        paramOrgExample.createCriteria().andOrgIdEqualTo(param.getSourceOrgId()).andParamCodeIn(paramCodes);
        List<IscmStoreApplyParamOrg> paramOrgs = iscmStoreApplyParamOrgMapper.selectByExample(paramOrgExample);
        if (CollectionUtils.isNotEmpty(paramOrgs)) {
            List<IscmStoreApplyParamOrg> willPush = new ArrayList<>();
            userDataOrgMap.forEach((k, v) -> {
                willPush.addAll(paramOrgs.stream().map(p -> {
                    p.setOrgId(v.getId());
                    p.setOrgName(v.getShortName());
                    p.setSapCode(v.getSapcode());
                    p.setParentOrgId(orgVO.getId());
                    p.setParentOrgName(orgVO.getShortName());
                    p.setInheritType(ApplyParamInheritTypeEnum.YES.getCode());
                    return p;
                }).collect(Collectors.toList()));
            });
            iscmStoreApplyParamOrgExtendMapper.batchInsert(willPush);
        }
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void saveAutoApply(List<AutoApplyParamDTO> autoApplyParamDTOList, TokenUserDTO userDTO, OrgSimpleDTO orgTreeVO, List<OrgDTO> parents, Long orgId) {
        List<IscmStoreApplyParamOrg> paramOrgs = new ArrayList<>();
        List<String> paramCodes = new ArrayList<>();
        mark:
        for (AutoApplyParamDTO paramDTO : autoApplyParamDTOList) {
            ApplyParamCodeEnum paramCodeEnum = Optional.ofNullable(ApplyParamCodeEnum.getEnumByCode(paramDTO.getParamCode())).orElseThrow(() -> new BusinessErrorException("参数编码不能为空"));
            if (StringUtils.isBlank(paramDTO.getParamValue())) {
                IscmStoreApplyParamOrgExample orgExample = new IscmStoreApplyParamOrgExample();
                orgExample.createCriteria().andOrgIdEqualTo(Objects.nonNull(orgTreeVO) ? orgTreeVO.getId() : orgId).andParamCodeEqualTo(paramCodeEnum.getCode());
                iscmStoreApplyParamOrgMapper.deleteByExample(orgExample);
            }
            IscmStoreApplyParamOrg paramOrg = new IscmStoreApplyParamOrg();
            paramOrg.setParamCode(paramCodeEnum.getCode());
            paramOrg.setParamName(paramCodeEnum.getName());
            paramOrg.setParamLevel(Objects.nonNull(orgTreeVO) ? orgTreeVO.getType() : OrgTypeEnum.ORG_TYPE_STORE_GROUP.getCode());
            paramOrg.setOrgId(Objects.nonNull(orgTreeVO) ? orgTreeVO.getId() : orgId);
            paramOrg.setSapCode(Objects.nonNull(orgTreeVO) ? orgTreeVO.getSapcode() : "");
            paramOrg.setOrgName(Objects.nonNull(orgTreeVO) ? orgTreeVO.getShortName() : "门店组");
            if (Objects.nonNull(orgTreeVO) && OrgTypeEnum.ORG_TYPE_COMPANY.getCode() < orgTreeVO.getType()) {
                if (CollectionUtils.isNotEmpty(parents) && orgTreeVO.getType() == OrgTypeEnum.ORG_TYPE_BUSINESS.getCode()) {
                    OrgDTO parent = parents.stream().filter(p -> OrgTypeEnum.ORG_TYPE_REGIONAL_PLATFORM.getCode() == p.getType()).findAny().orElseThrow(() -> new BusinessErrorException("没有找到父级平台"));
                    paramOrg.setParentOrgId(parent.getId());
                    paramOrg.setParentOrgName(parent.getShortName());
                } else {
                    OrgDTO parent = parents.stream().filter(p -> OrgTypeEnum.ORG_TYPE_COMPANY.getCode() == p.getType()).findAny().orElseThrow(() -> new BusinessErrorException("没有找到父级信息"));
                    paramOrg.setParentOrgId(parent.getId());
                    paramOrg.setParentOrgName(parent.getName());
                }
            }
            paramOrg.setInheritType(ApplyParamInheritTypeEnum.NO.getCode());
            switch (paramCodeEnum) {
                case COMBINED_CODE_RANGE: {
                    // 为空默认为仅直营
                    CombinedCodeRangeEnum combinedCodeRangeEnum = StringUtils.isBlank(paramDTO.getParamValue()) ? CombinedCodeRangeEnum.ONLY_DIRECT : Optional.ofNullable(CombinedCodeRangeEnum.getEnumByCode(paramDTO.getParamValue())).orElseThrow(() -> new BusinessErrorException(paramCodeEnum.getName() + "-非法的选项"));
                    paramOrg.setParamValue(paramDTO.getParamValue());
                    break;
                }
                case ON_WAY_STOCK_CALCULATION: {
                    if (StringUtils.isBlank(paramDTO.getParamValue())) {
                        // 为空直接删除
                        IscmStoreApplyParamOrgExample orgExample = new IscmStoreApplyParamOrgExample();
                        orgExample.createCriteria().andOrgIdEqualTo(Objects.nonNull(orgTreeVO) ? orgTreeVO.getId() : orgId).andParamCodeEqualTo(paramCodeEnum.getCode());
                        iscmStoreApplyParamOrgMapper.deleteByExample(orgExample);
                        break mark;
                    }
                    ArrayList<String> paramValues = Lists.newArrayList(StringUtils.split(paramDTO.getParamValue(), ","));
                    for (String paramValue : paramValues) {
                        if (Objects.isNull(ApplyParamOnWayStockEnum.getEnumByCode(paramValue))) {
                            throw new BusinessErrorException(paramCodeEnum.getName() + "-非法的选项");
                        }
                    }
                    paramOrg.setParamValue(paramDTO.getParamValue());
                    break;
                }
                case UNABLE_STOCK_CALCULATION: {
                    if (StringUtils.isBlank(paramDTO.getParamValue())) {
                        // 为空直接删除
                        IscmStoreApplyParamOrgExample orgExample = new IscmStoreApplyParamOrgExample();
                        orgExample.createCriteria().andOrgIdEqualTo(Objects.nonNull(orgTreeVO) ? orgTreeVO.getId() : orgId).andParamCodeEqualTo(paramCodeEnum.getCode());
                        iscmStoreApplyParamOrgMapper.deleteByExample(orgExample);
                        break mark;
                    }
                    ArrayList<String> paramValues = Lists.newArrayList(StringUtils.split(paramDTO.getParamValue(), ","));
                    for (String paramValue : paramValues) {
                        if (Objects.isNull(ApplyParamUnableStockEnum.getEnumByCode(paramValue))) {
                            throw new BusinessErrorException(paramCodeEnum.getName() + "-非法的选项");
                        }
                    }
                    paramOrg.setParamValue(paramDTO.getParamValue());
                    break;
                }
                case SPECIAL_CTRL_GOODS_SWITCH: {
                    if ("true".equals(paramDTO.getParamValue()) || "false".equals(paramDTO.getParamValue())) {
                        paramOrg.setParamValue(paramDTO.getParamValue());
                    } else {
                        throw new BusinessErrorException(paramCodeEnum.getName() + "参数值非法");
                    }
                    break;
                }
                case SPECIAL_GOODS_ONCE_APPLY_LIMIT:
                case SPECIAL_GOODS_PER_THIRTY_DAYS_APPLY_LIMIT:
                    try {
                        Integer.valueOf(paramDTO.getParamValue());
                        paramOrg.setParamValue(paramDTO.getParamValue());
                    } catch (NumberFormatException e) {
                        throw new BusinessErrorException(paramCodeEnum.getName() + "参数值非法");
                    }
                    break;
                case STORE_AUTO_APPLY_TIME:
                    Optional.ofNullable(DateUtils.parseDate(paramDTO.getParamValue(), DateUtils.DATE_HHMM_PATTERN)).orElseThrow(() -> new BusinessErrorException("时间格式错误,请传入HH:mm格式的时间"));
                    paramOrg.setParamValue(paramDTO.getParamValue());
                    break;
                case MIDDLE_DEAL_SWITCH:
                    if (paramDTO.getParamValue().equals("true") || paramDTO.getParamValue().equals("false")) {
                        paramOrg.setParamValue(paramDTO.getParamValue());
                        if (paramDTO.getParamValue().equals("false")) {
                            // 如果是否 则要删除余数
                            IscmStoreApplyParamOrgExample orgExample = new IscmStoreApplyParamOrgExample();
                            orgExample.createCriteria().andOrgIdEqualTo(Objects.nonNull(orgTreeVO) ? orgTreeVO.getId() : orgId).andParamCodeEqualTo(ApplyParamCodeEnum.REMAINDER_DEAL.getCode());
                            iscmStoreApplyParamOrgMapper.deleteByExample(orgExample);
                        }
                    } else {
                        throw new BusinessErrorException(paramCodeEnum.getName() + "参数值非法");
                    }
                    break;
                case REMAINDER_DEAL:
                    if (Arrays.stream(ApplyParamRemainderDealEnum.values()).map(ApplyParamRemainderDealEnum::getCode).collect(Collectors.toList()).contains(paramDTO.getParamValue())) {
                        paramOrg.setParamValue(paramDTO.getParamValue());
                    } else {
                        try {
                            BigDecimal decimal = new BigDecimal(paramDTO.getParamValue());
                            if (decimal.scale() > 1 || !(decimal.compareTo(BigDecimal.ZERO) == 1 && decimal.compareTo(BigDecimal.ONE) == -1)) {
                                throw new BusinessErrorException("仅支持一位且大于0小于1的小数");
                            }
                            paramOrg.setParamValue(decimal.stripTrailingZeros().toPlainString());
                        } catch (Exception e) {
                            throw new BusinessErrorException("自定义系数请输入一位且大于0小于1的小数");
                        }
                    }
                    break;
                case ZY_MIDDLE_CATEGORY:
                case JM_MIDDLE_CATEGORY:
                    paramOrg.setParamValue(paramDTO.getParamValue());
                    break;
                default:
                    throw new BusinessErrorException("非法的参数类型");

            }
            paramOrg.setCreatedBy(userDTO.getUserId());
            paramOrg.setCreatedName(userDTO.getName());
            paramOrg.setUpdatedBy(userDTO.getUserId());
            paramOrg.setUpdatedName(userDTO.getName());
            paramOrgs.add(paramOrg);
            paramCodes.add(paramCodeEnum.getCode());
        }
        if (CollectionUtils.isNotEmpty(paramOrgs)) {
            // 先删除
            IscmStoreApplyParamOrgExample orgExample = new IscmStoreApplyParamOrgExample();
            orgExample.createCriteria().andOrgIdEqualTo(Objects.nonNull(orgTreeVO) ? orgTreeVO.getId() : orgId).andParamCodeIn(paramCodes);
            iscmStoreApplyParamOrgMapper.deleteByExample(orgExample);
            iscmStoreApplyParamOrgExtendMapper.batchInsert(paramOrgs);
        }
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void saveGoodsLimit(List<GoodsLimitParamDTO> goodsLimitParamDTOS, TokenUserDTO userDTO, OrgSimpleDTO orgTreeVO, List<OrgDTO> parents, Long orgId) {
        if (CollectionUtils.isEmpty(goodsLimitParamDTOS)) {
            IscmStoreApplyParamGoodsLimitOrgExample limitOrgExample = new IscmStoreApplyParamGoodsLimitOrgExample();
            limitOrgExample.createCriteria().andOrgIdEqualTo(Objects.nonNull(orgTreeVO) ? orgTreeVO.getId() : orgId);
            iscmStoreApplyParamGoodsLimitOrgMapper.deleteByExample(limitOrgExample);
            return;
        }
        Set<String> distrCricleSet = new HashSet<>();
        List<IscmStoreApplyParamGoodsLimitOrg> goodsLimitOrgs = new ArrayList<>();
        for (GoodsLimitParamDTO paramDTO : goodsLimitParamDTOS) {
            int perSize = distrCricleSet.size();
            ApplyParamStoreDistrCircleEnum applyParamStoreDistrCircleEnum = Optional.ofNullable(ApplyParamStoreDistrCircleEnum.getEnumByCode(paramDTO.getStoreDistrCircle())).orElseThrow(() -> new BusinessErrorException("非法的门店类型/配送周期"));
            ApplyParamGoodsLevelEnum goodsLevelEnum = Optional.ofNullable(ApplyParamGoodsLevelEnum.getEnumByCode(paramDTO.getGoodsLevel())).orElseThrow(() -> new BusinessErrorException("商品级别不能为空"));
            distrCricleSet.add(applyParamStoreDistrCircleEnum.getCode() + "-" + goodsLevelEnum.getCode());
            if (perSize == distrCricleSet.size()) {
                throw new BusinessErrorException(applyParamStoreDistrCircleEnum.getName() + "-" + goodsLevelEnum.getName() + "重复");
            }
            Integer upperLimit = Optional.ofNullable(paramDTO.getUpperLimit()).orElseThrow(() -> new BusinessErrorException("上限天数不能为空"));
            Integer lowLimit = Optional.ofNullable(paramDTO.getLowLimit()).orElseThrow(() -> new BusinessErrorException("下限天数不能为空"));
            if (upperLimit <= lowLimit) {
                throw new BusinessErrorException("上限天数需大于下限天数");
            }
            IscmStoreApplyParamGoodsLimitOrg goodsLimitOrg = new IscmStoreApplyParamGoodsLimitOrg();
            goodsLimitOrg.setParamCode(ApplyParamCodeEnum.GOODS_LEVEL_LIMIT_DAYS.getCode());
            goodsLimitOrg.setParamName(ApplyParamCodeEnum.GOODS_LEVEL_LIMIT_DAYS.getName());
            goodsLimitOrg.setGoodsLevel(goodsLevelEnum.getCode());
            goodsLimitOrg.setGoodsLevelName(goodsLevelEnum.getName());
            goodsLimitOrg.setParamLevel(Objects.nonNull(orgTreeVO) ? orgTreeVO.getType() : OrgTypeEnum.ORG_TYPE_STORE_GROUP.getCode());
            goodsLimitOrg.setOrgId(Objects.nonNull(orgTreeVO) ? orgTreeVO.getId() : orgId);
            goodsLimitOrg.setSapCode(Objects.nonNull(orgTreeVO) ? orgTreeVO.getSapcode() : "");
            goodsLimitOrg.setOrgName(Objects.nonNull(orgTreeVO) ? orgTreeVO.getShortName() : "门店组");
            if (Objects.nonNull(orgTreeVO) && OrgTypeEnum.ORG_TYPE_COMPANY.getCode() < orgTreeVO.getType()) {
                if (CollectionUtils.isNotEmpty(parents) && orgTreeVO.getType() == OrgTypeEnum.ORG_TYPE_BUSINESS.getCode()) {
                    OrgDTO parent = parents.stream().filter(p -> OrgTypeEnum.ORG_TYPE_REGIONAL_PLATFORM.getCode() == p.getType()).findAny().orElseThrow(() -> new BusinessErrorException("没有找到父级平台"));
                    goodsLimitOrg.setParentOrgId(parent.getId());
                    goodsLimitOrg.setParentOrgName(parent.getShortName());
                } else {
                    OrgDTO parent = parents.stream().filter(p -> OrgTypeEnum.ORG_TYPE_COMPANY.getCode() == p.getType()).findAny().orElseThrow(() -> new BusinessErrorException("没有找到父级信息"));
                    goodsLimitOrg.setParentOrgId(parent.getId());
                    goodsLimitOrg.setParentOrgName(parent.getName());
                }
            }
            goodsLimitOrg.setInheritType(ApplyParamInheritTypeEnum.NO.getCode());
            goodsLimitOrg.setStoreDistrCircle(applyParamStoreDistrCircleEnum.getCode());
            goodsLimitOrg.setUpperLimit(upperLimit);
            goodsLimitOrg.setLowLimit(lowLimit);
            goodsLimitOrg.setCreatedBy(userDTO.getUserId());
            goodsLimitOrg.setCreatedName(userDTO.getName());
            goodsLimitOrg.setUpdatedBy(userDTO.getUserId());
            goodsLimitOrg.setUpdatedName(userDTO.getName());
            goodsLimitOrgs.add(goodsLimitOrg);
        }
        if (CollectionUtils.isNotEmpty(goodsLimitOrgs)) {
            // 先删除
            IscmStoreApplyParamGoodsLimitOrgExample limitOrgExample = new IscmStoreApplyParamGoodsLimitOrgExample();
            limitOrgExample.createCriteria().andOrgIdEqualTo(Objects.nonNull(orgTreeVO) ? orgTreeVO.getId() : orgId);
            iscmStoreApplyParamGoodsLimitOrgMapper.deleteByExample(limitOrgExample);
            iscmStoreApplyParamGoodsLimitOrgExtendMapper.batchInsert(goodsLimitOrgs);
        }
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void saveGoodsLevel(List<GoodsLevelParamDTO> goodsLevelParamDTOS, TokenUserDTO userDTO, OrgSimpleDTO orgTreeVO, List<OrgDTO> parents, Long orgId) throws Exception {
        List<EmployeeInfoVO> employeeInfoVOS = permissionService.getEmployeeInfoByUserIdList(Lists.newArrayList(userDTO.getUserId()));

        if (CollectionUtils.isEmpty(goodsLevelParamDTOS)) {
            IscmStoreApplyParamGoodsLevelOrgExample levelOrgExample = new IscmStoreApplyParamGoodsLevelOrgExample();
            levelOrgExample.createCriteria().andOrgIdEqualTo(Objects.nonNull(orgTreeVO) ? orgTreeVO.getId() : orgId);
           // deletePushHd(userDTO, orgId, employeeInfoVOS);
            iscmStoreApplyParamGoodsLevelOrgMapper.deleteByExample(levelOrgExample);
            return;
        }
        if (goodsLevelParamDTOS.size() > (int) goodsLevelParamDTOS.stream().map(GoodsLevelParamDTO::getGoodsLevel).distinct().count()) {
            throw new BusinessErrorException("商品级别重复");
        }
        if (goodsLevelParamDTOS.size() < ApplyParamGoodsLevelEnum.values().length - 8) {
            throw new BusinessErrorException("请至少传入七级商品级别");
        }
        // 按照一级到新一级排序
        List<GoodsLevelParamDTO> orderDTOS = goodsLevelParamDTOS.stream().sorted(Comparator.comparing(v -> ApplyParamGoodsLevelEnum.getEnumByCode(v.getGoodsLevel()).ordinal())).collect(Collectors.toList());
        ApplyParamGoodsLevelEnum perGoodsLevelEnum = null;
        List<GoodsLevelParamDTO> passList = new ArrayList<>();
        List<GoodsLevelParamDTO> oldList = new ArrayList<>();
        List<GoodsLevelParamDTO> newList = new ArrayList<>();
        for (int i = 0; i < orderDTOS.size(); i++) {
            GoodsLevelParamDTO paramDTO = orderDTOS.get(i);
            ApplyParamGoodsLevelEnum goodsLevelEnum = Optional.ofNullable(ApplyParamGoodsLevelEnum.getEnumByCode(paramDTO.getGoodsLevel())).orElseThrow(() -> new BusinessErrorException("商品级别不能为空"));
            if (goodsLevelEnum.ordinal() >= ApplyParamGoodsLevelEnum.NEW_FIRST.ordinal()) {
                if (Objects.isNull(paramDTO.getStartValue()) || Objects.isNull(paramDTO.getEndValue())) {
                    continue;
                }
            }
            if (goodsLevelEnum.ordinal() < ApplyParamGoodsLevelEnum.NEW_FIRST.ordinal()) {
                oldList.add(paramDTO);
            } else {
                newList.add(paramDTO);
            }
        }
        checkCross(oldList, passList);
        checkCross(newList, passList);
        List<IscmStoreApplyParamGoodsLevelOrg> goodsLevelOrgList = passList.stream().map(v -> {
            IscmStoreApplyParamGoodsLevelOrg levelOrg = new IscmStoreApplyParamGoodsLevelOrg();
            levelOrg.setParamCode(ApplyParamCodeEnum.GOODS_LEVEL.getCode());
            levelOrg.setParamName(ApplyParamCodeEnum.GOODS_LEVEL.getName());
            levelOrg.setOrgId(Objects.nonNull(orgTreeVO) ? orgTreeVO.getId() : orgId);
            levelOrg.setOrgName(Objects.nonNull(orgTreeVO) ? orgTreeVO.getShortName() : "门店组");
            levelOrg.setSapCode(Objects.nonNull(orgTreeVO) ? orgTreeVO.getSapcode() : "");
            levelOrg.setParamLevel(Objects.nonNull(orgTreeVO) ? orgTreeVO.getType() : OrgTypeEnum.ORG_TYPE_STORE_GROUP.getCode());
            levelOrg.setGoodsLevel(v.getGoodsLevel());
            levelOrg.setInheritType(ApplyParamInheritTypeEnum.NO.getCode());
            levelOrg.setStartValue(v.getStartValue());
            levelOrg.setEndValue(v.getEndValue());
            levelOrg.setGoodsLevelName(ApplyParamGoodsLevelEnum.getNameByCode(v.getGoodsLevel()));
            levelOrg.setRemarks(v.getRemarks());
            if (Objects.nonNull(orgTreeVO) && OrgTypeEnum.ORG_TYPE_COMPANY.getCode() < orgTreeVO.getType()) {
                if (CollectionUtils.isNotEmpty(parents) && orgTreeVO.getType() == OrgTypeEnum.ORG_TYPE_BUSINESS.getCode()) {
                    OrgDTO parent = parents.stream().filter(p -> OrgTypeEnum.ORG_TYPE_REGIONAL_PLATFORM.getCode() == p.getType()).findAny().orElseThrow(() -> new BusinessErrorException("没有找到父级平台"));
                    levelOrg.setParentOrgId(parent.getId());
                    levelOrg.setParentOrgName(parent.getShortName());
                } else {
                    OrgDTO parent = parents.stream().filter(p -> OrgTypeEnum.ORG_TYPE_COMPANY.getCode() == p.getType()).findAny().orElseThrow(() -> new BusinessErrorException("没有找到父级信息"));
                    levelOrg.setParentOrgId(parent.getId());
                    levelOrg.setParentOrgName(parent.getName());
                }
            }
            levelOrg.setCreatedBy(userDTO.getUserId());
            levelOrg.setCreatedName(userDTO.getName());
            levelOrg.setUpdatedBy(userDTO.getUserId());
            levelOrg.setUpdatedName(userDTO.getName());
            return levelOrg;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(goodsLevelOrgList)) {
            // 先删除
            IscmStoreApplyParamGoodsLevelOrgExample levelOrgExample = new IscmStoreApplyParamGoodsLevelOrgExample();
            levelOrgExample.createCriteria().andOrgIdEqualTo(Objects.nonNull(orgTreeVO) ? orgTreeVO.getId() : orgId);
            iscmStoreApplyParamGoodsLevelOrgMapper.deleteByExample(levelOrgExample);
            iscmStoreApplyParamGoodsLevelOrgExtendMapper.batchInsert(goodsLevelOrgList);
        }
    }

//    private void deletePushHd(TokenUserDTO userDTO, Long orgId, List<EmployeeInfoVO> employeeInfoVOS) throws Exception {
//        if (orgId != null) {
//            StoreApplyParamDTO autoApplyList = getAutoApplyList(orgId, OrgTypeEnum.ORG_TYPE_STORE_GROUP.getCode(), ApplyParamTypeEnum.AUTO_APPLY.getCode(), userDTO);
//            autoApplyList.setOperationFlag(Constants.DELETE_STATUS);
//            List<GoodsLimitParamDTO> goodsLimitParamDTOS = autoApplyList.getGoodsLimitParamDTOS();
//            for (GoodsLimitParamDTO goodsLimitParamDTO : goodsLimitParamDTOS) {
//                goodsLimitParamDTO.setUpperLimit(null);
//                goodsLimitParamDTO.setLowLimit(null);
//            }
//            storeApplyParamPushService.pushApplyParamGoodsUpperLimitToHd(null, employeeInfoVOS.get(0), autoApplyList.getUpperLimits(),autoApplyList.getGoodsLevelParamDTOS(), orgId,  Constants.DELETE_STATUS);
//        }
//    }

    private void checkCross(List<GoodsLevelParamDTO> orderDTOS, List<GoodsLevelParamDTO> passList) {
        for (int i = 0; i < orderDTOS.size(); i++) {
            GoodsLevelParamDTO paramDTO = orderDTOS.get(i);
            ApplyParamGoodsLevelEnum goodsLevelEnum = ApplyParamGoodsLevelEnum.getEnumByCode(paramDTO.getGoodsLevel());
            int startValue = Optional.ofNullable(paramDTO.getStartValue()).orElseThrow(() -> new BusinessErrorException(goodsLevelEnum.getName() + "开始砝值不能为空"));
            int endValue = Optional.ofNullable(paramDTO.getEndValue()).orElseThrow(() -> new BusinessErrorException(goodsLevelEnum.getName() + "结束砝值不能为空"));
            if (startValue >= endValue) {
                throw new BusinessErrorException(goodsLevelEnum.getName() + "开始砝值需小于结束砝值");
            }
            for (int j = 0; j < i; j++) {
                if (i == 0) {
                    break;
                }
                GoodsLevelParamDTO paramDTO1 = orderDTOS.get(j);
                if (Math.max(startValue, paramDTO1.getStartValue()) < Math.min(endValue, paramDTO1.getEndValue())) {
                    throw new BusinessErrorException(goodsLevelEnum.getName() + "砝值与" + ApplyParamGoodsLevelEnum.getNameByCode(paramDTO1.getGoodsLevel()) + "交叉");
                }
            }
            passList.add(paramDTO);
        }
    }

    private StoreApplyParamDTO getStoreApplyParamDTO(Long orgId) {
        StoreApplyParamDTO paramDTO = new StoreApplyParamDTO();
        IscmStoreApplyParamOrgExample applyParamOrgExample = new IscmStoreApplyParamOrgExample();
        applyParamOrgExample.createCriteria().andOrgIdEqualTo(orgId);
        Map<String, List<AutoApplyParamDTO>> autoMap = iscmStoreApplyParamOrgMapper.selectByExample(applyParamOrgExample).stream().map(v -> {
            AutoApplyParamDTO param = new AutoApplyParamDTO();
            BeanUtils.copyProperties(v, param);
            param.setGmtCreate(DateUtils.conventDateStrByDate(v.getGmtCreate(), DateUtils.DATETIME_PATTERN));
            param.setGmtUpdate(DateUtils.conventDateStrByDate(v.getGmtUpdate(), DateUtils.DATETIME_PATTERN));
            return param;
        }).collect(Collectors.groupingBy(AutoApplyParamDTO::getParamCode));
        paramDTO.setCombinedCodeRange(CollectionUtils.isEmpty(autoMap.get(ApplyParamCodeEnum.COMBINED_CODE_RANGE.getCode())) ? new AutoApplyParamDTO(ApplyParamCodeEnum.COMBINED_CODE_RANGE.getCode(), ApplyParamCodeEnum.COMBINED_CODE_RANGE.getName()) : autoMap.get(ApplyParamCodeEnum.COMBINED_CODE_RANGE.getCode()).get(0));
        paramDTO.setOnwayStockDTO(CollectionUtils.isEmpty(autoMap.get(ApplyParamCodeEnum.ON_WAY_STOCK_CALCULATION.getCode())) ? new AutoApplyParamDTO() : autoMap.get(ApplyParamCodeEnum.ON_WAY_STOCK_CALCULATION.getCode()).get(0));
        paramDTO.setUnAbleStockDTO(CollectionUtils.isEmpty(autoMap.get(ApplyParamCodeEnum.UNABLE_STOCK_CALCULATION.getCode())) ? new AutoApplyParamDTO() : autoMap.get(ApplyParamCodeEnum.UNABLE_STOCK_CALCULATION.getCode()).get(0));
        List<AutoApplyParamDTO> specialParams = new ArrayList<>();
        specialParams.add(CollectionUtils.isEmpty(autoMap.get(ApplyParamCodeEnum.SPECIAL_CTRL_GOODS_SWITCH.getCode())) ? new AutoApplyParamDTO(ApplyParamCodeEnum.SPECIAL_CTRL_GOODS_SWITCH.getCode(), ApplyParamCodeEnum.SPECIAL_CTRL_GOODS_SWITCH.getName()) : autoMap.get(ApplyParamCodeEnum.SPECIAL_CTRL_GOODS_SWITCH.getCode()).get(0));
        specialParams.add(CollectionUtils.isEmpty(autoMap.get(ApplyParamCodeEnum.SPECIAL_GOODS_ONCE_APPLY_LIMIT.getCode())) ? new AutoApplyParamDTO(ApplyParamCodeEnum.SPECIAL_GOODS_ONCE_APPLY_LIMIT.getCode(), ApplyParamCodeEnum.SPECIAL_GOODS_ONCE_APPLY_LIMIT.getName()) : autoMap.get(ApplyParamCodeEnum.SPECIAL_GOODS_ONCE_APPLY_LIMIT.getCode()).get(0));
        specialParams.add(CollectionUtils.isEmpty(autoMap.get(ApplyParamCodeEnum.SPECIAL_GOODS_PER_THIRTY_DAYS_APPLY_LIMIT.getCode())) ? new AutoApplyParamDTO(ApplyParamCodeEnum.SPECIAL_GOODS_PER_THIRTY_DAYS_APPLY_LIMIT.getCode(), ApplyParamCodeEnum.SPECIAL_GOODS_PER_THIRTY_DAYS_APPLY_LIMIT.getName()) : autoMap.get(ApplyParamCodeEnum.SPECIAL_GOODS_PER_THIRTY_DAYS_APPLY_LIMIT.getCode()).get(0));
        paramDTO.setSpecialGoodsCtrlDTOS(specialParams);

        IscmStoreApplyParamGoodsLimitOrgExample goodsLimitOrgExample = new IscmStoreApplyParamGoodsLimitOrgExample();
        goodsLimitOrgExample.createCriteria().andOrgIdEqualTo(orgId);
        paramDTO.setGoodsLimitParamDTOS(iscmStoreApplyParamGoodsLimitOrgMapper.selectByExample(goodsLimitOrgExample).stream().map(v -> {
            GoodsLimitParamDTO param = new GoodsLimitParamDTO();
            BeanUtils.copyProperties(v, param);
            param.setId(v.getId());
            param.setStoreDistrCircleDesc(ApplyParamStoreDistrCircleEnum.getNameByCode(v.getStoreDistrCircle()));
            param.setGoodsLevelDesc(ApplyParamGoodsLevelEnum.getNameByCode(v.getGoodsLevel()));
            param.setGmtCreate(DateUtils.conventDateStrByDate(v.getGmtCreate(), DateUtils.DATETIME_PATTERN));
            param.setGmtUpdate(DateUtils.conventDateStrByDate(v.getGmtUpdate(), DateUtils.DATETIME_PATTERN));
            param.setGoodsLevelOrder(ApplyParamGoodsLevelEnum.getEnumByCode(v.getGoodsLevel()).ordinal());
            param.setStoreDistrCircleOrder(ApplyParamStoreDistrCircleEnum.getEnumByCode(v.getStoreDistrCircle()).ordinal());
            return param;
        }).sorted(Comparator.comparing(GoodsLimitParamDTO::getStoreDistrCircleOrder).thenComparing(GoodsLimitParamDTO::getGoodsLevelOrder)).collect(Collectors.toList()));
        IscmStoreApplyParamGoodsLevelOrgExample goodsLevelExample = new IscmStoreApplyParamGoodsLevelOrgExample();
        goodsLevelExample.createCriteria().andOrgIdEqualTo(orgId);
        logger.debug("getStoreApplyParamDTO.orgId : " + orgId);
        Map<Byte, GoodsLevelParamDTO> levelParamMap = iscmStoreApplyParamGoodsLevelOrgMapper.selectByExample(goodsLevelExample).stream().map(v -> {
            GoodsLevelParamDTO param = new GoodsLevelParamDTO();
            BeanUtils.copyProperties(v, param);
            param.setGoodsLevelDesc(ApplyParamGoodsLevelEnum.getNameByCode(v.getGoodsLevel()));
            param.setGmtCreate(DateUtils.conventDateStrByDate(v.getGmtCreate(), DateUtils.DATETIME_PATTERN));
            param.setGmtUpdate(DateUtils.conventDateStrByDate(v.getGmtUpdate(), DateUtils.DATETIME_PATTERN));
            return param;
        }).collect(Collectors.toMap(GoodsLevelParamDTO::getGoodsLevel, Function.identity(), (k1, k2) -> k1));
        paramDTO.setGoodsLevelParamDTOS(Arrays.stream(ApplyParamGoodsLevelEnum.values()).map(v -> {
            GoodsLevelParamDTO level = levelParamMap.get(v.getCode());
            if (Objects.nonNull(level)) {
                return level;
            }
            level = new GoodsLevelParamDTO();
            level.setGoodsLevel(v.getCode());
            level.setGoodsLevelDesc(v.getName());
            return level;
        }).collect(Collectors.toList()));
        return paramDTO;
    }
}

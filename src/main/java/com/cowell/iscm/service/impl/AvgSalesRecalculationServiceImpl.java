package com.cowell.iscm.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.iscm.config.Constants;
import com.cowell.iscm.config.IdGenConfig;
import com.cowell.iscm.entity.BdpAvgDailySales;
import com.cowell.iscm.entity.BdpAvgDailySalesExample;
import com.cowell.iscm.entity.IscmRecalculationRecord;
import com.cowell.iscm.entity.IscmStoreApplyParamGoodsOrgValidBlacklistExample;
import com.cowell.iscm.entityTidb.*;
import com.cowell.iscm.enums.*;
import com.cowell.iscm.mapper.BdpAvgDailySalesMapper;
import com.cowell.iscm.mapper.IscmStoreApplyParamGoodsOrgValidBlacklistMapper;
import com.cowell.iscm.mapper.IscmStoreApplyParamOrgMapper;
import com.cowell.iscm.mapper.extend.BdpAvgDailySalesExtendMapper;
import com.cowell.iscm.mapper.extend.IscmRecalculationRecordExtendMapper;
import com.cowell.iscm.mapper.extend.IscmStoreApplyParamGoodsOrgValidBlacklistExtendMapper;
import com.cowell.iscm.mapper.extend.IscmStoreApplyParamGoodsOrgValidWhitelistExtendMapper;
import com.cowell.iscm.mapperTidb.BdpPushAvgDailySalesMapper;
import com.cowell.iscm.mapperTidb.IscmApplyGoodsReplaceCodeMapper;
import com.cowell.iscm.mapperTidb.StoreApplyDateBakMapper;
import com.cowell.iscm.mapperTidb.StoreApplyDateMapper;
import com.cowell.iscm.mapperTidb.extend.BdpPushAvgDailySalesExtendMapper;
import com.cowell.iscm.mapperTidb.extend.StoreApplyDateBakExtendMapper;
import com.cowell.iscm.mapperTidb.extend.StoreApplyDateExtendMapper;
import com.cowell.iscm.mq.producer.DealBdpAvgSalesProducer;
import com.cowell.iscm.mq.producer.ManualApplyProducer;
import com.cowell.iscm.rest.errors.AmisBadRequestException;
import com.cowell.iscm.rest.errors.BusinessErrorException;
import com.cowell.iscm.rest.errors.ErrorCodeEnum;
import com.cowell.iscm.service.*;
import com.cowell.iscm.service.dto.AvgSalesQueryDTO;
import com.cowell.iscm.service.dto.BdpAvgDailySalesDTO;
import com.cowell.iscm.service.dto.ProcessResponse;
import com.cowell.iscm.service.dto.StoreApplyInfoDTO;
import com.cowell.iscm.service.dto.applyParam.*;
import com.cowell.iscm.service.dto.storeAutonomyAllot.ProcessProgressDTO;
import com.cowell.iscm.service.feign.*;
import com.cowell.iscm.service.feign.dto.MdmStoreBaseDTO;
import com.cowell.iscm.service.feign.dto.StockGoodsBatchCodeSimpleInfo;
import com.cowell.iscm.service.feign.dto.StockGoodsPagableQueryParam;
import com.cowell.iscm.service.feign.response.PageResponse;
import com.cowell.iscm.utils.BeanUtils;
import com.cowell.iscm.utils.DateUtils;
import com.cowell.permission.dto.ChildOrgsDTO;
import com.cowell.permission.dto.OrgDTO;
import com.cowell.permission.vo.OrgVO;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class AvgSalesRecalculationServiceImpl implements AvgSalesRecalculationService {

    private final Logger logger = LoggerFactory.getLogger(AvgSalesRecalculationServiceImpl.class);

    @Resource
    private BdpAvgDailySalesMapper bdpAvgDailySalesMapper;

    @Resource
    private BdpAvgDailySalesExtendMapper bdpAvgDailySalesExtendMapper;

    @Resource
    private StoreApplyDateMapper storeApplyDateMapper;

    @Resource
    private StoreApplyDateExtendMapper storeApplyDateExtendMapper;

    @Resource
    private PermissionService permissionService;

    @Resource
    private TagGoodsService tagGoodsService;

    @Resource
    private TagService tagService;

    @Resource
    private IscmStoreApplyParamOrgMapper iscmStoreApplyParamOrgMapper;

    @Resource
    private StoreApplyParamNonAutomaticService storeApplyParamNonAutomaticService;

    @Resource
    private IscmStoreApplyParamGoodsOrgValidBlacklistMapper iscmStoreApplyParamGoodsOrgValidBlacklistMapper;

    @Resource
    private IscmStoreApplyParamGoodsOrgValidBlacklistExtendMapper iscmStoreApplyParamGoodsOrgValidBlacklistExtendMapper;

    @Resource
    private IscmApplyGoodsReplaceCodeMapper iscmApplyGoodsReplaceCodeMapper;

    @Resource
    private StoreApplyDateBakMapper storeApplyDateBakMapper;

    @Resource
    private StoreApplyDateBakExtendMapper storeApplyDateBakExtendMapper;

    @Resource
    private RetryService retryService;

    @Resource
    private StoreApplyParamService storeApplyParamService;

    @Resource
    private IscmRecalculationRecordExtendMapper iscmRecalculationRecordExtendMapper;

    @Resource
    private RecalculationStoreApplyService recalculationStoreApplyService;

    @Resource
    private BdpPushAvgDailySalesMapper bdpPushAvgDailySalesMapper;

    @Resource
    private BdpPushAvgDailySalesExtendMapper bdpPushAvgDailySalesExtendMapper;

    @Resource
    private IscmStoreApplyParamGoodsOrgValidWhitelistExtendMapper iscmStoreApplyParamGoodsOrgValidWhitelistExtendMapper;
    @Resource
    private StoreService storeService;
    @Resource
    private HealthcareBizCpService healthcareBizCpService;

    @Resource
    private DealBdpAvgSalesProducer dealBdpAvgSalesProducer;
    @Resource
    private ManualApplyProducer manualApplyProducer;
    /**
     * 复算推送海典开关 true or false
     */
    @Value("${iscm.recalculation.push.hd.switch:true}")
    private Boolean pushHdSwitch;

    /**
     * 复算拆单企业列表 ,分割
     */
    @Value("${iscm.recalculation.split.companys:}")
    private String splitCompanys;
    /**
     * 加盟店请货下发条数
     */
    @Value("${iscm.recalculation.jm.apply.max:500}")
    private Integer jmApplyMax;

    @Resource
    private TocService tocService;

    @Autowired
    @Qualifier("taskExecutor")
    private AsyncTaskExecutor executor;
    @Autowired
    private RedissonClient redissonClient;
    @Override
    public void avgRecalculationByCompany(String companyCode) throws Exception {
        try {
            logger.info("日均销复算企业:{}复算开始", companyCode);
            StoreApplyDateExample example = new StoreApplyDateExample();
            StoreApplyDateExample.Criteria criteria = example.createCriteria();
            criteria.andCompanyCodeEqualTo(companyCode).andApplyDateEqualTo(new Date());
//            long count = storeApplyDateMapper.countByExample(example);
            criteria.andStoreStatusEqualTo("营业");
            OrgVO company = permissionService.queryOrgInfoBySapCodes(Lists.newArrayList(companyCode), OrgTypeEnum.ORG_TYPE_BUSINESS.getCode()).stream().findFirst().orElseThrow(() -> new BusinessErrorException("日均销复算企业:" + companyCode + "没有查询到企业信息"));
            List<ChildOrgsDTO> orgsDTOS = permissionService.listChildOrgAssignedType(Lists.newArrayList(company.getId()), OrgTypeEnum.ORG_TYPE_STORE.getCode());
            if (CollectionUtils.isEmpty(orgsDTOS)) {
                throw new BusinessErrorException("没有查询到企业下的门店2");
            }
            ChildOrgsDTO childOrgsDTO = orgsDTOS.stream().filter(v -> company.getId().equals(v.getId())).findAny().orElse(null);
            if (null == childOrgsDTO || CollectionUtils.isEmpty(childOrgsDTO.getChildren()) || CollectionUtils.isEmpty(childOrgsDTO.getChildren().stream().map(OrgDTO::getOutId).filter(Objects::nonNull).collect(Collectors.toList()))) {
                throw new BusinessErrorException("没有查询到企业下的门店3");
            }
            List<MdmStoreBaseDTO> stores = storeService.findStoreInfoAndExtendByStoreIds(childOrgsDTO.getChildren().stream().map(OrgDTO::getOutId).filter(Objects::nonNull).collect(Collectors.toList()));
//            List<StoreApplyDate> storeApplyDates = storeApplyDateMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(stores)) {
                logger.info("日均销复算企业:{}今天没有需要请货的门店", companyCode);
                return;
            }
            List<MdmStoreBaseDTO> filteredStores = stores.stream().filter(v -> {
                JSONObject extend = JSONObject.parseObject(v.getExtend());
                return Objects.nonNull(extend)
                        && extend.containsKey("deliveryDate")
                        && extend.getString("deliveryDate").contains(DateUtils.conventDateStrByDate(new Date(), DateUtils.DATE_PATTERN));
            }).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filteredStores)) {
                logger.info("日均销复算企业:{}过滤请货日后今天没有需要请货的门店", companyCode);
                return;
            }
            // 取平台
            List<OrgDTO> orgDTOS = permissionService.listDirectParentOrgByOrgId(company.getId());
            Optional<OrgDTO> platform = orgDTOS.stream().filter(v -> v.getType().equals(OrgTypeEnum.ORG_TYPE_REGIONAL_PLATFORM.getCode())).findAny();
            List<String> blackStoreCodes = new ArrayList<>();
            List<Byte> goodsLines = new ArrayList<>();
            List<String> goodsClasses = new ArrayList<>();
            List<String> goodsSigns = new ArrayList<>();
            getNonApplyParam(companyCode, OrgTypeEnum.ORG_TYPE_BUSINESS.getCode(), blackStoreCodes, goodsLines, goodsClasses, goodsSigns);
            if (CollectionUtils.isNotEmpty(blackStoreCodes)) {
                logger.info("日均销复算企业:{}黑名单门店:{}", companyCode, JSON.toJSONString(blackStoreCodes));
                filteredStores = filteredStores.stream().filter(v -> !blackStoreCodes.contains(v.getStoreNo())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(filteredStores)) {
                    logger.info("日均销复算企业:{}今天需要请货的门店均在黑名单中", companyCode);
                    return;
                }
            }
            StoreApplyParamDTO autoApplyList = storeApplyParamService.getAutoApplyList(company.getId(), null,ApplyParamTypeEnum.MIDDLE_PACKAGE_GOODS_APPLY.getCode(), null, null);
            StoreApplyParamDTO paramDTO = storeApplyParamService.getAutoApplyList(company.getId(), null,ApplyParamTypeEnum.AUTO_APPLY.getCode(), null, null);
            StoreApplyParamDTO middleApplyList = storeApplyParamService.getAutoApplyList(company.getId(), null,ApplyParamTypeEnum.APPLY_MIDDLE_CATEGORY.getCode(), null, null);
            List<AutoApplyParamDTO> specialGoodsCtrlDTOS = paramDTO.getSpecialGoodsCtrlDTOS();
            BigDecimal specialOnceLimit = null;
            BigDecimal specialThirtyDaysLimit = null;
            logger.info("specialGoodsCtrlDTOS:{}", JSON.toJSONString(specialGoodsCtrlDTOS));
            if (CollectionUtils.isNotEmpty(specialGoodsCtrlDTOS)) {
                if ("true".equals(specialGoodsCtrlDTOS.stream().filter(v -> v.getParamCode().equals(ApplyParamCodeEnum.SPECIAL_CTRL_GOODS_SWITCH.getCode())).findFirst().orElse(new AutoApplyParamDTO()).getParamValue())){
                    AutoApplyParamDTO specialOnceLimitDTO = specialGoodsCtrlDTOS.stream().filter(v -> v.getParamCode().equals(ApplyParamCodeEnum.SPECIAL_GOODS_ONCE_APPLY_LIMIT.getCode())).findFirst().orElse(null);
                    if (Objects.nonNull(specialOnceLimitDTO)) {
                        specialOnceLimit = new BigDecimal(specialOnceLimitDTO.getParamValue());
                    }
                    AutoApplyParamDTO specialThirtyDaysLimitDTO = specialGoodsCtrlDTOS.stream().filter(v -> v.getParamCode().equals(ApplyParamCodeEnum.SPECIAL_GOODS_PER_THIRTY_DAYS_APPLY_LIMIT.getCode())).findFirst().orElse(null);
                    if (Objects.nonNull(specialThirtyDaysLimitDTO)) {
                        specialThirtyDaysLimit = new BigDecimal(specialThirtyDaysLimitDTO.getParamValue());
                    }
                }
            }
//            AutoApplyParamDTO combinedCodeRange = paramDTO.getCombinedCodeRange();
//            logger.info("combinedCodeRange:{}", JSON.toJSONString(combinedCodeRange));
//            CombinedCodeRangeEnum combinedCodeRangeEnum = CombinedCodeRangeEnum.getEnumByCode(null != combinedCodeRange ? combinedCodeRange.getParamValue() : null);

            Map<String, OrgVO> orgVOMap = new HashMap<>();
            Lists.partition(filteredStores.stream().map(MdmStoreBaseDTO::getStoreNo).distinct().collect(Collectors.toList()), Constants.FEIGN_FOREST_SERVICE_ONCE_MAX_VALUE).forEach(v ->
                orgVOMap.putAll(
                        permissionService.queryOrgInfoBySapCodes(v, OrgTypeEnum.ORG_TYPE_STORE.getCode())
                                .stream().filter(org -> StringUtils.isNotBlank(org.getSapcode())).collect(Collectors.toMap(OrgVO::getSapcode, Function.identity(), (k1,k2) -> k1))));
            for (MdmStoreBaseDTO mdmStoreBaseDTO : filteredStores) {
                OrgVO store = orgVOMap.get(mdmStoreBaseDTO.getStoreNo());
                if (null == store) {
                    logger.info("日均销复算企业:{},门店:{}没有查询到门店信息", companyCode, mdmStoreBaseDTO.getStoreNo());
                    continue;
                }
                avgRecalculationByStore(platform, company, store, autoApplyList, mdmStoreBaseDTO, goodsLines, goodsClasses, goodsSigns, specialOnceLimit, specialThirtyDaysLimit, middleApplyList);
            }
            logger.info("日均销复算企业:{}复算完毕", companyCode);
        } catch (Exception e) {
            logger.error("日均销复算企业:"+ companyCode + "使用日均销复算失败", e);
            throw e;
        }
    }

    @Override
    public void bdpCallbackStoreApplyDate() {
        executor.execute(()-> {
            List<StoreApplyCountDTO> storeApplyCountDTOS = storeApplyDateExtendMapper.selectStoreCount().stream().filter(v -> v.getRealCount().equals(v.getShouldCount())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(storeApplyCountDTOS)) {
                logger.info("没有符合条件的日均销门店请货日数据,不需要备份");
                return;
            }
            Set<String> comSet = new HashSet<>();
            for (StoreApplyCountDTO storeApplyCountDTO : storeApplyCountDTOS) {
                refreshStoreApplyToBak(storeApplyCountDTO.getCompanyCode(), storeApplyCountDTO.getApplyDate(), comSet);
            }
        });
    }
    @Transactional(rollbackFor = Exception.class)
    public void refreshStoreApplyToBak(String companyCode, Date aplyDate, Set<String> comSet) {
        StoreApplyDateExample selectExample = new StoreApplyDateExample();
        selectExample.createCriteria().andCompanyCodeEqualTo(companyCode).andApplyDateEqualTo(aplyDate);
        int perSize = comSet.size();
        comSet.add(companyCode);
        if (perSize < comSet.size()) {
            // 直接删除备份表的这个企业
            StoreApplyDateBakExample delBakExample = new StoreApplyDateBakExample();
            delBakExample.createCriteria().andCompanyCodeEqualTo(companyCode);
            long count = storeApplyDateBakMapper.countByExample(delBakExample);
            for (int i = 0 ;; i++ ) {
                delBakExample.setLimit(Constants.BATCH_DELETE_ONCE_MAX_VALUE);
                storeApplyDateBakMapper.deleteByExample(delBakExample);
                if ((long) ((i + 1) * Constants.BATCH_DELETE_ONCE_MAX_VALUE) >= count) {
                    break;
                }
            }
        }
        Lists.partition(storeApplyDateMapper.selectByExample(selectExample).stream().map(v -> {
            StoreApplyDateBak storeApplyDateBak = new StoreApplyDateBak();
            BeanUtils.copyProperties(v, storeApplyDateBak);
            return storeApplyDateBak;
        }).collect(Collectors.toList()), Constants.BATCH_INSERT_ONCE_MAX_VALUE).forEach(v -> {
            storeApplyDateBakExtendMapper.batchInsert(v);
        });
    }

    /**
     * 获取不自动请货参数
     * @param sapCode 组织编码
     * @param orgType 类型
     * @param blackStoreCodes 门店黑名单sapcode
     * @param goodsLines 不自动请货商品类型
     * @param goodsSigns 不自动请货商品标记
     * @throws Exception
     */
    private void getNonApplyParam(String sapCode, Integer orgType, List<String> blackStoreCodes, List<Byte> goodsLines, List<String> goodsClasses, List<String> goodsSigns) throws Exception {
        List<OrgVO> orgVOS = permissionService.queryOrgInfoBySapCodes(Lists.newArrayList(sapCode), orgType);
        if (CollectionUtils.isEmpty(orgVOS)) {
            logger.info("日均销复算sapcode:{}没有查询到编码对应的组织", sapCode);
            return;
        }
        // 当前企业的父级信息
        TreeMap<Integer, OrgDTO> orgDTOMap = new TreeMap<>(permissionService.listDirectParentOrgByOrgId(orgVOS.get(0).getId()).stream()
                .filter(v -> OrgTypeEnum.ORG_TYPE_COMPANY.getCode() == v.getType()
                        || OrgTypeEnum.ORG_TYPE_REGIONAL_PLATFORM.getCode() == v.getType()
                        || OrgTypeEnum.ORG_TYPE_BUSINESS.getCode() == v.getType()).collect(Collectors.toMap(OrgDTO::getType, Function.identity(), (k1, k2) -> k1)));
        // 取不自动请货门店参数配置
        NavigableMap<Integer, OrgDTO> navigableMap = orgDTOMap.descendingMap();
        for (Map.Entry<Integer, OrgDTO> entry : navigableMap.entrySet()) {
            StoreApplyParamNonAutomaticDTO storeApplyParamNonAutomaticDTO = storeApplyParamNonAutomaticService.getApplyList(null, entry.getValue().getId());
            if (storeApplyParamNonAutomaticDTO.nonNull()) {
                // 不自动请货门店
                if (null != storeApplyParamNonAutomaticDTO.getStoreInfoList() && StringUtils.isNotBlank(storeApplyParamNonAutomaticDTO.getStoreInfoList().getParamValue())) {
                    List<Long> storeIds = tagGoodsService.storeComponentQuery(Long.valueOf(storeApplyParamNonAutomaticDTO.getStoreInfoList().getParamValue()));
                    logger.info("storeIds:{}", JSON.toJSONString(storeIds));
                    if (CollectionUtils.isNotEmpty(storeIds)) {
                        List<List<Long>> partition = Lists.partition(storeIds, Constants.FEIGN_ITEM_SEARCH_ONCE_MAX_VALUE);
                        for (List<Long> s : partition) {
                            blackStoreCodes.addAll(permissionService.listOrgByOutId(OrgTypeEnum.ORG_TYPE_STORE.getCode(), s).stream().map(OrgDTO::getSapcode).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList()));
                        }
                        logger.info("blackStoreCodes:{}", JSON.toJSONString(blackStoreCodes));
                    }
                }
                // 不自动请货商品经营属性
                if (null != storeApplyParamNonAutomaticDTO.getGoodsManagementAttr() && StringUtils.isNotBlank(storeApplyParamNonAutomaticDTO.getGoodsManagementAttr().getParamValue())) {
                    goodsLines.addAll(Arrays.stream(StringUtils.split(storeApplyParamNonAutomaticDTO.getGoodsManagementAttr().getParamValue(), ",")).map(Byte::valueOf).collect(Collectors.toList()));
                }
                // 不自动请货商品类型
                if (null != storeApplyParamNonAutomaticDTO.getGoodsClass() && StringUtils.isNotBlank(storeApplyParamNonAutomaticDTO.getGoodsClass().getParamValue())) {
                    goodsClasses.addAll(Arrays.stream(StringUtils.split(storeApplyParamNonAutomaticDTO.getGoodsClass().getParamValue(), ",")).collect(Collectors.toList()));
                }
                // 不自动请货商品标记
                if (null != storeApplyParamNonAutomaticDTO.getGoodsSign() && StringUtils.isNotBlank(storeApplyParamNonAutomaticDTO.getGoodsSign().getParamValue())) {
                    goodsSigns.addAll(Arrays.stream(StringUtils.split(storeApplyParamNonAutomaticDTO.getGoodsSign().getParamValue(), ",")).map(v -> ApplyParamGoodsSignEnum.getNameByCode(Integer.parseInt(v))).collect(Collectors.toList()));
                }
                break;
            }
        }
        // 如果是门店 且在黑名单里 则直接返回该门店
        if (orgType == OrgTypeEnum.ORG_TYPE_STORE.getCode() && blackStoreCodes.contains(orgVOS.get(0).getSapcode())) {
            blackStoreCodes.clear();
            blackStoreCodes.add(orgVOS.get(0).getSapcode());
        }
    }

    @Override
    public void avgRecalculationByStores(String companyCode, String storeCodes) throws Exception {
        try {
            logger.info("日均销复算开始,企业:{},门店:{}", companyCode, storeCodes);
            if(StringUtils.isBlank(companyCode)) {
                throw new BusinessErrorException("企业编码不能为空");
            }
            if(StringUtils.isBlank(storeCodes)) {
                logger.info("无门店,直接用企业复算");
                avgRecalculationByCompany(companyCode);
                return;
            }
            OrgVO company = permissionService.queryOrgInfoBySapCodes(Lists.newArrayList(companyCode), OrgTypeEnum.ORG_TYPE_BUSINESS.getCode()).stream().findFirst().orElseThrow(() -> new BusinessErrorException("日均销复算企业:" + companyCode + "没有查询到企业信息"));
            // 取平台
            List<OrgDTO> orgDTOS = permissionService.listDirectParentOrgByOrgId(company.getId());
            Optional<OrgDTO> platform = orgDTOS.stream().filter(v -> v.getType().equals(OrgTypeEnum.ORG_TYPE_REGIONAL_PLATFORM.getCode())).findAny();
            String[] split = StringUtils.split(storeCodes, ",");
            List<MdmStoreBaseDTO> stores = storeService.findStoreInfoAndExtendByStoreNos(Lists.newArrayList(split));
            if (CollectionUtils.isEmpty(stores)) {
                logger.info("日均销复算企业:{}今天没有需要请货的门店", companyCode);
                return;
            }
            List<MdmStoreBaseDTO> filteredStores = stores.stream().filter(v -> {
                JSONObject extend = JSONObject.parseObject(v.getExtend());
                return Objects.nonNull(extend)
                        && extend.containsKey("deliveryDate")
                        && extend.getString("deliveryDate").contains(DateUtils.conventDateStrByDate(new Date(), DateUtils.DATE_PATTERN));
            }).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filteredStores)) {
                logger.info("日均销复算企业:{}过滤请货日后今天没有需要请货的门店", companyCode);
                return;
            }
            Map<String, MdmStoreBaseDTO> storeMap = filteredStores.stream().collect(Collectors.toMap(MdmStoreBaseDTO::getStoreNo, Function.identity(), (k1, k2) -> k1));
            for (String storeCode : split) {
                StoreApplyDateExample example = new StoreApplyDateExample();
                example.createCriteria().andCompanyCodeEqualTo(companyCode).andStoreCodeEqualTo(storeCode).andApplyDateEqualTo(new Date()).andStoreStatusEqualTo("营业");
                if (MapUtils.isEmpty(storeMap)) {
                    logger.info("日均销复算门店:{}今天不需要请货", storeCode);
                    return;
                }
                List<String> blackStoreCodes = new ArrayList<>();
                List<Byte> goodsLines = new ArrayList<>();
                List<String> goodsClasses = new ArrayList<>();
                List<String> goodsSigns = new ArrayList<>();
                getNonApplyParam(storeCode, OrgTypeEnum.ORG_TYPE_STORE.getCode(), blackStoreCodes, goodsLines, goodsClasses, goodsSigns);
                List<OrgVO> orgVOS = permissionService.queryOrgInfoBySapCodes(Lists.newArrayList(storeCode), OrgTypeEnum.ORG_TYPE_STORE.getCode());
                if (CollectionUtils.isEmpty(orgVOS)) {
                    logger.info("日均销复算sapcode:{}没有查询到编码对应的组织", storeCode);
                    return;
                }
                if (blackStoreCodes.contains(storeCode)) {
                    logger.info("日均销复算门店:{}在黑名单中", storeCode);
                    return;
                }
                StoreApplyParamDTO autoApplyList = storeApplyParamService.getAutoApplyList(company.getId(),null, ApplyParamTypeEnum.MIDDLE_PACKAGE_GOODS_APPLY.getCode(), null, null);
                StoreApplyParamDTO paramDTO = storeApplyParamService.getAutoApplyList(company.getId(), null,ApplyParamTypeEnum.AUTO_APPLY.getCode(), null, null);
                StoreApplyParamDTO middleApplyList = storeApplyParamService.getAutoApplyList(company.getId(), null,ApplyParamTypeEnum.APPLY_MIDDLE_CATEGORY.getCode(), null, null);
                List<AutoApplyParamDTO> specialGoodsCtrlDTOS = paramDTO.getSpecialGoodsCtrlDTOS();
                BigDecimal specialOnceLimit = null;
                BigDecimal specialThirtyDaysLimit = null;
                logger.info("specialGoodsCtrlDTOS:{}", JSON.toJSONString(specialGoodsCtrlDTOS));
                if (CollectionUtils.isNotEmpty(specialGoodsCtrlDTOS)) {
                    if ("true".equals(specialGoodsCtrlDTOS.stream().filter(v -> v.getParamCode().equals(ApplyParamCodeEnum.SPECIAL_CTRL_GOODS_SWITCH.getCode())).findFirst().orElse(new AutoApplyParamDTO()).getParamValue())) {
                        AutoApplyParamDTO specialOnceLimitDTO = specialGoodsCtrlDTOS.stream().filter(v -> v.getParamCode().equals(ApplyParamCodeEnum.SPECIAL_GOODS_ONCE_APPLY_LIMIT.getCode())).findFirst().orElse(null);
                        if (Objects.nonNull(specialOnceLimitDTO)) {
                            specialOnceLimit = new BigDecimal(specialOnceLimitDTO.getParamValue());
                        }
                        AutoApplyParamDTO specialThirtyDaysLimitDTO = specialGoodsCtrlDTOS.stream().filter(v -> v.getParamCode().equals(ApplyParamCodeEnum.SPECIAL_GOODS_PER_THIRTY_DAYS_APPLY_LIMIT.getCode())).findFirst().orElse(null);
                        if (Objects.nonNull(specialThirtyDaysLimitDTO)) {
                            specialThirtyDaysLimit = new BigDecimal(specialThirtyDaysLimitDTO.getParamValue());
                        }
                    }
                }
                avgRecalculationByStore(platform, company, orgVOS.get(0), autoApplyList, storeMap.get(storeCode), goodsLines, goodsClasses, goodsSigns, specialOnceLimit, specialThirtyDaysLimit,middleApplyList);
            }
            logger.info("日均销复算企业:{},门店:{}复算完毕", companyCode, storeCodes);
        } catch (BusinessErrorException e) {
            logger.warn("日均销复算企业" + companyCode + ",门店:"+ storeCodes + "使用日均销复算失败", e);
            throw e;
        } catch (Exception e) {
            logger.error("日均销复算企业" + companyCode + ",门店:"+ storeCodes + "使用日均销复算失败", e);
            throw e;
        }
    }

    @Override
    public void avgRecalculationByStore(Optional<OrgDTO> platform, OrgVO company, OrgVO store, StoreApplyParamDTO autoApplyList, MdmStoreBaseDTO storeApplyDate, List<Byte> goodsLines, List<String> goodsClasses, List<String> goodsSigns, BigDecimal specialOnceLimit, BigDecimal specialThirtyDaysLimit, StoreApplyParamDTO middleApplyList) throws Exception {
        try {
            String bdpApplyNo = "BDP" + DateUtils.conventDateStrByDate(new Date(), DateUtils.DATE_SAP_PATTERN) + store.getSapcode();
            String applyNo = bdpApplyNo + DateUtils.conventDateStrByDate(new Date(), DateUtils.TIME_HHMM_PATTERN);
            String zyApplyNo = applyNo + "ZY";
            BdpAvgDailySalesExample example = new BdpAvgDailySalesExample();
            BdpAvgDailySalesExample.Criteria criteria = example.createCriteria();
            criteria.andStoreIdEqualTo(store.getOutId()).andDistrForbidEqualTo((YNEnum.NO.getType().byteValue())).andGoodsStatusEqualTo((YNEnum.NO.getType().byteValue())).andLawfulEqualTo((YNEnum.YES.getType().byteValue())).andApplyForbidEqualTo((YNEnum.NO.getType().byteValue()));
            if (CollectionUtils.isNotEmpty(goodsLines)) {
                criteria.andGoodsLineNotIn(goodsLines);
            }
            if (CollectionUtils.isNotEmpty(goodsLines)) {
                if (goodsSigns.contains(ApplyParamGoodsSignEnum.DTP.getName())) {
                    criteria.andDtpGoodsEqualTo(YNEnum.NO.getType().byteValue());
                }
                if (goodsSigns.contains(ApplyParamGoodsSignEnum.COLD_CHAIN.getName())) {
                    criteria.andColdchainindEqualTo(YNEnum.NO.getType().byteValue());
                }
                if (goodsSigns.contains(ApplyParamGoodsSignEnum.PASS_TICKET.getName())) {
                    criteria.andPurchaseTypeNotEqualTo(ApplyParamGoodsSignEnum.PASS_TICKET.getName());
                }
            }
            List<Long> zyStoreMiddleCategorys = middleApplyList.getZyStoreMiddleCategorys();
            List<Long> jmStoreMiddleCategorys = middleApplyList.getJmStoreMiddleCategorys();
            Map<Integer, List<Long>> classMap = new HashMap<>();
            for (String goodsClass : goodsClasses) {
                List<Long> classIds = classMap.get(goodsClass.length());
                if (CollectionUtils.isEmpty(classIds)) {
                    classIds = new ArrayList<>();
                }
                classIds.add(Long.valueOf(goodsClass));
                classMap.put(goodsClass.length(), classIds);
            }
            for (Map.Entry<Integer, List<Long>> entry : classMap.entrySet()) {
                if (entry.getKey() == 2) {
                    criteria.andCategoryIdNotIn(entry.getValue());
                }
                if (entry.getKey() == 4) {
                    criteria.andMiddleCategoryIdNotIn(entry.getValue());
                }
                if (entry.getKey() == 6) {
                    criteria.andSmallCategoryIdNotIn(entry.getValue());
                }
                if (entry.getKey() == 8) {
                    criteria.andSubCategoryIdNotIn(entry.getValue());
                }
            }

            long count = bdpAvgDailySalesMapper.countByExample(example);
            if (count <= 0L) {
                logger.warn("日均销复算企业:{}门店:{}没有需要复算的数据", company.getSapcode(), storeApplyDate.getStoreNo());
                return;
            }
            Map<String, Integer> whiteGoodsMap = recalculationStoreApplyService.getWhiteMap(company.getId(), store.getSapcode());
            logger.info("whiteGoodsMap:{}", JSON.toJSONString(whiteGoodsMap));
            IscmStoreApplyParamGoodsOrgValidBlacklistExample blackExample = getBlackExample(platform, company.getId(), store.getSapcode());

            List<String> blackGoodsNos = new ArrayList<>();
            if (null != blackExample) {
                blackGoodsNos.addAll(iscmStoreApplyParamGoodsOrgValidBlacklistExtendMapper.selectGoodsNosByExample(blackExample));
            }
            logger.info("blackGoodsNos:{}", JSON.toJSONString(blackGoodsNos));
            Integer pageSize = Constants.BATCH_INSERT_ONCE_MAX_VALUE;
            example.setLimit(pageSize);
            AtomicInteger aInt = new AtomicInteger(0);
            // 复算结果
            List<IscmRecalculationRecord> records = new ArrayList<>();
            for (int i = 0;; i++) {
                example.setOffset(Long.valueOf(i * pageSize));
                Map<String, BdpAvgDailySales> bdpAvgDailySalesMap = bdpAvgDailySalesMapper.selectByExample(example).stream().collect(Collectors.toMap(BdpAvgDailySales::getGoodsNo, Function.identity(), (k1, k2) -> k1));
                if (MapUtils.isEmpty(bdpAvgDailySalesMap)) {
                    break;
                }
                Map<String, List<StockGoodsBatchCodeSimpleInfo>> goodsStockMap = new HashMap<>();
                StockGoodsPagableQueryParam param = new StockGoodsPagableQueryParam();
                param.setBusinessId(company.getOutId());
                param.setStoreId(store.getOutId());
                // 主替换码码关系
                Map<String, List<IscmApplyGoodsReplaceCode>> masterToReplaceMap = new HashMap<>();
                // 替换码库存
                Map<String, List<StockGoodsBatchCodeSimpleInfo>> replaceGoodsStockMap = new HashMap<>();
                // 替换码数据
                Map<String, String> replaceGoodsNoMap = new HashMap<>();
                List<String> replaceGoodsNos = new ArrayList<>();
                bdpAvgDailySalesMap.forEach((k,v) -> {
                    if (StoreAttrEnum.DIRECT.getCode().equals(v.getStoreAttr()) && CollectionUtils.isNotEmpty(zyStoreMiddleCategorys) && zyStoreMiddleCategorys.contains(v.getMiddleCategoryId())) {
                        replaceGoodsNos.add(k);
                    }
                    if (StoreAttrEnum.JM.getCode().equals(v.getStoreAttr()) && CollectionUtils.isNotEmpty(jmStoreMiddleCategorys) && jmStoreMiddleCategorys.contains(v.getMiddleCategoryId())) {
                        replaceGoodsNos.add(k);
                    }
                });
//                combinedCodeRangeEnum = null == combinedCodeRangeEnum ? CombinedCodeRangeEnum.ONLY_DIRECT : combinedCodeRangeEnum;
//                // 查门店组
//                EntityTagQueryParam tagQueryParam = new EntityTagQueryParam();
//                tagQueryParam.setEntityCodeList(Lists.newArrayList(store.getId().toString()));
//                // 写死类型
//                tagQueryParam.setEntitySelectType(2);
//                tagQueryParam.setEntityType(2);
//                tagQueryParam.setManageFlag(0);
//                tagQueryParam.setTagBizType(17);
//                List<EntityTagQueryDTO> entityTagList = tagService.entityTagQuery(tagQueryParam);
//                if (CollectionUtils.isNotEmpty(entityTagList)) {
//                    List<TagInfo> tagList = entityTagList.get(0).getTagList();
//                    if (CollectionUtils.isNotEmpty(tagList)) {
//                        Long tagId = tagList.get(0).getTagId();
//                        StoreApplyParamDTO paramDTO = storeApplyParamService.getAutoApplyList(tagId,OrgTypeEnum.ORG_TYPE_STORE_GROUP.getCode(), ApplyParamTypeEnum.AUTO_APPLY.getCode(), null,null);
//                        CombinedCodeRangeEnum enumByCode = CombinedCodeRangeEnum.getEnumByCode(paramDTO.getCombinedCodeRange().getParamValue());
//                        if (null != enumByCode) {
//                            combinedCodeRangeEnum = enumByCode;
//                        }
//                    }
//                }
                // 替换码
                BdpAvgDailySales bdpAvgDailySales = bdpAvgDailySalesMap.values().stream().findAny().get();
                if (CollectionUtils.isNotEmpty(replaceGoodsNos)) {
                    IscmApplyGoodsReplaceCodeExample replaceCodeExample = new IscmApplyGoodsReplaceCodeExample();
                    replaceCodeExample.createCriteria().andCompanyCodeEqualTo(company.getSapcode()).andMasterGoodsNoIn(replaceGoodsNos);
                    List<IscmApplyGoodsReplaceCode> replaceList = iscmApplyGoodsReplaceCodeMapper.selectByExample(replaceCodeExample)
                            .stream().filter(v -> Objects.nonNull(v.getChangeRatio()) && StringUtils.isNotBlank(v.getMasterGoodsNo()) && StringUtils.isNotBlank(v.getReplaceGoodsNo()))
                            .filter(v -> !v.getMasterGoodsNo().equals(v.getReplaceGoodsNo())).collect(Collectors.toList());
                    IscmApplyGoodsReplaceCodeExample Example = new IscmApplyGoodsReplaceCodeExample();
                    Example.createCriteria().andCompanyCodeEqualTo(company.getSapcode()).andReplaceGoodsNoIn(Lists.newArrayList(bdpAvgDailySalesMap.keySet()));
                    replaceGoodsNoMap.putAll(iscmApplyGoodsReplaceCodeMapper.selectByExample(replaceCodeExample).stream().map(IscmApplyGoodsReplaceCode::getReplaceGoodsNo).collect(Collectors.toMap(v -> v, Function.identity(), (k1, k2) -> k1)));
                    if (CollectionUtils.isNotEmpty(replaceList)) {
                        masterToReplaceMap.putAll(replaceList.stream().collect(Collectors.groupingBy(IscmApplyGoodsReplaceCode::getMasterGoodsNo)));
                        List<List<IscmApplyGoodsReplaceCode>> partition = Lists.partition(replaceList, Constants.FEIGN_SEARCHAPI_SERVICE_ONCE_MAX_VALUE);
                        for (List<IscmApplyGoodsReplaceCode> goos : partition) {
                            param.setGoodsNos(goos.stream().map(IscmApplyGoodsReplaceCode::getReplaceGoodsNo).distinct().collect(Collectors.toList()));
                            try {
                                replaceGoodsStockMap.putAll(retryService.retryStockGoodsPage(param));
                            } catch (Exception e) {
                                logger.info("替换码库存轮询后依旧失败");
                                continue;
                            }
                        }
                    }
                }
                List<List<String>> goodsNos = Lists.partition(Lists.newArrayList(bdpAvgDailySalesMap.keySet()), Constants.FEIGN_SEARCHAPI_SERVICE_ONCE_MAX_VALUE);
                List<String> failGoodsNos = new ArrayList<>();
                for (List<String> goos : goodsNos) {
                    param.setGoodsNos(goos);
                    Map<String, List<StockGoodsBatchCodeSimpleInfo>> stockMap = new HashMap<>();
                    try{
                        stockMap = retryService.retryStockGoodsPage(param);
                    } catch (Exception e) {
                        logger.info("库存轮询后依旧失败");
                        failGoodsNos.addAll(goos);
                        continue;
                    }
                    goodsStockMap.putAll(stockMap);
                }
                for (Map.Entry<String, BdpAvgDailySales> entry : bdpAvgDailySalesMap.entrySet()) {
                    BdpAvgDailySales sales = entry.getValue();
                    if (blackGoodsNos.contains(sales.getGoodsNo())){
                        logger.info("商品:{}在黑名单中", sales.getGoodsNo());
                        continue;
                    }
                    if (replaceGoodsNoMap.containsKey(sales.getGoodsNo())) {
                        logger.info("子码不用复算,排除");
                        continue;
                    }
                    // 库存
                    List<StockGoodsBatchCodeSimpleInfo> goodsStocks = goodsStockMap.get(entry.getKey());
                    if (CollectionUtils.isEmpty(goodsStocks) && failGoodsNos.contains(sales.getGoodsNo())) {
                        continue;
                    }
                    BigDecimal waitStock = CollectionUtils.isEmpty(goodsStocks) ? BigDecimal.ZERO : goodsStocks.stream().map(StockGoodsBatchCodeSimpleInfo::getWaitStock).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal unqualifiedAreaStock = CollectionUtils.isEmpty(goodsStocks) ? BigDecimal.ZERO : goodsStocks.stream().map(StockGoodsBatchCodeSimpleInfo::getUnqualifiedAreaStock).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal stock = CollectionUtils.isEmpty(goodsStocks) ? BigDecimal.ZERO : goodsStocks.stream().map(StockGoodsBatchCodeSimpleInfo::getStock).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal transitStock = CollectionUtils.isEmpty(goodsStocks) ? BigDecimal.ZERO : goodsStocks.stream().map(StockGoodsBatchCodeSimpleInfo::getTransitStock).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                    // 替换码转换
                    List<IscmApplyGoodsReplaceCode> replaceCodes = masterToReplaceMap.get(entry.getKey());
                    if (CollectionUtils.isNotEmpty(replaceCodes)) {
                        for (IscmApplyGoodsReplaceCode replaceCode : replaceCodes) {
                            if (Objects.isNull(replaceCode.getChangeRatio()) || replaceCode.getChangeRatio().compareTo(BigDecimal.ZERO) <= 0) {
                                continue;
                            }
                            List<StockGoodsBatchCodeSimpleInfo> replaceGoodsStock = replaceGoodsStockMap.get(replaceCode.getReplaceGoodsNo());
                            if (CollectionUtils.isNotEmpty(replaceGoodsStock)) {
                                waitStock = waitStock.add(replaceGoodsStock.stream().map(StockGoodsBatchCodeSimpleInfo::getWaitStock).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(replaceCode.getChangeRatio()).setScale(4, RoundingMode.HALF_UP));
                                unqualifiedAreaStock = unqualifiedAreaStock.add(replaceGoodsStock.stream().map(StockGoodsBatchCodeSimpleInfo::getUnqualifiedAreaStock).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(replaceCode.getChangeRatio()).setScale( 4, RoundingMode.HALF_UP));
                                stock = stock.add(replaceGoodsStock.stream().map(StockGoodsBatchCodeSimpleInfo::getStock).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(replaceCode.getChangeRatio()).setScale( 4, RoundingMode.HALF_UP));
                                transitStock = transitStock.add(replaceGoodsStock.stream().map(StockGoodsBatchCodeSimpleInfo::getTransitStock).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(replaceCode.getChangeRatio()).setScale( 4, RoundingMode.HALF_UP));
                            }
                        }
                    }
                    waitStock = waitStock.compareTo(BigDecimal.ZERO) == -1 ? BigDecimal.ZERO : waitStock;
                    unqualifiedAreaStock = unqualifiedAreaStock.compareTo(BigDecimal.ZERO) == -1 ? BigDecimal.ZERO : unqualifiedAreaStock;
                    stock = stock.compareTo(BigDecimal.ZERO) == -1 ? BigDecimal.ZERO : stock;
                    transitStock = transitStock.compareTo(BigDecimal.ZERO) == -1 ? BigDecimal.ZERO : transitStock;
                    // 1.请货触发条件：门店可用库存（可用库存=总库存-非可用库存+总在途库存 ）＜库存下限或者最小陈列量
                    // 非可用库存=合格品区锁定库存(WaitStock)+非合格品区库存（UnqualifiedAreaStock）
                    BigDecimal useStock = stock.subtract(waitStock.add(unqualifiedAreaStock)).add(transitStock);
                    if (!(useStock.compareTo(RecalculationStoreApplyServiceImpl.max(sales.getStockLowerLimit(), sales.getMinDisplayQty())) < 0)) {
                        continue;
                    }
                    // 请货数量=max（库存上限/最小陈列）-（总库存-非可用库存+总在途库存 ）
                    // oldApplyQty 跟applyQty比对 不同则证明用了中包装
                    BigDecimal oldApplyQty = RecalculationStoreApplyServiceImpl.max(sales.getStockUpperLimit(), sales.getMinDisplayQty()).subtract(useStock);
                    BigDecimal applyQty = RecalculationStoreApplyServiceImpl.max(sales.getStockUpperLimit(), sales.getMinDisplayQty()).subtract(useStock);
                    if (applyQty.compareTo(BigDecimal.ZERO) <= 0) {
                        continue;
                    }
                    boolean scarce = false;
                    Integer scarceLimit = null;
                    boolean middleAble = false;
                    if (whiteGoodsMap.containsKey(sales.getGoodsNo())) {
                        // 可用库存 + 请货数量 ≤ 请货修改白名单商品上限
                        logger.info("商品:{}白名单商品上限:{}",sales.getGoodsNo(), whiteGoodsMap.get(sales.getGoodsNo()));
                        scarceLimit = whiteGoodsMap.get(sales.getGoodsNo());
                        if (BigDecimal.valueOf(scarceLimit).compareTo(applyQty.add(useStock)) < 0) {
                            scarce = true;
                        }
                        applyQty = BigDecimal.valueOf(scarceLimit).compareTo(applyQty.add(useStock)) < 0 ? BigDecimal.valueOf(scarceLimit).subtract(useStock) : applyQty;
                        if (applyQty.compareTo(BigDecimal.ZERO) <= 0) {
                            continue;
                        }
                    }
                    // 中包装取整规则 优先级 门店SKU维度 > 企业SKU维度 > 企业维度
                    // 企业中包装开关
                    BigDecimal middleQty = sales.getMiddlePackageQty();
                    // 起请比例
                    BigDecimal applyRatio = sales.getApplyRatio();
                    logger.info("商品:{}起请判断applyQty:{},middleQty:{}", sales.getGoodsNo(), applyQty, middleQty);
                    if (null != middleQty && null != applyRatio && applyQty.compareTo(middleQty) <= 0) {
                        applyRatio = applyRatio.divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
                        logger.info("商品:{}起请判断applyRatio:{},applyQty:{},middleQty:{}", sales.getGoodsNo(), applyRatio, applyQty, middleQty);
                        // 起请逻辑处理条件：一个以内（包括一个）“中包装量
                        // 中包装数量 * 起请比例 > 请货数量 (舍去 不给建议)
                        if (applyQty.compareTo(middleQty.multiply(applyRatio)) < 0) {
                            logger.info("商品:{}中包装数量*起请比例>请货数量",sales.getGoodsNo());
                            continue;
                        }
                    }
                    // 非中药饮片才需要圆整
                    if (!"1202".equals(sales.getMiddleCategoryId().toString())) {
                        if (!(Objects.isNull(middleQty) || BigDecimal.ZERO.compareTo(middleQty) == 0)) {
                            logger.info("企业:{}门店:{}商品:{}中包装数量不为空需要用中包装复算", company.getSapcode(), sales.getStoreCode(), sales.getGoodsNo());
                            // 优先取一店一目商品的中包装数量,如果没有 则取企业级商品的中包装数量
                            ComMiddleSwitchEnum switchEnum = ComMiddleSwitchEnum.getEnumByCode(sales.getMiddlePackageSwitchStore());
                            if (null == switchEnum) {
                                switchEnum = ComMiddleSwitchEnum.getEnumByCode(sales.getMiddlePackageSwitchBiz());
                            }
                            if (Objects.nonNull(switchEnum)) {
                                logger.info("企业:{}门店:{}商品:{}取一店一目商品中包装处理方式:{}", company.getSapcode(), sales.getStoreCode(), sales.getGoodsNo(), switchEnum.getName());
                                if (switchEnum.equals(ComMiddleSwitchEnum.UP)) {
                                    // 如果请货数量<=中包装数量, 则凑一个中包装数量
                                    if (applyQty.compareTo(middleQty) <= 0) {
                                        applyQty = middleQty;
                                        middleAble = true;
                                    } else {
                                        BigDecimal[] bigDecimals = applyQty.divideAndRemainder(middleQty);
                                        if (bigDecimals[1].compareTo(BigDecimal.ZERO) > 0) {
                                            applyQty = bigDecimals[0].multiply(middleQty).add(middleQty);
                                            middleAble = true;
                                        } else {
                                            applyQty = bigDecimals[0].multiply(middleQty);
                                            middleAble = true;
                                        }
                                    }
                                }
                                if (switchEnum.equals(ComMiddleSwitchEnum.HALF_UP)) {
                                    // 如果请货数量<=中包装数量, 则凑一个中包装数量
                                    if (applyQty.compareTo(middleQty) <= 0) {
                                        applyQty = middleQty;
                                        middleAble = true;
                                    } else {
                                        BigDecimal[] bigDecimals = applyQty.divideAndRemainder(middleQty);
                                        if (bigDecimals[1].divide(middleQty, 2, RoundingMode.HALF_UP).compareTo(new BigDecimal("0.5")) < 0) {
                                            applyQty = bigDecimals[0].multiply(middleQty);
                                            middleAble = true;
                                        } else {
                                            applyQty = bigDecimals[0].multiply(middleQty).add(middleQty);
                                            middleAble = true;
                                        }
                                    }
                                }
                            } else {
                                // 如果一店一目以及企业均没有配置是否启用中包装,则取iscm的参数
                                AutoApplyParamDTO switchDTO = autoApplyList.getMiddleDealSwitchDTO();
                                if (Objects.isNull(switchDTO)) {
                                    switchDTO = new AutoApplyParamDTO();
                                    switchDTO.setParamValue("false");
                                }
                                if ("true".equals(switchDTO.getParamValue())) {
                                    AutoApplyRemainderDealDTO remainderDealDTO = autoApplyList.getRemainderDealDTO();
                                    ApplyParamRemainderDealEnum dealEnum = ApplyParamRemainderDealEnum.getEnumByCode(remainderDealDTO.getParamValue());
                                    if (Objects.nonNull(dealEnum)) {
                                        // 如果请货数量<=中包装数量, 则凑一个中包装数量
                                        if (applyQty.compareTo(middleQty) <= 0) {
                                            applyQty = middleQty;
                                            middleAble = true;
                                        } else {
                                            if (dealEnum.equals(ApplyParamRemainderDealEnum.UP)) {
                                                BigDecimal[] bigDecimals = applyQty.divideAndRemainder(middleQty);
                                                if (bigDecimals[1].compareTo(BigDecimal.ZERO) > 0) {
                                                    applyQty = bigDecimals[0].multiply(middleQty).add(middleQty);
                                                    middleAble = true;
                                                } else {
                                                    applyQty = bigDecimals[0].multiply(middleQty);
                                                    middleAble = true;
                                                }
                                            } else if (dealEnum.equals(ApplyParamRemainderDealEnum.HALF_UP)) {
                                                BigDecimal[] bigDecimals = applyQty.divideAndRemainder(middleQty);
                                                if (bigDecimals[1].divide(middleQty, 2, RoundingMode.HALF_UP).compareTo(new BigDecimal("0.5")) < 0) {
                                                    applyQty = bigDecimals[0].multiply(middleQty);
                                                    middleAble = true;
                                                } else {
                                                    applyQty = bigDecimals[0].multiply(middleQty).add(middleQty);
                                                    middleAble = true;
                                                }
                                            } else if (dealEnum.equals(ApplyParamRemainderDealEnum.SELF)) {
                                                BigDecimal[] bigDecimals = applyQty.divideAndRemainder(middleQty);
                                                if (bigDecimals[1].divide(middleQty, 2, RoundingMode.HALF_UP).compareTo(remainderDealDTO.getSelf()) < 0) {
                                                    applyQty = bigDecimals[0].multiply(middleQty);
                                                    middleAble = true;
                                                } else {
                                                    applyQty = bigDecimals[0].multiply(middleQty).add(middleQty);
                                                    middleAble = true;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if (YNEnum.YES.getType().equals(sales.getSpecialCtrl())) {
                        BigDecimal once = null, thirty = null;
                        if (Objects.nonNull(specialOnceLimit)) {
                            if (applyQty.compareTo(specialOnceLimit) > 0) {
                                once = specialOnceLimit;
                                applyQty = specialOnceLimit;
                            }
                        }
                        if (Objects.nonNull(specialThirtyDaysLimit)) {
                            if (applyQty.add(sales.getSpecialThirtyDaysQty()).compareTo(specialThirtyDaysLimit) > 0) {
                                thirty = applyQty.subtract(sales.getSpecialThirtyDaysQty().add(applyQty).subtract(specialThirtyDaysLimit));
                                applyQty = thirty;
                            }
                        }
                        if (Objects.nonNull(once) && Objects.nonNull(thirty)) {
                            applyQty = RecalculationStoreApplyServiceImpl.min(once, thirty);
                        }
                        // 如果是中包装商品 需要是中包装的整数倍
                        if (applyQty.compareTo(oldApplyQty) != 0) {
                            BigDecimal[] bigDecimals = applyQty.divideAndRemainder(middleQty);
                            applyQty = applyQty.subtract(bigDecimals[1]);
                        }
                    }
                    IscmRecalculationRecord record = new IscmRecalculationRecord();
                    List<String> splitCompanyCodes = Arrays.stream(StringUtils.split(splitCompanys, ",")).collect(Collectors.toList());
                    if(splitCompanyCodes.contains(company.getSapcode()) && "1202".equals(sales.getMiddleCategoryId().toString())) {
                        record.setApplyNo(zyApplyNo);
                    } else {
                        record.setApplyNo(applyNo);
                    }
                    record.setCategoryId(sales.getSubCategoryId());
                    record.setApplyRatio(sales.getApplyRatio());
                    record.setBdpApplyNo(bdpApplyNo);
                    record.setApplyLine(aInt.incrementAndGet() + "");
                    record.setCompanyOrgId(company.getId());
                    record.setBusinessId(company.getOutId());
                    record.setCompanyOrgName(company.getShortName());
                    record.setCompanyCode(company.getSapcode());
                    record.setStoreOrgId(store.getId());
                    record.setStoreId(store.getOutId());
                    record.setStoreCode(store.getSapcode());
                    record.setStoreName(store.getShortName());
                    record.setApplyDate(new Date());
                    record.setDataOriginType((byte) 3);
                    record.setGoodsNo(sales.getGoodsNo());
                    record.setApplyGoodsType(ApplyGoodsTypeEnum.NORMAL_GOODS.getCode());
                    record.setBdpApplyTotal(BigDecimal.ZERO);
                    record.setVarA(BigDecimal.ZERO);
                    record.setVarB(BigDecimal.ZERO);
                    if (null != ComMiddleSwitchEnum.getEnumByCode(sales.getMiddlePackageSwitchBiz())) {
                        record.setMiddlePackageSwitch(ComMiddleSwitchEnum.getEnumByCode(sales.getMiddlePackageSwitchBiz()).getCode());
                    }
                    if (null != ComMiddleSwitchEnum.getEnumByCode(sales.getMiddlePackageSwitchStore())) {
                        record.setMiddlePackageSwitch(ComMiddleSwitchEnum.getEnumByCode(sales.getMiddlePackageSwitchStore()).getCode());
                    }
                    record.setMiddlePackageQty(sales.getMiddlePackageQty());
                    if (!sales.getMiddleCategoryId().toString().equals("1202")) {
                        record.setApplyTotal(applyQty.setScale(0, RoundingMode.UP));
                    } else {
                        record.setApplyTotal(applyQty);
                    }
                    record.setBdpAverageDailySales(sales.getAverageDailySales());
                    record.setMinDisplayQty(sales.getMinDisplayQty());
                    record.setStockUpperLimit(sales.getStockUpperLimit());
                    record.setStockLowerLimit(sales.getStockLowerLimit());
                    record.setSaleDaysBefore(BigDecimal.ZERO);
                    record.setSaleDaysAfter(BigDecimal.ZERO);
                    record.setPurchaseType(0);// 0 不存在
                    record.setPurchaseChannel("");// 不存在
                    record.setSpecialCtrl(sales.getSpecialCtrl() == YNEnum.NO.getType().byteValue() ? YNEnum.NO.getDesc() : YNEnum.YES.getDesc());
                    record.setSpecialThirtyDaysQty(sales.getSpecialThirtyDaysQty());
                    record.setWarehouseCode("");
                    StringBuilder applyReason = new StringBuilder();
                    if (record.getStockLowerLimit().compareTo(record.getMinDisplayQty()) > 0) {
                        if (!scarce) {
                            if (record.getStockUpperLimit().compareTo(record.getMinDisplayQty()) > 0){
                                applyReason.append(RecalculationReasonEnum.REASON_ONE.getCode());
                            } else {
                                applyReason.append(RecalculationReasonEnum.REASON_TWO.getCode());
                            }
                        } else {
                            applyReason.append(RecalculationReasonEnum.REASON_THREE.getCode());
                        }
                    } else {
                        if (!scarce) {
                            if (record.getStockUpperLimit().compareTo(record.getMinDisplayQty()) > 0){
                                applyReason.append(RecalculationReasonEnum.REASON_FOUR.getCode());
                            } else {
                                applyReason.append(RecalculationReasonEnum.REASON_FIVE.getCode());
                            }
                        } else {
                            applyReason.append(RecalculationReasonEnum.REASON_SIX.getCode());
                        }
                    }
                    if (middleAble) {
                        applyReason.append("," + RecalculationReasonEnum.REASON_TEN.getCode());
                    }
                    record.setApplyStock(useStock);
                    record.setScarceLimit(scarceLimit);
                    record.setApplyReason(applyReason.toString());
                    records.add(record);
                }
                if (Long.valueOf(i * pageSize) >= count) {
                    break;
                }
            }
            if (CollectionUtils.isNotEmpty(records)) {
                Lists.partition(records, Constants.BATCH_INSERT_ONCE_MAX_VALUE).forEach(v -> iscmRecalculationRecordExtendMapper.batchInsert(v));
                if (pushHdSwitch) {
                    logger.info("企业:{}门店:{}开始推送mb", company.getSapcode(), store.getSapcode());
                    recalculationStoreApplyService.pushToHd(company.getSapcode(), applyNo);
                    recalculationStoreApplyService.pushToHd(company.getSapcode(), zyApplyNo);
                    logger.info("企业:{}门店:{}推送完毕", company.getSapcode(), store.getSapcode());
                }
            }

        } catch (BusinessErrorException e) {
            logger.warn("日均销复算门店:"+ storeApplyDate.getStoreNo() + "使用日均销复算失败", e);
            throw e;
        } catch (Exception e) {
            logger.error("日均销复算门店:"+ storeApplyDate.getStoreNo() + "使用日均销复算失败", e);
            throw e;
        }
    }
    public List<IscmRecalculationRecord> recalculationManualByStore(Optional<OrgDTO> platform, OrgVO company, OrgVO store, StoreApplyParamDTO autoApplyList, List<Byte> goodsLines, List<String> goodsClasses, List<String> goodsSigns, BigDecimal specialOnceLimit, BigDecimal specialThirtyDaysLimit, StoreApplyParamDTO middleApplyList) throws Exception {
        try {
            String bdpApplyNo = "BDP" + DateUtils.conventDateStrByDate(new Date(), DateUtils.DATE_SAP_PATTERN) + store.getSapcode();
            String applyNo = bdpApplyNo + DateUtils.conventDateStrByDate(new Date(), DateUtils.TIME_HHMM_PATTERN);
            String zyApplyNo = applyNo + "ZY";
            BdpAvgDailySalesExample example = new BdpAvgDailySalesExample();
            BdpAvgDailySalesExample.Criteria criteria = example.createCriteria();
            criteria.andStoreIdEqualTo(store.getOutId()).andDistrForbidEqualTo((YNEnum.NO.getType().byteValue())).andGoodsStatusEqualTo((YNEnum.NO.getType().byteValue())).andLawfulEqualTo((YNEnum.YES.getType().byteValue())).andApplyForbidEqualTo((YNEnum.NO.getType().byteValue()));
            if (CollectionUtils.isNotEmpty(goodsLines)) {
                criteria.andGoodsLineNotIn(goodsLines);
            }
            if (CollectionUtils.isNotEmpty(goodsLines)) {
                if (goodsSigns.contains(ApplyParamGoodsSignEnum.DTP.getName())) {
                    criteria.andDtpGoodsEqualTo(YNEnum.NO.getType().byteValue());
                }
                if (goodsSigns.contains(ApplyParamGoodsSignEnum.COLD_CHAIN.getName())) {
                    criteria.andColdchainindEqualTo(YNEnum.NO.getType().byteValue());
                }
                if (goodsSigns.contains(ApplyParamGoodsSignEnum.PASS_TICKET.getName())) {
                    criteria.andPurchaseTypeNotEqualTo(ApplyParamGoodsSignEnum.PASS_TICKET.getName());
                }
            }
            List<Long> zyStoreMiddleCategorys = middleApplyList.getZyStoreMiddleCategorys();
            List<Long> jmStoreMiddleCategorys = middleApplyList.getJmStoreMiddleCategorys();
            Map<Integer, List<Long>> classMap = new HashMap<>();
            for (String goodsClass : goodsClasses) {
                List<Long> classIds = classMap.get(goodsClass.length());
                if (CollectionUtils.isEmpty(classIds)) {
                    classIds = new ArrayList<>();
                }
                classIds.add(Long.valueOf(goodsClass));
                classMap.put(goodsClass.length(), classIds);
            }
            for (Map.Entry<Integer, List<Long>> entry : classMap.entrySet()) {
                if (entry.getKey() == 2) {
                    criteria.andCategoryIdNotIn(entry.getValue());
                }
                if (entry.getKey() == 4) {
                    criteria.andMiddleCategoryIdNotIn(entry.getValue());
                }
                if (entry.getKey() == 6) {
                    criteria.andSmallCategoryIdNotIn(entry.getValue());
                }
                if (entry.getKey() == 8) {
                    criteria.andSubCategoryIdNotIn(entry.getValue());
                }
            }

            long count = bdpAvgDailySalesMapper.countByExample(example);
            if (count <= 0L) {
                logger.warn("日均销复算企业:{}门店:{}没有需要复算的数据", company.getSapcode(), store.getSapcode());
                return new ArrayList<>();
            }
            Map<String, Integer> whiteGoodsMap = recalculationStoreApplyService.getWhiteMap(company.getId(), store.getSapcode());
            logger.info("whiteGoodsMap:{}", JSON.toJSONString(whiteGoodsMap));
            IscmStoreApplyParamGoodsOrgValidBlacklistExample blackExample = getBlackExample(platform, company.getId(), store.getSapcode());

            List<String> blackGoodsNos = new ArrayList<>();
            if (null != blackExample) {
                blackGoodsNos.addAll(iscmStoreApplyParamGoodsOrgValidBlacklistExtendMapper.selectGoodsNosByExample(blackExample));
            }
            logger.info("blackGoodsNos:{}", JSON.toJSONString(blackGoodsNos));
            Integer pageSize = Constants.BATCH_INSERT_ONCE_MAX_VALUE;
            example.setLimit(pageSize);
            AtomicInteger aInt = new AtomicInteger(0);
            // 复算结果
            List<IscmRecalculationRecord> records = new ArrayList<>();
            for (int i = 0;; i++) {
                example.setOffset(Long.valueOf(i * pageSize));
                Map<String, BdpAvgDailySales> bdpAvgDailySalesMap = bdpAvgDailySalesMapper.selectByExample(example).stream().collect(Collectors.toMap(BdpAvgDailySales::getGoodsNo, Function.identity(), (k1, k2) -> k1));
                if (MapUtils.isEmpty(bdpAvgDailySalesMap)) {
                    break;
                }
                Map<String, List<StockGoodsBatchCodeSimpleInfo>> goodsStockMap = new HashMap<>();
                StockGoodsPagableQueryParam param = new StockGoodsPagableQueryParam();
                param.setBusinessId(company.getOutId());
                param.setStoreId(store.getOutId());
                // 主替换码码关系
                Map<String, List<IscmApplyGoodsReplaceCode>> masterToReplaceMap = new HashMap<>();
                // 替换码库存
                Map<String, List<StockGoodsBatchCodeSimpleInfo>> replaceGoodsStockMap = new HashMap<>();
                // 替换码数据
                Map<String, String> replaceGoodsNoMap = new HashMap<>();
                List<String> replaceGoodsNos = new ArrayList<>();
                bdpAvgDailySalesMap.forEach((k,v) -> {
                    if (StoreAttrEnum.DIRECT.getCode().equals(v.getStoreAttr()) && CollectionUtils.isNotEmpty(zyStoreMiddleCategorys) && zyStoreMiddleCategorys.contains(v.getMiddleCategoryId())) {
                        replaceGoodsNos.add(k);
                    }
                    if (StoreAttrEnum.JM.getCode().equals(v.getStoreAttr()) && CollectionUtils.isNotEmpty(jmStoreMiddleCategorys) && jmStoreMiddleCategorys.contains(v.getMiddleCategoryId())) {
                        replaceGoodsNos.add(k);
                    }
                });
                // 替换码
                BdpAvgDailySales bdpAvgDailySales = bdpAvgDailySalesMap.values().stream().findAny().get();
                if (CollectionUtils.isNotEmpty(replaceGoodsNos)) {
                    IscmApplyGoodsReplaceCodeExample replaceCodeExample = new IscmApplyGoodsReplaceCodeExample();
                    replaceCodeExample.createCriteria().andCompanyCodeEqualTo(company.getSapcode()).andMasterGoodsNoIn(replaceGoodsNos);
                    List<IscmApplyGoodsReplaceCode> replaceList = iscmApplyGoodsReplaceCodeMapper.selectByExample(replaceCodeExample)
                            .stream().filter(v -> Objects.nonNull(v.getChangeRatio()) && StringUtils.isNotBlank(v.getMasterGoodsNo()) && StringUtils.isNotBlank(v.getReplaceGoodsNo()))
                            .filter(v -> !v.getMasterGoodsNo().equals(v.getReplaceGoodsNo())).collect(Collectors.toList());
                    IscmApplyGoodsReplaceCodeExample Example = new IscmApplyGoodsReplaceCodeExample();
                    Example.createCriteria().andCompanyCodeEqualTo(company.getSapcode()).andReplaceGoodsNoIn(Lists.newArrayList(bdpAvgDailySalesMap.keySet()));
                    replaceGoodsNoMap.putAll(iscmApplyGoodsReplaceCodeMapper.selectByExample(replaceCodeExample).stream().map(IscmApplyGoodsReplaceCode::getReplaceGoodsNo).collect(Collectors.toMap(v -> v, Function.identity(), (k1, k2) -> k1)));
                    if (CollectionUtils.isNotEmpty(replaceList)) {
                        masterToReplaceMap.putAll(replaceList.stream().collect(Collectors.groupingBy(IscmApplyGoodsReplaceCode::getMasterGoodsNo)));
                        List<List<IscmApplyGoodsReplaceCode>> partition = Lists.partition(replaceList, Constants.FEIGN_SEARCHAPI_SERVICE_ONCE_MAX_VALUE);
                        for (List<IscmApplyGoodsReplaceCode> goos : partition) {
                            param.setGoodsNos(goos.stream().map(IscmApplyGoodsReplaceCode::getReplaceGoodsNo).distinct().collect(Collectors.toList()));
                            try {
                                replaceGoodsStockMap.putAll(retryService.retryStockGoodsPage(param));
                            } catch (Exception e) {
                                logger.info("替换码库存轮询后依旧失败");
                                continue;
                            }
                        }
                    }
                }
                List<List<String>> goodsNos = Lists.partition(Lists.newArrayList(bdpAvgDailySalesMap.keySet()), Constants.FEIGN_SEARCHAPI_SERVICE_ONCE_MAX_VALUE);
                List<String> failGoodsNos = new ArrayList<>();
                for (List<String> goos : goodsNos) {
                    param.setGoodsNos(goos);
                    Map<String, List<StockGoodsBatchCodeSimpleInfo>> stockMap = new HashMap<>();
                    try{
                        stockMap = retryService.retryStockGoodsPage(param);
                    } catch (Exception e) {
                        logger.info("库存轮询后依旧失败");
                        failGoodsNos.addAll(goos);
                        continue;
                    }
                    goodsStockMap.putAll(stockMap);
                }
                for (Map.Entry<String, BdpAvgDailySales> entry : bdpAvgDailySalesMap.entrySet()) {
                    BdpAvgDailySales sales = entry.getValue();
                    if (blackGoodsNos.contains(sales.getGoodsNo())){
                        logger.info("商品:{}在黑名单中", sales.getGoodsNo());
                        continue;
                    }
                    if (replaceGoodsNoMap.containsKey(sales.getGoodsNo())) {
                        logger.info("子码不用复算,排除");
                        continue;
                    }
                    // 库存
                    List<StockGoodsBatchCodeSimpleInfo> goodsStocks = goodsStockMap.get(entry.getKey());
                    if (CollectionUtils.isEmpty(goodsStocks) && failGoodsNos.contains(sales.getGoodsNo())) {
                        continue;
                    }
                    BigDecimal waitStock = CollectionUtils.isEmpty(goodsStocks) ? BigDecimal.ZERO : goodsStocks.stream().map(StockGoodsBatchCodeSimpleInfo::getWaitStock).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal unqualifiedAreaStock = CollectionUtils.isEmpty(goodsStocks) ? BigDecimal.ZERO : goodsStocks.stream().map(StockGoodsBatchCodeSimpleInfo::getUnqualifiedAreaStock).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal stock = CollectionUtils.isEmpty(goodsStocks) ? BigDecimal.ZERO : goodsStocks.stream().map(StockGoodsBatchCodeSimpleInfo::getStock).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal transitStock = CollectionUtils.isEmpty(goodsStocks) ? BigDecimal.ZERO : goodsStocks.stream().map(StockGoodsBatchCodeSimpleInfo::getTransitStock).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                    // 替换码转换
                    List<IscmApplyGoodsReplaceCode> replaceCodes = masterToReplaceMap.get(entry.getKey());
                    if (CollectionUtils.isNotEmpty(replaceCodes)) {
                        for (IscmApplyGoodsReplaceCode replaceCode : replaceCodes) {
                            if (Objects.isNull(replaceCode.getChangeRatio()) || replaceCode.getChangeRatio().compareTo(BigDecimal.ZERO) <= 0) {
                                continue;
                            }
                            List<StockGoodsBatchCodeSimpleInfo> replaceGoodsStock = replaceGoodsStockMap.get(replaceCode.getReplaceGoodsNo());
                            if (CollectionUtils.isNotEmpty(replaceGoodsStock)) {
                                waitStock = waitStock.add(replaceGoodsStock.stream().map(StockGoodsBatchCodeSimpleInfo::getWaitStock).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(replaceCode.getChangeRatio()).setScale(4, RoundingMode.HALF_UP));
                                unqualifiedAreaStock = unqualifiedAreaStock.add(replaceGoodsStock.stream().map(StockGoodsBatchCodeSimpleInfo::getUnqualifiedAreaStock).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(replaceCode.getChangeRatio()).setScale( 4, RoundingMode.HALF_UP));
                                stock = stock.add(replaceGoodsStock.stream().map(StockGoodsBatchCodeSimpleInfo::getStock).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(replaceCode.getChangeRatio()).setScale( 4, RoundingMode.HALF_UP));
                                transitStock = transitStock.add(replaceGoodsStock.stream().map(StockGoodsBatchCodeSimpleInfo::getTransitStock).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(replaceCode.getChangeRatio()).setScale( 4, RoundingMode.HALF_UP));
                            }
                        }
                    }
                    waitStock = waitStock.compareTo(BigDecimal.ZERO) == -1 ? BigDecimal.ZERO : waitStock;
                    unqualifiedAreaStock = unqualifiedAreaStock.compareTo(BigDecimal.ZERO) == -1 ? BigDecimal.ZERO : unqualifiedAreaStock;
                    stock = stock.compareTo(BigDecimal.ZERO) == -1 ? BigDecimal.ZERO : stock;
                    transitStock = transitStock.compareTo(BigDecimal.ZERO) == -1 ? BigDecimal.ZERO : transitStock;
                    // 1.请货触发条件：门店可用库存（可用库存=总库存-非可用库存+总在途库存 ）＜库存下限或者最小陈列量
                    // 非可用库存=合格品区锁定库存(WaitStock)+非合格品区库存（UnqualifiedAreaStock）
                    BigDecimal useStock = stock.subtract(waitStock.add(unqualifiedAreaStock)).add(transitStock);
                    if (!(useStock.compareTo(RecalculationStoreApplyServiceImpl.max(sales.getStockLowerLimit(), sales.getMinDisplayQty())) < 0)) {
                        continue;
                    }
                    // 请货数量=max（库存上限/最小陈列）-（总库存-非可用库存+总在途库存 ）
                    // oldApplyQty 跟applyQty比对 不同则证明用了中包装
                    BigDecimal oldApplyQty = RecalculationStoreApplyServiceImpl.max(sales.getStockUpperLimit(), sales.getMinDisplayQty()).subtract(useStock);
                    BigDecimal applyQty = RecalculationStoreApplyServiceImpl.max(sales.getStockUpperLimit(), sales.getMinDisplayQty()).subtract(useStock);
                    if (applyQty.compareTo(BigDecimal.ZERO) <= 0) {
                        continue;
                    }
                    boolean scarce = false;
                    Integer scarceLimit = null;
                    boolean middleAble = false;
                    if (whiteGoodsMap.containsKey(sales.getGoodsNo())) {
                        // 可用库存 + 请货数量 ≤ 请货修改白名单商品上限
                        logger.info("商品:{}白名单商品上限:{}",sales.getGoodsNo(), whiteGoodsMap.get(sales.getGoodsNo()));
                        scarceLimit = whiteGoodsMap.get(sales.getGoodsNo());
                        if (BigDecimal.valueOf(scarceLimit).compareTo(applyQty.add(useStock)) < 0) {
                            scarce = true;
                        }
                        applyQty = BigDecimal.valueOf(scarceLimit).compareTo(applyQty.add(useStock)) < 0 ? BigDecimal.valueOf(scarceLimit).subtract(useStock) : applyQty;
                        if (applyQty.compareTo(BigDecimal.ZERO) <= 0) {
                            continue;
                        }
                    }
                    // 中包装取整规则 优先级 门店SKU维度 > 企业SKU维度 > 企业维度
                    // 企业中包装开关
                    BigDecimal middleQty = sales.getMiddlePackageQty();
                    // 起请比例
                    BigDecimal applyRatio = sales.getApplyRatio();
                    logger.info("商品:{}起请判断applyQty:{},middleQty:{}", sales.getGoodsNo(), applyQty, middleQty);
                    if (null != middleQty && null != applyRatio && applyQty.compareTo(middleQty) <= 0) {
                        applyRatio = applyRatio.divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
                        logger.info("商品:{}起请判断applyRatio:{},applyQty:{},middleQty:{}", sales.getGoodsNo(), applyRatio, applyQty, middleQty);
                        // 起请逻辑处理条件：一个以内（包括一个）“中包装量
                        // 中包装数量 * 起请比例 > 请货数量 (舍去 不给建议)
                        if (applyQty.compareTo(middleQty.multiply(applyRatio)) < 0) {
                            logger.info("商品:{}中包装数量*起请比例>请货数量",sales.getGoodsNo());
                            continue;
                        }
                    }
                    // 非中药饮片才需要圆整
                    if (!"1202".equals(sales.getMiddleCategoryId().toString())) {
                        if (!(Objects.isNull(middleQty) || BigDecimal.ZERO.compareTo(middleQty) == 0)) {
                            logger.info("企业:{}门店:{}商品:{}中包装数量不为空需要用中包装复算", company.getSapcode(), sales.getStoreCode(), sales.getGoodsNo());
                            // 优先取一店一目商品的中包装数量,如果没有 则取企业级商品的中包装数量
                            ComMiddleSwitchEnum switchEnum = ComMiddleSwitchEnum.getEnumByCode(sales.getMiddlePackageSwitchStore());
                            if (null == switchEnum) {
                                switchEnum = ComMiddleSwitchEnum.getEnumByCode(sales.getMiddlePackageSwitchBiz());
                            }
                            if (Objects.nonNull(switchEnum)) {
                                logger.info("企业:{}门店:{}商品:{}取一店一目商品中包装处理方式:{}", company.getSapcode(), sales.getStoreCode(), sales.getGoodsNo(), switchEnum.getName());
                                if (switchEnum.equals(ComMiddleSwitchEnum.UP)) {
                                    // 如果请货数量<=中包装数量, 则凑一个中包装数量
                                    if (applyQty.compareTo(middleQty) <= 0) {
                                        applyQty = middleQty;
                                        middleAble = true;
                                    } else {
                                        BigDecimal[] bigDecimals = applyQty.divideAndRemainder(middleQty);
                                        if (bigDecimals[1].compareTo(BigDecimal.ZERO) > 0) {
                                            applyQty = bigDecimals[0].multiply(middleQty).add(middleQty);
                                            middleAble = true;
                                        } else {
                                            applyQty = bigDecimals[0].multiply(middleQty);
                                            middleAble = true;
                                        }
                                    }
                                }
                                if (switchEnum.equals(ComMiddleSwitchEnum.HALF_UP)) {
                                    // 如果请货数量<=中包装数量, 则凑一个中包装数量
                                    if (applyQty.compareTo(middleQty) <= 0) {
                                        applyQty = middleQty;
                                        middleAble = true;
                                    } else {
                                        BigDecimal[] bigDecimals = applyQty.divideAndRemainder(middleQty);
                                        if (bigDecimals[1].divide(middleQty, 2, RoundingMode.HALF_UP).compareTo(new BigDecimal("0.5")) < 0) {
                                            applyQty = bigDecimals[0].multiply(middleQty);
                                            middleAble = true;
                                        } else {
                                            applyQty = bigDecimals[0].multiply(middleQty).add(middleQty);
                                            middleAble = true;
                                        }
                                    }
                                }
                            } else {
                                // 如果一店一目以及企业均没有配置是否启用中包装,则取iscm的参数
                                AutoApplyParamDTO switchDTO = autoApplyList.getMiddleDealSwitchDTO();
                                if (Objects.isNull(switchDTO)) {
                                    switchDTO = new AutoApplyParamDTO();
                                    switchDTO.setParamValue("false");
                                }
                                if ("true".equals(switchDTO.getParamValue())) {
                                    AutoApplyRemainderDealDTO remainderDealDTO = autoApplyList.getRemainderDealDTO();
                                    ApplyParamRemainderDealEnum dealEnum = ApplyParamRemainderDealEnum.getEnumByCode(remainderDealDTO.getParamValue());
                                    if (Objects.nonNull(dealEnum)) {
                                        // 如果请货数量<=中包装数量, 则凑一个中包装数量
                                        if (applyQty.compareTo(middleQty) <= 0) {
                                            applyQty = middleQty;
                                            middleAble = true;
                                        } else {
                                            if (dealEnum.equals(ApplyParamRemainderDealEnum.UP)) {
                                                BigDecimal[] bigDecimals = applyQty.divideAndRemainder(middleQty);
                                                if (bigDecimals[1].compareTo(BigDecimal.ZERO) > 0) {
                                                    applyQty = bigDecimals[0].multiply(middleQty).add(middleQty);
                                                    middleAble = true;
                                                } else {
                                                    applyQty = bigDecimals[0].multiply(middleQty);
                                                    middleAble = true;
                                                }
                                            } else if (dealEnum.equals(ApplyParamRemainderDealEnum.HALF_UP)) {
                                                BigDecimal[] bigDecimals = applyQty.divideAndRemainder(middleQty);
                                                if (bigDecimals[1].divide(middleQty, 2, RoundingMode.HALF_UP).compareTo(new BigDecimal("0.5")) < 0) {
                                                    applyQty = bigDecimals[0].multiply(middleQty);
                                                    middleAble = true;
                                                } else {
                                                    applyQty = bigDecimals[0].multiply(middleQty).add(middleQty);
                                                    middleAble = true;
                                                }
                                            } else if (dealEnum.equals(ApplyParamRemainderDealEnum.SELF)) {
                                                BigDecimal[] bigDecimals = applyQty.divideAndRemainder(middleQty);
                                                if (bigDecimals[1].divide(middleQty, 2, RoundingMode.HALF_UP).compareTo(remainderDealDTO.getSelf()) < 0) {
                                                    applyQty = bigDecimals[0].multiply(middleQty);
                                                    middleAble = true;
                                                } else {
                                                    applyQty = bigDecimals[0].multiply(middleQty).add(middleQty);
                                                    middleAble = true;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if (YNEnum.YES.getType().equals(sales.getSpecialCtrl())) {
                        BigDecimal once = null, thirty = null;
                        if (Objects.nonNull(specialOnceLimit)) {
                            if (applyQty.compareTo(specialOnceLimit) > 0) {
                                once = specialOnceLimit;
                                applyQty = specialOnceLimit;
                            }
                        }
                        if (Objects.nonNull(specialThirtyDaysLimit)) {
                            if (applyQty.add(sales.getSpecialThirtyDaysQty()).compareTo(specialThirtyDaysLimit) > 0) {
                                thirty = applyQty.subtract(sales.getSpecialThirtyDaysQty().add(applyQty).subtract(specialThirtyDaysLimit));
                                applyQty = thirty;
                            }
                        }
                        if (Objects.nonNull(once) && Objects.nonNull(thirty)) {
                            applyQty = RecalculationStoreApplyServiceImpl.min(once, thirty);
                        }
                        // 如果是中包装商品 需要是中包装的整数倍
                        if (applyQty.compareTo(oldApplyQty) != 0) {
                            BigDecimal[] bigDecimals = applyQty.divideAndRemainder(middleQty);
                            applyQty = applyQty.subtract(bigDecimals[1]);
                        }
                    }
                    IscmRecalculationRecord record = new IscmRecalculationRecord();
                    List<String> splitCompanyCodes = Arrays.stream(StringUtils.split(splitCompanys, ",")).collect(Collectors.toList());
                    if(splitCompanyCodes.contains(company.getSapcode()) && "1202".equals(sales.getMiddleCategoryId().toString())) {
                        record.setApplyNo(zyApplyNo);
                    } else {
                        record.setApplyNo(applyNo);
                    }
                    record.setCategoryId(sales.getSubCategoryId());
                    record.setApplyRatio(sales.getApplyRatio());
                    record.setBdpApplyNo(bdpApplyNo);
                    record.setApplyLine(aInt.incrementAndGet() + "");
                    record.setCompanyOrgId(company.getId());
                    record.setBusinessId(company.getOutId());
                    record.setCompanyOrgName(company.getShortName());
                    record.setCompanyCode(company.getSapcode());
                    record.setStoreOrgId(store.getId());
                    record.setStoreId(store.getOutId());
                    record.setStoreCode(store.getSapcode());
                    record.setStoreName(store.getShortName());
                    record.setApplyDate(new Date());
                    record.setDataOriginType((byte) 3);
                    record.setGoodsNo(sales.getGoodsNo());
                    record.setApplyGoodsType(ApplyGoodsTypeEnum.NORMAL_GOODS.getCode());
                    record.setBdpApplyTotal(BigDecimal.ZERO);
                    record.setVarA(BigDecimal.ZERO);
                    record.setVarB(BigDecimal.ZERO);
                    if (null != ComMiddleSwitchEnum.getEnumByCode(sales.getMiddlePackageSwitchBiz())) {
                        record.setMiddlePackageSwitch(ComMiddleSwitchEnum.getEnumByCode(sales.getMiddlePackageSwitchBiz()).getCode());
                    }
                    if (null != ComMiddleSwitchEnum.getEnumByCode(sales.getMiddlePackageSwitchStore())) {
                        record.setMiddlePackageSwitch(ComMiddleSwitchEnum.getEnumByCode(sales.getMiddlePackageSwitchStore()).getCode());
                    }
                    record.setMiddlePackageQty(sales.getMiddlePackageQty());
                    if (!sales.getMiddleCategoryId().toString().equals("1202")) {
                        record.setApplyTotal(applyQty.setScale(0, RoundingMode.UP));
                    } else {
                        record.setApplyTotal(applyQty);
                    }
                    record.setBdpAverageDailySales(sales.getAverageDailySales());
                    record.setMinDisplayQty(sales.getMinDisplayQty());
                    record.setStockUpperLimit(sales.getStockUpperLimit());
                    record.setStockLowerLimit(sales.getStockLowerLimit());
                    record.setSaleDaysBefore(BigDecimal.ZERO);
                    record.setSaleDaysAfter(BigDecimal.ZERO);
                    record.setPurchaseType(0);// 0 不存在
                    record.setPurchaseChannel("");// 不存在
                    record.setSpecialCtrl(sales.getSpecialCtrl() == YNEnum.NO.getType().byteValue() ? YNEnum.NO.getDesc() : YNEnum.YES.getDesc());
                    record.setSpecialThirtyDaysQty(sales.getSpecialThirtyDaysQty());
                    record.setWarehouseCode("");
                    StringBuilder applyReason = new StringBuilder();
                    if (record.getStockLowerLimit().compareTo(record.getMinDisplayQty()) > 0) {
                        if (!scarce) {
                            if (record.getStockUpperLimit().compareTo(record.getMinDisplayQty()) > 0){
                                applyReason.append(RecalculationReasonEnum.REASON_ONE.getCode());
                            } else {
                                applyReason.append(RecalculationReasonEnum.REASON_TWO.getCode());
                            }
                        } else {
                            applyReason.append(RecalculationReasonEnum.REASON_THREE.getCode());
                        }
                    } else {
                        if (!scarce) {
                            if (record.getStockUpperLimit().compareTo(record.getMinDisplayQty()) > 0){
                                applyReason.append(RecalculationReasonEnum.REASON_FOUR.getCode());
                            } else {
                                applyReason.append(RecalculationReasonEnum.REASON_FIVE.getCode());
                            }
                        } else {
                            applyReason.append(RecalculationReasonEnum.REASON_SIX.getCode());
                        }
                    }
                    if (middleAble) {
                        applyReason.append("," + RecalculationReasonEnum.REASON_TEN.getCode());
                    }
                    record.setApplyStock(useStock);
                    record.setScarceLimit(scarceLimit);
                    record.setApplyReason(applyReason.toString());
                    records.add(record);
                }
                if (Long.valueOf(i * pageSize) >= count) {
                    break;
                }
            }
            if (CollectionUtils.isNotEmpty(records)) {
                Lists.partition(records, Constants.BATCH_INSERT_ONCE_MAX_VALUE).forEach(v -> iscmRecalculationRecordExtendMapper.batchInsert(v));
            }
            return records;
        } catch (Exception e) {
            logger.error("日均销复算门店:"+ store.getSapcode() + "使用日均销复算失败", e);
            throw e;
        }
    }

    @Override
    public CommonRes<String> manualStoreApply(String companyCode, String storeNo) {
        String lockKey = "ISCM-MANUALSTOREAPPLY-" + storeNo;
        RBucket<Boolean> bucket = redissonClient.getBucket(lockKey);
        if (bucket.isExists() && Boolean.FALSE.equals(bucket.get())) {
            logger.info("门店:{}正在计算中", storeNo);
            throw new AmisBadRequestException(ErrorCodeEnum.STORE_APPLY_CALCULATING);
        }
        CommonRes<String> commonRes = CommonRes.OK();
        commonRes.setData(lockKey);
        Boolean autoApply = healthcareBizCpService.checkStoreAutoApply(storeNo);
        if (autoApply) {
            logger.info("门店:{}未开启手工补货", storeNo);
            commonRes = CommonRes.ERROR(ErrorCodeEnum.STORE_NOT_MANUAL);
            return commonRes;
        }
        bucket.set(false, 10L, TimeUnit.MINUTES);
        executor.execute(() -> {
            PageResponse<List<StoreApplyInfoDTO>> producerRes = new PageResponse<>();
            producerRes.setResult(new ArrayList<>());
            producerRes.setTotalSize(0L);
            producerRes.setCode(ErrorCodeEnum.SUCCESS.hashCode());
            try {
                Optional<OrgVO> any = permissionService.queryOrgInfoBySapCodes(Lists.newArrayList(storeNo), OrgTypeEnum.ORG_TYPE_STORE.getCode()).stream().findAny();
                if (!any.isPresent()) {
                    producerRes.setCode(Integer.valueOf(ErrorCodeEnum.GET_PERM_ERROR.getCode()));
                    producerRes.setMessage(ErrorCodeEnum.GET_PERM_ERROR.getMsg());
                    return;
                }
                OrgVO store = any.get();
                logger.info("手工请货门店:{}复算开始", store);
                OrgVO company = permissionService.queryOrgInfoBySapCodes(Lists.newArrayList(companyCode), OrgTypeEnum.ORG_TYPE_BUSINESS.getCode()).stream().findFirst().orElseThrow(() -> new BusinessErrorException("日均销复算企业:" + companyCode + "没有查询到企业信息"));
                List<MdmStoreBaseDTO> stores = storeService.findStoreInfoAndExtendByStoreNos(Lists.newArrayList(storeNo));
                if (CollectionUtils.isEmpty(stores)) {
                    logger.info("日均销复算企业:{}今天没有需要请货的门店", companyCode);
                    producerRes.setCode(Integer.valueOf(ErrorCodeEnum.GET_PERM_ERROR.getCode()));
                    producerRes.setMessage(ErrorCodeEnum.GET_PERM_ERROR.getMsg());
                    return;
                }
                // 取平台
                List<OrgDTO> orgDTOS = permissionService.listDirectParentOrgByOrgId(company.getId());
                Optional<OrgDTO> platform = orgDTOS.stream().filter(v -> v.getType().equals(OrgTypeEnum.ORG_TYPE_REGIONAL_PLATFORM.getCode())).findAny();
                List<String> blackStoreCodes = new ArrayList<>();
                List<Byte> goodsLines = new ArrayList<>();
                List<String> goodsClasses = new ArrayList<>();
                List<String> goodsSigns = new ArrayList<>();
                getNonApplyParam(companyCode, OrgTypeEnum.ORG_TYPE_BUSINESS.getCode(), blackStoreCodes, goodsLines, goodsClasses, goodsSigns);
                StoreApplyParamDTO autoApplyList = storeApplyParamService.getAutoApplyList(company.getId(), null, ApplyParamTypeEnum.MIDDLE_PACKAGE_GOODS_APPLY.getCode(), null, null);
                StoreApplyParamDTO paramDTO = storeApplyParamService.getAutoApplyList(company.getId(), null, ApplyParamTypeEnum.AUTO_APPLY.getCode(), null, null);
                StoreApplyParamDTO middleApplyList = storeApplyParamService.getAutoApplyList(company.getId(), null, ApplyParamTypeEnum.APPLY_MIDDLE_CATEGORY.getCode(), null, null);
                List<AutoApplyParamDTO> specialGoodsCtrlDTOS = paramDTO.getSpecialGoodsCtrlDTOS();
                BigDecimal specialOnceLimit = null;
                BigDecimal specialThirtyDaysLimit = null;
                logger.info("specialGoodsCtrlDTOS:{}", JSON.toJSONString(specialGoodsCtrlDTOS));
                if (CollectionUtils.isNotEmpty(specialGoodsCtrlDTOS)) {
                    if ("true".equals(specialGoodsCtrlDTOS.stream().filter(v -> v.getParamCode().equals(ApplyParamCodeEnum.SPECIAL_CTRL_GOODS_SWITCH.getCode())).findFirst().orElse(new AutoApplyParamDTO()).getParamValue())) {
                        AutoApplyParamDTO specialOnceLimitDTO = specialGoodsCtrlDTOS.stream().filter(v -> v.getParamCode().equals(ApplyParamCodeEnum.SPECIAL_GOODS_ONCE_APPLY_LIMIT.getCode())).findFirst().orElse(null);
                        if (Objects.nonNull(specialOnceLimitDTO)) {
                            specialOnceLimit = new BigDecimal(specialOnceLimitDTO.getParamValue());
                        }
                        AutoApplyParamDTO specialThirtyDaysLimitDTO = specialGoodsCtrlDTOS.stream().filter(v -> v.getParamCode().equals(ApplyParamCodeEnum.SPECIAL_GOODS_PER_THIRTY_DAYS_APPLY_LIMIT.getCode())).findFirst().orElse(null);
                        if (Objects.nonNull(specialThirtyDaysLimitDTO)) {
                            specialThirtyDaysLimit = new BigDecimal(specialThirtyDaysLimitDTO.getParamValue());
                        }
                    }
                }
                List<IscmRecalculationRecord> iscmRecalculationRecords = recalculationStoreApplyService.recalculationManualByStore(platform, company, storeNo, autoApplyList, specialOnceLimit, specialThirtyDaysLimit);
                if (CollectionUtils.isEmpty(iscmRecalculationRecords)) {
                    logger.info("日销复算开始");
                    iscmRecalculationRecords.addAll(recalculationManualByStore(platform, company, store, autoApplyList, goodsLines, goodsClasses, goodsSigns, specialOnceLimit, specialThirtyDaysLimit, middleApplyList));
                }
                producerRes.setTotalSize(iscmRecalculationRecords.size());
                Lists.partition(iscmRecalculationRecords, jmApplyMax).forEach(part -> {
                    producerRes.setResult(part.stream().map(v -> {
                        StoreApplyInfoDTO dto = new StoreApplyInfoDTO();
                        dto.setGoodsNo(v.getGoodsNo());
                        dto.setApplyStock(null == v.getApplyStock() ? "" : v.getApplyStock().toPlainString());
                        dto.setApplyTotal(v.getApplyTotal().toPlainString());
                        return dto;
                    }).collect(Collectors.toList()));
                    // 发送
                    producerRes.setMessage(lockKey);
                    manualApplyProducer.send(producerRes);
                });
            } catch (Exception e) {
                logger.error("手工请货失败", e);
                producerRes.setCode(Integer.valueOf(ErrorCodeEnum.SYSTEM_ERROR.getCode()));
                producerRes.setMessage(ErrorCodeEnum.SYSTEM_ERROR.getMsg());
            } finally {
                bucket.set(true, 1L, TimeUnit.MINUTES);
            }
        });
        return commonRes;
    }

    @Override
    public void bdpCallbackDealAvg(String companyCode, String storeCodeStr) {
        List<String> storeCodeList = new ArrayList<>();
        if (StringUtils.isNotBlank(storeCodeStr)) {
            storeCodeList.addAll(Arrays.stream(StringUtils.split(storeCodeStr.trim(), ",")).distinct().collect(Collectors.toList()));
        }
        List<String> storeCodes = bdpPushAvgDailySalesExtendMapper.getAllStoreCode(companyCode, storeCodeList);
        if (CollectionUtils.isEmpty(storeCodes)) {
            logger.info("今日日均销数据为空");
            return;
        }
        List<OrgVO> stores = new ArrayList<>();
        Lists.partition(storeCodes, Constants.FEIGN_FOREST_SERVICE_ONCE_MAX_VALUE).forEach(v -> stores.addAll(permissionService.queryOrgInfoBySapCodes(v, OrgTypeEnum.ORG_TYPE_STORE.getCode())));
        if (CollectionUtils.isEmpty(stores)) {
            logger.info("今日日均销门店没有查询到组织信息");
            return;
        }
        executor.execute(() -> {
            stores.forEach(v -> {
                if (null != v.getOutId()) {
//                    boolean send = dealBdpAvgSalesProducer.send(v);
//                    if (!send) {
//                        logger.info("日均销门店:{}发送mq失败", v);
//                    }
                    consumerBdpAvgSales(v);
                }
            });
            logger.info("日均销门店生产者发送完毕");
        });
    }

    @Override
    public void consumerBdpAvgSales(OrgVO store) {
        try{
            logger.info("日均销门店:{}开始消费", store.getSapcode());
            BdpPushAvgDailySalesExample pushExample = new BdpPushAvgDailySalesExample();
            pushExample.createCriteria().andStoreCodeEqualTo(store.getSapcode());
            pushExample.setOrderByClause(" id asc");
            pushExample.setLimit(Constants.BATCH_INSERT_ONCE_MAX_VALUE);
            long count = bdpPushAvgDailySalesMapper.countByExample(pushExample);
            List<Long> ids = tocService.getDistributedIDList(IdGenConfig.IDGEN_BDP_AVG_DAILY_SALES, (int) count + 1);
            AtomicInteger idIndex = new AtomicInteger(0);
            for (int i = 0;; i++) {
                pushExample.setOffset(Long.valueOf(i * Constants.BATCH_INSERT_ONCE_MAX_VALUE));
                List<BdpPushAvgDailySales> bdpPushAvgDailySales = bdpPushAvgDailySalesMapper.selectByExample(pushExample);
                if (CollectionUtils.isEmpty(bdpPushAvgDailySales)) {
                    logger.info("今日日均销数据,门店:{}第:{}次循环为空,完毕", store.getSapcode(), i);
                    break;
                }
                //先删后增
                BdpAvgDailySalesExample example = new BdpAvgDailySalesExample();
                example.createCriteria().andStoreIdEqualTo(store.getOutId()).andGoodsNoIn(bdpPushAvgDailySales.stream().map(BdpPushAvgDailySales::getGoodsNo).distinct().collect(Collectors.toList()));
                bdpAvgDailySalesMapper.deleteByExample(example);
                bdpAvgDailySalesExtendMapper.batchInsert(bdpPushAvgDailySales.stream().map(v -> {
                    BdpAvgDailySales sales = new BdpAvgDailySales();
                    BeanUtils.copyProperties(v, sales);
                    sales.setId(ids.get(idIndex.getAndIncrement()));
                    sales.setStoreOrgId(store.getId());
                    sales.setStoreId(store.getOutId());
                    return sales;
                }).collect(Collectors.toList()));
                if (bdpPushAvgDailySales.size() < Constants.BATCH_INSERT_ONCE_MAX_VALUE) {
                    logger.info("今日日均销数据,门店:{}第:{}次循环小于分页数量,完毕", store.getSapcode(), i);
                    break;
                }
            }
            logger.info("日均销门店:{}消费完毕", store.getSapcode());
        } catch (Exception e) {
            logger.error("日均销门店:"+store.getSapcode()+"消费失败", e);
        }
    }

    @Override
    public void refreshStoreAttr(List<String> jmdStoreCodes) {
        Lists.partition(jmdStoreCodes, 50).forEach(codes -> {
            List<OrgVO> jmdStoreOrgVOS = permissionService.queryOrgInfoBySapCodes(codes);
            jmdStoreOrgVOS.forEach(store -> {
                bdpAvgDailySalesExtendMapper.updateStoreAttr(store.getOutId(), StoreAttrEnum.JM.getCode());
            });
        });
        logger.info("刷新完成");
    }

    @Override
    public List<BdpAvgDailySalesDTO> getList(AvgSalesQueryDTO param) {
        if (null == param.getStoreId()) {
            throw new BusinessErrorException("请选择门店");
        }
        if (CollectionUtils.isEmpty(param.getGoodsNos())) {
            return new ArrayList<>();
        }
        BdpAvgDailySalesExample example = new BdpAvgDailySalesExample();
        example.createCriteria().andStoreIdEqualTo(param.getStoreId()).andGoodsNoIn(param.getGoodsNos());
        return bdpAvgDailySalesMapper.selectByExample(example).stream().map(v -> {
            BdpAvgDailySalesDTO dto = new BdpAvgDailySalesDTO();
            BeanUtils.copyProperties(v, dto);
            dto.setLawfulDesc(YNEnum.getName(v.getLawful().intValue()));
            dto.setNewableDesc(YNEnum.getName(v.getNewable().intValue()));
            dto.setGoodsStatusDesc(YNEnum.getName(v.getGoodsStatus().intValue()));
            dto.setDistrForbidDesc(YNEnum.getName(v.getDistrForbid().intValue()));
            dto.setApplyForbidDesc(YNEnum.getName(v.getApplyForbid().intValue()));
            dto.setSpecialCtrlDesc(YNEnum.getName(v.getSpecialCtrl().intValue()));
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     *
     * @param companyOrgId
     * @return key 是否是门店级的参数
     * @throws Exception
     */
    private IscmStoreApplyParamGoodsOrgValidBlacklistExample getBlackExample(Optional<OrgDTO> platform, Long companyOrgId, String storeCode) throws Exception {
        IscmStoreApplyParamGoodsOrgValidBlacklistExample example = getBlackExampleByIdAndType(companyOrgId, companyOrgId, OrgTypeEnum.ORG_TYPE_BUSINESS.getCode(), storeCode);
        if (null != example) {
            return example;
        }
        if (platform.isPresent()) {
            return getBlackExampleByIdAndType(platform.get().getId(), companyOrgId, OrgTypeEnum.ORG_TYPE_REGIONAL_PLATFORM.getCode(), storeCode);
        }
        return null;
    }

    private IscmStoreApplyParamGoodsOrgValidBlacklistExample getBlackExampleByIdAndType(Long orgId, Long companyOrgId, Integer orgType, String storeCode){
        IscmStoreApplyParamGoodsOrgValidBlacklistExample example = new IscmStoreApplyParamGoodsOrgValidBlacklistExample();
        example.createCriteria().andOrgIdEqualTo(orgId).andParamLevelEqualTo(orgType).andStartDateLessThanOrEqualTo(DateUtils.dealDateTimeStart(new Date())).andEndDateGreaterThan(new Date());
        long count = iscmStoreApplyParamGoodsOrgValidBlacklistMapper.countByExample(example);
        if (count > 0L) {
            example.clear();
            IscmStoreApplyParamGoodsOrgValidBlacklistExample.Criteria criteria = example.createCriteria();
            criteria.andOrgIdEqualTo(orgId).andParamLevelEqualTo(orgType).andStoreLeveEqualTo(OrgTypeEnum.ORG_TYPE_STORE.getCode()).andStartDateLessThanOrEqualTo(DateUtils.dealDateTimeStart(new Date())).andEndDateGreaterThan(new Date()).andStoreCodeEqualTo(storeCode);
            IscmStoreApplyParamGoodsOrgValidBlacklistExample.Criteria criteria1 = example.createCriteria();
            criteria1.andOrgIdEqualTo(orgId).andParamLevelEqualTo(orgType).andStoreOrgIdEqualTo(companyOrgId).andStoreLeveEqualTo(OrgTypeEnum.ORG_TYPE_BUSINESS.getCode()).andStartDateLessThanOrEqualTo(DateUtils.dealDateTimeStart(new Date())).andEndDateGreaterThan(new Date());
            example.or(criteria1);
            count = iscmStoreApplyParamGoodsOrgValidBlacklistMapper.countByExample(example);
            if (count > 0L) {
                return example;
            }
//            if (count > 0L) {
//                criteria.andStoreCodeEqualTo(storeCode);
//                return example;
//            }
//            example.clear();
//            example.createCriteria().andOrgIdEqualTo(orgId).andParamLevelEqualTo(orgType).andStoreOrgIdEqualTo(companyOrgId).andStoreLeveEqualTo(OrgTypeEnum.ORG_TYPE_BUSINESS.getCode()).andStartDateLessThanOrEqualTo(DateUtils.dealDateTimeStart(new Date()));
//            count = iscmStoreApplyParamGoodsOrgValidBlacklistMapper.countByExample(example);
//            if (count > 0L) {
//                return example;
//            }
        }
        return null;
    }


}

package com.cowell.iscm.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.iscm.config.Constants;
import com.cowell.iscm.config.IdGenConfig;
import com.cowell.iscm.entity.*;
import com.cowell.iscm.entityTidb.*;
import com.cowell.iscm.enums.*;
import com.cowell.iscm.mapper.*;
import com.cowell.iscm.mapper.extend.IscmRecalculationNoResultLogExtendMapper;
import com.cowell.iscm.mapper.extend.IscmRecalculationRecordExtendMapper;
import com.cowell.iscm.mapper.extend.IscmStoreApplyParamGoodsOrgValidWhitelistExtendMapper;
import com.cowell.iscm.mapper.extend.IscmStoreApplyParamGoodsValidWhitelistExtendMapper;
import com.cowell.iscm.mapperTidb.IscmApplyGoodsMultipleCodeMapper;
import com.cowell.iscm.mapperTidb.IscmApplyGoodsReplaceCodeMapper;
import com.cowell.iscm.mapperTidb.IscmBdpApplyInfoMapper;
import com.cowell.iscm.mapperTidb.IscmCompanyApplyDateMapper;
import com.cowell.iscm.mapperTidb.extend.IscmBdpApplyInfoExtendMapper;
import com.cowell.iscm.mq.producer.RecalculationStoreApplyProducer;
import com.cowell.iscm.rest.errors.BusinessErrorException;
import com.cowell.iscm.rest.util.MailTools;
import com.cowell.iscm.service.AvgSalesRecalculationService;
import com.cowell.iscm.service.RecalculationStoreApplyService;
import com.cowell.iscm.service.RetryService;
import com.cowell.iscm.service.StoreApplyParamService;
import com.cowell.iscm.service.dto.CompanyCommonDTO;
import com.cowell.iscm.service.dto.ListToExcelMultiSheetDTO;
import com.cowell.iscm.service.dto.StoreCommonDTO;
import com.cowell.iscm.service.dto.TocCallbackParam;
import com.cowell.iscm.service.dto.applyParam.*;
import com.cowell.iscm.service.feign.*;
import com.cowell.iscm.service.feign.dto.*;
import com.cowell.iscm.service.feign.response.PageResponse;
import com.cowell.iscm.utils.DateUtils;
import com.cowell.iscm.utils.HutoolUtil;
import com.cowell.iscm.utils.MBUtils;
import com.cowell.permission.dto.OrgDTO;
import com.cowell.permission.dto.mdm.MdmStoreBaseDTO;
import com.cowell.permission.vo.OrgVO;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 复算门店请货
 * <AUTHOR>
 */
@Service
public class RecalculationStoreApplyServiceImpl implements RecalculationStoreApplyService {

    private final Logger logger = LoggerFactory.getLogger(RecalculationStoreApplyServiceImpl.class);

    @Autowired
    private IscmBdpApplyInfoMapper iscmBdpApplyInfoMapper;

    @Autowired
    private IscmBdpApplyInfoExtendMapper iscmBdpApplyInfoExtendMapper;

    @Autowired
    private IscmRecalculationRecordMapper iscmRecalculationRecordMapper;

    @Autowired
    private IscmRecalculationRecordExtendMapper  iscmRecalculationRecordExtendMapper;

    @Autowired
    private IscmRecalculationNoResultLogExtendMapper iscmRecalculationNoResultLogExtendMapper;

    @Autowired
    private IscmRecalculationNoResultLogMapper iscmRecalculationNoResultLogMapper;

    @Value("${iscm.recalculation.query.pagesize:500}")
    private Integer pageSize;

    @Value("${iscm.mb.recalculation.properties:}")
    private String recalculationPrpo;

    @Value("${iscm.mb.recalculation.properties_bak:}")
    private String recalculationPrpoBak;

    @Value("${iscm.mb.hd.hot.return.properties:}")
    private String mbHotReturnProperties;

    @Value("${iscm.mb.push.wms.properties:}")
    private String pushwmsPrpo;

    // 推送wms的dc编码 多个用,隔开
    @Value("${iscm.mb.push.wms.dccodes:}")
    private String pushwmsDcCodes;

    /**
     * 需要复算的公司编码用,分割,如果为空 则都要复算
     */
    @Value("${iscm.recalculation.company:}")
    private String recalculationCompany;

    @Autowired
    private StockcenterService stockcenterService;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private RetryService retryService;

    @Autowired
    private StoreApplyParamService storeApplyParamService;

    @Autowired
    private StoreService storeService;

    @Autowired
    private TagService tagService;

    @Autowired
    private IscmApplyGoodsMultipleCodeMapper iscmApplyGoodsMultipleCodeMapper;

    @Autowired
    private IscmApplyGoodsReplaceCodeMapper iscmApplyGoodsReplaceCodeMapper;

    @Autowired
    private IscmRecalculationWarnRecordMapper iscmRecalculationWarnRecordMapper;

    @Autowired
    private IscmRecalculationRoundCountMapper iscmRecalculationRoundCountMapper;

    @Autowired
    private IscmCompanyApplyDateMapper iscmCompanyApplyDateMapper;

    @Autowired
    private IscmStoreApplyParamGoodsOrgValidWhitelistMapper iscmStoreApplyParamGoodsOrgValidWhitelistMapper;

    @Autowired
    private IscmStoreApplyParamGoodsOrgValidWhitelistExtendMapper iscmStoreApplyParamGoodsOrgValidWhitelistExtendMapper;

    @Autowired
    private IscmStoreApplyParamGoodsValidWhitelistExtendMapper iscmStoreApplyParamGoodsValidWhitelistExtendMapper;

    @Autowired
    private RecalculationStoreApplyProducer producer;

    @Autowired
    private TocService tocService;

    @Autowired
    private MBUtils mbUtils;

    /**
     * 复算推送海典开关 true or false
     */
    @Value("${iscm.recalculation.push.hd.switch:true}")
    private Boolean pushHdSwitch;

    /**
     * 复算推送wms开关 true or false
     */
    @Value("${iscm.recalculation.push.wms.switch:true}")
    private Boolean pushWmsSwitch;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    @Qualifier("taskExecutor")
    private AsyncTaskExecutor executor;

    /**
     * 复算延迟分钟数
     */
    @Value("${iscm.recalculation.delayMinutes:0}")
    private Integer delayMinutes;

    /**
     * 复算计划到货时间距请货日的延迟天数
     */
    @Value("${iscm.recalculation.planTimeDelayDays:7}")
    private Integer planTimeDelayDays;

    /**
     * searchapi最大查询数量
     */
    @Value("${iscm.recalculation.searchPage:400}")
    private Integer searchPage;

    /**
     * 企业轮询最大次数
     */
    @Value("${iscm.recalculation.round.count.max:12}")
    private Integer maxRoundCount;

    /**
     * 轮询延迟分钟数
     */
    @Value("${iscm.recalculation.round.delayMinutes:5}")
    private Integer roundDelayMinutes;

    /**
     * 复算监控库存日志开关 true or false
     */
    @Value("${iscm.recalculation.monitor.stock.switch:true}")
    private Boolean monitorStockSwitch;


    /**
     * 复算监控库存企业列表 ,分割
     */
    @Value("${iscm.recalculation.monitor.stock.companys:}")
    private String monitorStockCompanys;

    /**
     * 复算拆单企业列表 ,分割
     */
    @Value("${iscm.recalculation.split.companys:}")
    private String splitCompanys;


    @Autowired
    private MailTools mailTools;

    @Value("${iscm.recalculation.warn.wxrobot.url:https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b3dbe8c2-60bf-404e-bafd-18a7c1167ec1}")
    private String wxRobotUrl;

    /**
     * 企业占比 企业编码:占比 多个,分割
     */
    @Value("${iscm.recalculation.company.rate:3110:50}")
    private String companyRate;

    /**
     * 企业配送削减配置 企业编码:1配|2配 多个,分割 forexample 3110:1|2|3,3050:1
     */
    @Value("${iscm.recalculation.company.deliverycycles:}")
    private String companyDeliverycycles;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private AvgSalesRecalculationService avgSalesRecalculationService;

    private static final String recalculationKey = "ISCM_RECALCULATION-";

    @Override
    public void recalculationByCompanyCode(String companyCode) throws Exception {
        RBucket<String> bucket = null;
        List<String> errorStoreCodes = new ArrayList<>();
        try {
            String cacheKey =  recalculationKey + companyCode + "-" + DateUtils.conventDateStrByDate(new Date(), DateUtils.DATE_SAP_PATTERN);
            bucket = redissonClient.getBucket(cacheKey);
            if (bucket.isExists()) {
                logger.info("企业:{}今天:{}已有复算任务", companyCode, DateUtils.conventDateStrByDate(new Date(), DateUtils.DATE_SAP_PATTERN));
                return;
            } else {
                bucket.set(cacheKey, 1L, TimeUnit.DAYS);
            }
        } catch (Exception e) {
            logger.error("企业:{}复算缓存失败,查询数据库", companyCode, e);
            IscmRecalculationRecordExample example = new IscmRecalculationRecordExample();
            example.createCriteria().andApplyDateEqualTo(new Date()).andCompanyCodeEqualTo(companyCode);
            long count = iscmRecalculationRecordMapper.countByExample(example);
            if (count > 0L) {
                logger.info("企业:{}今天:{}已有复算任务", companyCode, DateUtils.conventDateStrByDate(new Date(), DateUtils.DATE_SAP_PATTERN));
                return;
            }
        }
        logger.info("企业:{}开始复算", companyCode);
        if (StringUtils.isNotBlank(recalculationCompany)) {
            String[] split = StringUtils.split(recalculationCompany, ",");
            boolean present = Arrays.stream(split).filter(v -> v.equals(companyCode)).findAny().isPresent();
            if (!present){
                logger.info("企业:{}没在apollo配置,不需要复算", companyCode);
                return;
            }
        }
        try {
            IscmBdpApplyInfoExample example = new IscmBdpApplyInfoExample();
            example.createCriteria().andApplyDateEqualTo(new Date()).andCompanyCodeEqualTo(companyCode);
            long companyCount = iscmBdpApplyInfoMapper.countByExample(example);
            if (companyCount <= 0L) {
                logger.info("企业:{}待复算数据为空, 开始查询请货日", companyCode);
                IscmCompanyApplyDateExample applyDateExample = new IscmCompanyApplyDateExample();
                applyDateExample.createCriteria().andApplyDateEqualTo(new Date()).andCompanyCodeEqualTo(companyCode);
                long applyDateCount = iscmCompanyApplyDateMapper.countByExample(applyDateExample);
                if (applyDateCount > 0L) {
                    logger.info("企业:{}是请货日且待复算数据为空, 开始轮询", companyCode);
                    createRecalculationRoundJob(companyCode);
                    return;
                } else {
                    logger.info("企业:{}不是请货日,直接返回", companyCode);
                    return;
                }
            } else {
                example.setLimit(1);
                Long companyApplyTotal = Optional.ofNullable(iscmBdpApplyInfoMapper.selectByExample(example).stream().findAny().get().getCompanyApplyTotal()).orElse(0L);
                if(companyApplyTotal > companyCount) {
                    logger.info("企业:{}待复算数据还未推完, 开始轮询", companyCode);
                    createRecalculationRoundJob(companyCode);
                    return;
                }
            }
            List<String> storeCodes = iscmBdpApplyInfoExtendMapper.getStoreCodesByCompanyCode(companyCode, new Date());
            if (CollectionUtils.isEmpty(storeCodes)) {
                logger.info("企业:{}没有需要复算的数据", companyCode);
//                insertWarnReport(companyCode, new ArrayList<>());
//                sendErrorMsg2Wechat(companyCode, new ArrayList<>());
                return;
            }
            OrgVO company = null;
            Optional<OrgDTO> platform = null;
            try {
                company = permissionService.queryOrgInfoBySapCodes(Lists.newArrayList(companyCode), OrgTypeEnum.ORG_TYPE_BUSINESS.getCode()).stream().filter(v -> StringUtils.isNotBlank(v.getSapcode())).findFirst().orElseThrow(() -> new BusinessErrorException("企业编码:" + companyCode + "没有查询到对应企业"));
                // 取平台
                List<OrgDTO> orgDTOS = permissionService.listDirectParentOrgByOrgId(company.getId());
                platform = orgDTOS.stream().filter(v -> v.getType().equals(OrgTypeEnum.ORG_TYPE_REGIONAL_PLATFORM.getCode())).findAny();
            } catch (Exception e) {
                OrgInfoBaseCache cache = permissionService.getOrgBaseCacheBySapCode(Lists.newArrayList(companyCode), OrgTypeEnum.ORG_TYPE_BUSINESS.getCode()).stream().findFirst().orElseThrow(() -> new BusinessErrorException("企业编码:" + companyCode + "缓存中没有查询到对应企业"));
                OrgVO orgVO = new OrgVO();
                orgVO.setId(cache.getBusinessOrgId());
                orgVO.setName(cache.getBusinessShortName());
                orgVO.setShortName(cache.getBusinessShortName());
                orgVO.setOrgPath(cache.getOrgPath());
                orgVO.setType(cache.getType());
                orgVO.setParentId(cache.getPlatformOrgId());
                orgVO.setOutId(cache.getBusinessId());
                orgVO.setSapcode(cache.getBusinessSapCode());
                company = orgVO;
                OrgDTO orgDTO = new OrgDTO();
                orgDTO.setId(cache.getPlatformOrgId());
                orgDTO.setShortName(cache.getPlatformShortName());
                platform = Optional.of(orgDTO);
            }
            StoreApplyParamDTO autoApplyList = storeApplyParamService.getAutoApplyList(company.getId(), null,ApplyParamTypeEnum.MIDDLE_PACKAGE_GOODS_APPLY.getCode(), null,null);
            StoreApplyParamDTO paramDTO = storeApplyParamService.getAutoApplyList(company.getId(),null, ApplyParamTypeEnum.AUTO_APPLY.getCode(), null,null);
            List<AutoApplyParamDTO> specialGoodsCtrlDTOS = paramDTO.getSpecialGoodsCtrlDTOS();
            BigDecimal specialOnceLimit = null;
            BigDecimal specialThirtyDaysLimit = null;
            logger.info("specialGoodsCtrlDTOS:{}", JSON.toJSONString(specialGoodsCtrlDTOS));
            if (CollectionUtils.isNotEmpty(specialGoodsCtrlDTOS)) {
                if ("true".equals(specialGoodsCtrlDTOS.stream().filter(v -> v.getParamCode().equals(ApplyParamCodeEnum.SPECIAL_CTRL_GOODS_SWITCH.getCode())).findFirst().orElse(new AutoApplyParamDTO()).getParamValue())){
                    AutoApplyParamDTO specialOnceLimitDTO = specialGoodsCtrlDTOS.stream().filter(v -> v.getParamCode().equals(ApplyParamCodeEnum.SPECIAL_GOODS_ONCE_APPLY_LIMIT.getCode())).findFirst().orElse(null);
                    if (Objects.nonNull(specialOnceLimitDTO)) {
                        specialOnceLimit = new BigDecimal(specialOnceLimitDTO.getParamValue());
                    }
                    AutoApplyParamDTO specialThirtyDaysLimitDTO = specialGoodsCtrlDTOS.stream().filter(v -> v.getParamCode().equals(ApplyParamCodeEnum.SPECIAL_GOODS_PER_THIRTY_DAYS_APPLY_LIMIT.getCode())).findFirst().orElse(null);
                    if (Objects.nonNull(specialThirtyDaysLimitDTO)) {
                        specialThirtyDaysLimit = new BigDecimal(specialThirtyDaysLimitDTO.getParamValue());
                    }
                } else {
                    logger.info("企业:" + companyCode + "没有开启特管商品请货控制.");
                }
            } else {
                logger.info("企业:" + companyCode + "没有配置特管商品请货控制.");
            }
            logger.info("specialOnceLimit:{}, specialThirtyDaysLimit:{}", specialOnceLimit, specialThirtyDaysLimit);
//            AutoApplyParamDTO combinedCodeRange = paramDTO.getCombinedCodeRange();
//            logger.info("combinedCodeRange:{}", JSON.toJSONString(combinedCodeRange));
//            CombinedCodeRangeEnum combinedCodeRangeEnum = CombinedCodeRangeEnum.getEnumByCode(null != combinedCodeRange ? combinedCodeRange.getParamValue() : null);
            for(String storeCode : storeCodes) {
                try {
//                    recalculationByStores(platform, company, storeCode, autoApplyList, specialOnceLimit, specialThirtyDaysLimit, combinedCodeRangeEnum);
                    recalculationByStores(platform, company, storeCode, autoApplyList, specialOnceLimit, specialThirtyDaysLimit);
                } catch (Exception e) {
                    logger.info("企业:" +company.getSapcode() + "门店:" + storeCode + "复算请货失败", e);
                    errorStoreCodes.add(storeCode);
                }
            }
            logger.info("errorStoreCodes:{}", errorStoreCodes);
            logger.info("企业:{}复算完毕", companyCode);
        } catch (Exception e) {
            logger.error("企业:" + companyCode + "复算门店请货失败:", e);
            try {
                insertWarnReport(companyCode, new ArrayList<>());
                sendErrorMsg2Wechat(companyCode, new ArrayList<>());
            } catch (Exception e1) {
                logger.error("企业:" + companyCode + "复算门店推送异常失败:", e1);
                throw e1;
            }
            throw e;
        } finally {
            if (Objects.nonNull(bucket)) {
                bucket.delete();
            }
            if (CollectionUtils.isNotEmpty(errorStoreCodes)) {
                insertWarnReport(companyCode, errorStoreCodes);
                sendErrorMsg2Wechat(companyCode, errorStoreCodes);
            }
        }
    }

    /**
     *
     * @return 白名单
     * @throws Exception
     */
    @Override
    public Map<String, Integer> getWhiteMap(Long companyOrgId, String storeCode) {
        List<OrgInfoBaseCache> storeCaches = permissionService.getOrgBaseCacheBySapCode(Lists.newArrayList(storeCode), OrgTypeEnum.ORG_TYPE_STORE.getCode());
        if (CollectionUtils.isEmpty(storeCaches)) {
            return new HashMap<>();
        }
        OrgInfoBaseCache store = storeCaches.get(0);
        List<WhiteGoodsNoListDTO> whiteGoodsNoListDTOS = getWhiteList(store.getId(), store);
        if (CollectionUtils.isNotEmpty(whiteGoodsNoListDTOS)) {
             return whiteGoodsNoListDTOS.stream().collect(Collectors.toMap(WhiteGoodsNoListDTO::getGoodsNo, WhiteGoodsNoListDTO::getUpperQuantity, (k1,k2) -> k2));
        }
        whiteGoodsNoListDTOS = getWhiteList(companyOrgId, store);
        if (CollectionUtils.isNotEmpty(whiteGoodsNoListDTOS)) {
            return whiteGoodsNoListDTOS.stream().collect(Collectors.toMap(WhiteGoodsNoListDTO::getGoodsNo, WhiteGoodsNoListDTO::getUpperQuantity, (k1,k2) -> k2));
        }
        return new HashMap<>();
    }
    private List<WhiteGoodsNoListDTO> getWhiteList(Long orgId, OrgInfoBaseCache store){
        List<WhiteGoodsNoListDTO> whiteGoodsNoListDTOS = iscmStoreApplyParamGoodsValidWhitelistExtendMapper.selectWhiteGoodsInfoByOrgId(orgId, DateUtils.dealDateTimeStart(new Date()), new Date());
        // 过滤配送周期
        if (StringUtils.isNotBlank(store.getSalesLevel()) && StringUtils.isNotBlank(store.getDeliveryCycle())) {
            String salesLevel = store.getSalesLevel().replace("类店", "");
            String deliveryCycle = store.getDeliveryCycle();

            whiteGoodsNoListDTOS.removeIf(v -> {
                List<String> storeTypeList = Arrays.stream(StringUtils.split(v.getStoreType(), ",")).collect(Collectors.toList());
                return !storeTypeList.contains(salesLevel + deliveryCycle); });
        }
        return whiteGoodsNoListDTOS;
    }

    private void createRecalculationRoundJob(String companyCode) {
        try {
            logger.info("企业:{}开始创建复算轮询任务", companyCode);
            IscmRecalculationRoundCountExample example = new IscmRecalculationRoundCountExample();
            example.createCriteria().andApplyDateEqualTo(new Date()).andCompanyCodeEqualTo(companyCode);
            List<IscmRecalculationRoundCount> roundCounts = iscmRecalculationRoundCountMapper.selectByExample(example);
            int count = 0;
            if (CollectionUtils.isNotEmpty(roundCounts)) {
                IscmRecalculationRoundCount roundCount = roundCounts.get(0);
                if (roundCount.getRoundCount() >= maxRoundCount) {
                    logger.info("企业:{}已达到轮询最大次数:{},次,依旧不满足复算条件,不再创建轮询复算任务,开始使用日均销复算", companyCode, maxRoundCount);
                    sendJobErrorMsg2Wechat(companyCode, "企业:" + companyCode +  "已达到轮询最大次数" +  maxRoundCount+ "次,依旧不满足复算条件,不再创建轮询复算任务");
//                    avgSalesRecalculationService.avgRecalculationByCompany(companyCode);
                    return;
                }
                count = roundCount.getRoundCount() + 1;
                roundCount.setRoundCount(count);
                iscmRecalculationRoundCountMapper.updateByPrimaryKeySelective(roundCount);
            } else {
                IscmRecalculationRoundCount roundCount = new IscmRecalculationRoundCount();
                roundCount.setApplyDate(new Date());
                roundCount.setCompanyCode(companyCode);
                count = 1;
                roundCount.setRoundCount(count);
                roundCount.setCreatedBy(0L);
                roundCount.setCreatedName("系统管理员");
                roundCount.setUpdatedBy(0L);
                roundCount.setUpdatedName("系统管理员");
                iscmRecalculationRoundCountMapper.insertSelective(roundCount);
            }
            List<OrgVO> orgVOS = new ArrayList<>();
            try {
                orgVOS.addAll(permissionService.queryOrgInfoBySapCodes(Lists.newArrayList(companyCode), OrgTypeEnum.ORG_TYPE_BUSINESS.getCode()));
            } catch (Exception e) {
                logger.error("需要轮询复算的企业没有在权限中查询到信息", e);
                sendJobErrorMsg2Wechat(companyCode, "企业:" + companyCode +  "开始创建复算轮询任务查询权限异常");
                return;
            }
            String callBackUrl = "http://iscm/api/internal/recalculation/store/apply/tocCallbackRecalculation";
            for (OrgVO orgVO : orgVOS) {
                Long tocId = getTocId(orgVO, DateUtils.DATE_MONTH_PATTERN_NOCONCAT);
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(new Date());
                calendar.add(Calendar.MINUTE, roundDelayMinutes);
                try {
                    tocService.tocTaskDelete(tocId, "Recalculation");
                } catch (Exception e) {
                    logger.error("删除轮询复算定时任务失败,直接创建", e);
                }
                try {
                    tocService.tocTaskPush(tocId, TocUnitEnum.MONTH, "Recalculation", DateUtils.conventDateStrByPattern(calendar.getTime(), DateUtils.DATETIME_PATTERN), callBackUrl, orgVO.getSapcode());
//                    sendJobErrorMsg2Wechat(companyCode, "企业:" + companyCode +  "已经创建第" + count + "次复算轮询任务");
                } catch (Exception e) {
                    logger.error("创建轮询定时任务异常", e);
                    sendJobErrorMsg2Wechat(orgVO.getSapcode(), "企业:" + companyCode + "创建轮询定时任务异常");
                }
            }
        } catch (Exception e) {
            logger.error("创建轮询复算定时任务失败", e);
            sendJobErrorMsg2Wechat(companyCode, "企业:" + companyCode + "创建轮询定时任务异常");
        }
    }

    private void insertWarnReport(String companyCode, List<String> errorStoreCodes){
        IscmRecalculationWarnRecord warnRecord = new IscmRecalculationWarnRecord();
        warnRecord.setCompanyCode(companyCode);
        warnRecord.setErrorStoreCodes(CollectionUtils.isEmpty(errorStoreCodes) ? "" : errorStoreCodes.stream().collect(Collectors.joining(",")));
        warnRecord.setApplyDate(new Date());
        warnRecord.setDealStatus(RecalculationDealStatusEnum.NO_DEAL.getCode());
        iscmRecalculationWarnRecordMapper.insertSelective(warnRecord);
    }

    private void sendErrorMsg2Wechat(String companyCode, List<String> errorStoreCodes) {
        StringBuilder content = new StringBuilder();
        content.append("【ISCM自动请货报警】\n")
                .append(">企业编码：<font color=\\\"info\\\">").append(companyCode).append("</font>\n ")
                .append(">告警名称：<font color=\\\"info\\\">请货异常</font>\n ")
                .append(">时间：<font color=\\\"info\\\">").append(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())).append("</font>\n");
        if(CollectionUtils.isNotEmpty(errorStoreCodes)) {
            content.append(">异常门店编码：<font color=\\\"info\\\">").append(errorStoreCodes.stream().collect(Collectors.joining(","))).append("</font>\n");
        }
        sendToWechat(content);
    }

    private void sendJobErrorMsg2Wechat(String companyCode, String errorMsg) {
        StringBuilder content = new StringBuilder();
        content.append("【ISCM自动请货报警】\n")
                .append(">企业编码：<font color=\\\"info\\\">").append(companyCode).append("</font>\n ")
                .append(">告警名称：<font color=\\\"info\\\">请货定时任务异常</font>\n ")
                .append(">时间：<font color=\\\"info\\\">").append(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())).append("</font>\n")
                .append(">异常原因：<font color=\\\"info\\\">").append(errorMsg).append("</font>\n");
        sendToWechat(content);
    }

    private void sendJobSuccessMsg2Wechat(String companyCode, String errorMsg) {
        StringBuilder content = new StringBuilder();
        content.append("【ISCM自动请货定时任务创建信息】\n")
                .append(">企业编码：<font color=\\\"#FF0000\\\">").append(companyCode).append("</font>\n ")
                .append(">提醒名称：<font color=\\\"#FF0000\\\">请货定时任务创建</font>\n ")
                .append(">时间：<font color=\\\"#FF0000\\\">").append(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())).append("</font>\n")
                .append(">信息：<font color=\\\"#FF0000\\\">").append(errorMsg).append("</font>\n");
        sendToWechat(content);
    }

    private void sendWmsErrorMsg2Wechat(String companyCode, String applyNo, String warehouseCode, String errorMsg) {
        StringBuilder content = new StringBuilder();
        content.append("【ISCM自动请货报警】\n")
                .append(">企业编码：<font color=\\\"info\\\">").append(companyCode).append("</font>\n ")
                .append(">请货单号：<font color=\\\"info\\\">").append(applyNo).append("</font>\n ");
        if(StringUtils.isNotBlank(warehouseCode)) {
            content.append(">DC编码：<font color=\\\"info\\\">").append(warehouseCode).append("</font>\n ");
        }
        content.append(">告警名称：<font color=\\\"info\\\">推送wms异常</font>\n ")
                .append(">时间：<font color=\\\"info\\\">").append(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())).append("</font>\n");
        if (StringUtils.isNotBlank(errorMsg)) {
            content.append(">异常原因：<font color=\\\"info\\\">").append(errorMsg).append("</font>\n");
        }
        sendToWechat(content);
    }


    private void sendToWechat(StringBuilder content) {
        try {
            String textTemplate = "{\n" +
                    "    \"msgtype\": \"markdown\",\n" +
                    "    \"markdown\": {\n" +
                    "        \"content\": \"" + content.toString() + "\",\n" +
                    "    }\n" +
                    "}";
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
            HttpEntity<String> request = new HttpEntity<>(textTemplate, headers);
            restTemplate.postForEntity(wxRobotUrl, request, String.class);
        } catch (Exception e) {
            logger.error("发送错误信息失败", e);
        }
    }

    public void recalculationByStores(Optional<OrgDTO> platform, OrgVO company, String storeCode, StoreApplyParamDTO autoApplyList, BigDecimal specialOnceLimit, BigDecimal specialThirtyDaysLimit) throws Exception {
        String companyCode = company.getSapcode();
        String applyNo = "BDP" + DateUtils.conventDateStrByDate(new Date(), DateUtils.DATE_SAP_PATTERN) + storeCode + DateUtils.conventDateStrByDate(new Date(), DateUtils.TIME_HHMM_PATTERN);
        String zyApplyNo = applyNo + "ZY";
        List<OrgVO> storeList = permissionService.queryOrgInfoBySapCodes(Lists.newArrayList(storeCode), OrgTypeEnum.ORG_TYPE_STORE.getCode());
        Map<String, Integer> whiteGoodsMap = getWhiteMap(company.getId(), storeCode);
        logger.info("whiteGoodsMap:{}", JSON.toJSONString(whiteGoodsMap));
        Integer rate = null;
        logger.info("companyRate:{}", companyRate);
        if (StringUtils.isNotBlank(companyRate)) {
            try {
                Optional<String> any = Arrays.stream(StringUtils.split(companyRate, ",")).filter(v -> v.contains(companyCode)).findAny();
                rate = any.isPresent() ? Integer.valueOf(any.get().split(":")[1]) : null;
                logger.info("rate:{}", rate);
            } catch (Exception e) {
                logger.error("企业:" + companyCode + "复算门店:" + storeCode+ "占比获取失败.", e);
            }
        }
        logger.info("companyDeliverycycles:{}", companyDeliverycycles);
        List<String> deliverycycles = new ArrayList<>();
        if (StringUtils.isNotBlank(companyDeliverycycles)) {
            try {
                Optional<String> any = Arrays.stream(StringUtils.split(companyDeliverycycles, ",")).filter(v -> v.contains(companyCode)).findAny();
                if (any.isPresent()) {
                    String deliverycycle = any.get().split(":")[1];
                    if (StringUtils.isNotBlank(deliverycycle)) {
                        deliverycycles.addAll(Arrays.stream(StringUtils.split(deliverycycle, "|")).collect(Collectors.toList()));
                    }
                }
            } catch (Exception e) {
                logger.error("企业:" + companyCode + "复算门店:" + storeCode+ "配送削减获取失败.", e);
            }
        }
        logger.info("deliverycycles:{}", deliverycycles);
        List<IscmRecalculationRecord> insertRecords = new ArrayList<>();
        for (int i = 0; ; i++) {
            IscmBdpApplyInfoExample example = new IscmBdpApplyInfoExample();
            example.createCriteria().andApplyDateEqualTo(new Date()).andCompanyCodeEqualTo(company.getSapcode()).andStoreCodeEqualTo(storeCode);
            example.setLimit(pageSize);
            example.setOffset(Long.valueOf(i * pageSize));
            example.setOrderByClause(" id asc");
            List<IscmBdpApplyInfo> applyInfos = iscmBdpApplyInfoMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(applyInfos)) {
                logger.info("企业:" + companyCode + "复算门店:" + storeCode+ "请货完毕.");
                break;
            }
            List<IscmRecalculationNoResultLog> error = new ArrayList<>();
            if (CollectionUtils.isEmpty(storeList)) {
                logger.warn("企业:{}下的门店编码:{},没有查询到对应的门店", companyCode, storeCode);
                error.addAll(applyInfos.stream().map(v -> {
                    IscmRecalculationNoResultLog log = new IscmRecalculationNoResultLog();
                    BeanUtils.copyProperties(v, log);
                    log.setReason(RecalculationNoResultEnum.NO_STORE.getCode().toString());
                    return log;
                }).collect(Collectors.toList()));
                continue;
            }
            OrgVO store = storeList.get(0);
            Map<String, List<StockGoodsBatchCodeSimpleInfo>> goodsStockMap = new HashMap<>();
            StockGoodsPagableQueryParam param = new StockGoodsPagableQueryParam();
            param.setBusinessId(company.getOutId());
            param.setStoreId(store.getOutId());
            // 不复算好品推荐
            List<String> goodsNos = applyInfos.stream().filter(v -> !v.getApplyGoodsType().equals(Byte.valueOf(ApplyGoodsTypeEnum.GOOD_SUGGEST_GOODS.getCode()))).map(IscmBdpApplyInfo::getGoodsNo).distinct().collect(Collectors.toList());
            // 中码组商品
            List<String> masterGoodsNos = applyInfos.stream().filter(v -> !v.getApplyGoodsType().equals(Byte.valueOf(ApplyGoodsTypeEnum.GOOD_SUGGEST_GOODS.getCode())) && YNEnum.YES.getType().byteValue() == v.getMiddleCodeFlag().byteValue()).map(IscmBdpApplyInfo::getGoodsNo).distinct().collect(Collectors.toList());
            // 主替换码码关系
            Map<String, List<IscmApplyGoodsReplaceCode>> masterToReplaceMap = new HashMap<>();
            // 替换码库存
            Map<String, List<StockGoodsBatchCodeSimpleInfo>> replaceGoodsStockMap = new HashMap<>();
            // 替换码数据
            Map<String, String> replaceGoodsNoMap = new HashMap<>();
            IscmBdpApplyInfo iscmBdpApplyInfo = applyInfos.get(0);
            if (CollectionUtils.isNotEmpty(goodsNos)) {
                List<List<String>> partition = Lists.partition(goodsNos, Constants.FEIGN_SEARCHAPI_SERVICE_ONCE_MAX_VALUE);
                for (List<String> goos : partition) {
                    param.setGoodsNos(goos);
                    goodsStockMap.putAll(stockcenterService.goodsBatchCodePage(param));
                }
                if (CollectionUtils.isNotEmpty(masterGoodsNos)) {
                    // 替换码
                    IscmApplyGoodsReplaceCodeExample replaceCodeExample = new IscmApplyGoodsReplaceCodeExample();
                    replaceCodeExample.createCriteria().andCompanyCodeEqualTo(companyCode).andMasterGoodsNoIn(masterGoodsNos);
                    List<IscmApplyGoodsReplaceCode> replaceList = iscmApplyGoodsReplaceCodeMapper.selectByExample(replaceCodeExample)
                            .stream().filter(v -> Objects.nonNull(v.getChangeRatio()) && StringUtils.isNotBlank(v.getMasterGoodsNo()) && StringUtils.isNotBlank(v.getReplaceGoodsNo()))
                            .filter(v -> !v.getMasterGoodsNo().equals(v.getReplaceGoodsNo())).collect(Collectors.toList());

                    IscmApplyGoodsReplaceCodeExample Example = new IscmApplyGoodsReplaceCodeExample();
                    Example.createCriteria().andCompanyCodeEqualTo(companyCode).andReplaceGoodsNoIn(masterGoodsNos);
                    replaceGoodsNoMap.putAll(iscmApplyGoodsReplaceCodeMapper.selectByExample(replaceCodeExample).stream().map(IscmApplyGoodsReplaceCode::getReplaceGoodsNo).collect(Collectors.toMap(v -> v, Function.identity(), (k1, k2) -> k1)));

                    if (CollectionUtils.isNotEmpty(replaceList)) {
                        List<List<IscmApplyGoodsReplaceCode>> replacePartition = Lists.partition(replaceList, Constants.FEIGN_SEARCHAPI_SERVICE_ONCE_MAX_VALUE);
                        for (List<IscmApplyGoodsReplaceCode> goos : replacePartition) {
                            param.setGoodsNos(goos.stream().map(IscmApplyGoodsReplaceCode::getReplaceGoodsNo).distinct().collect(Collectors.toList()));
                            replaceGoodsStockMap.putAll(stockcenterService.goodsBatchCodePage(param));
                        }
                        masterToReplaceMap.putAll(replaceList.stream().collect(Collectors.groupingBy(IscmApplyGoodsReplaceCode::getMasterGoodsNo)));
                    }
                }
            }
//                    复算请货数量=BDP请货数量+变量A+变量B
//                    1）变量取【BDP计算数量-复算时的数量】
//                    2）变量A： BDP计算时【变量1】- 复算时[总库存（Stock）-（合格品区锁定库存(WaitStock)+非合格品区库存（UnqualifiedAreaStock））]，来源：库存中台
//                    3）变量B：BDP计算时【变量2】-复算时总在途库存，来源：库存中台
            // 复算后不为0的集合
            List<IscmRecalculationRecord> records = new ArrayList<>();
            // 只复算常规品
            if (CollectionUtils.isNotEmpty(goodsNos)) {
                ItemBaseBatchQueryParamVO paramVo = new ItemBaseBatchQueryParamVO();
                paramVo.setBusinessId(company.getOutId());
                paramVo.setStoreId(store.getOutId());
                Lists.partition(goodsNos, searchPage).forEach(goods -> {
                    paramVo.setGoodsNoList(goods);
                });
            }
            for (IscmBdpApplyInfo apply : applyInfos) {
                StringBuilder applyReason = new StringBuilder();
                Integer scarceLimit = null;
//                if (replaceGoodsNoMap.containsKey(apply.getGoodsNo())) {
//                    logger.info("企业:{}门店:{}商品:{}子码不用复算,排除", companyCode, apply.getStoreCode(), apply.getGoodsNo());
//                    IscmRecalculationNoResultLog log = new IscmRecalculationNoResultLog();
//                    BeanUtils.copyProperties(apply, log);
//                    log.setReason("子码不用复算,排除");
//                    error.add(log);
//                    continue;
//                }
                if (ApplyGoodsTypeEnum.GOOD_SUGGEST_GOODS.getCode() == apply.getApplyGoodsType()) {
                    logger.info("企业:{}门店:{}商品:{}好品推荐不用复算", companyCode, apply.getStoreCode(), apply.getGoodsNo());
                    if (apply.getApplyTotal().compareTo(BigDecimal.ZERO) <= 0) {
                        logger.info("企业:{},businessId:{},storeId:{},商品:{},没有查询到库存信息", companyCode, company.getOutId(), param.getStoreId(), apply.getGoodsNo());
                        IscmRecalculationNoResultLog log = new IscmRecalculationNoResultLog();
                        BeanUtils.copyProperties(apply, log);
                        log.setReason(RecalculationNoResultEnum.GOODS_SUGGEST_ZERO.getCode().toString());
                        error.add(log);
                    } else {
                        if (ApplyGoodsTypeEnum.GOOD_SUGGEST_GOODS.getCode() == apply.getApplyGoodsType()) {
                            applyReason.append(RecalculationReasonEnum.REASON_NINE.getCode());
                        }
                        if (ApplyGoodsTypeEnum.LOW_SELL_GOODS.getCode() == apply.getApplyGoodsType()) {
                            applyReason.append(RecalculationReasonEnum.REASON_SEVEN.getCode());
                        }
                        if (ApplyGoodsTypeEnum.PROMOTION_GOODS.getCode() == apply.getApplyGoodsType()) {
                            applyReason.append(RecalculationReasonEnum.REASON_EIGHT.getCode());
                        }
                        genRecord(company, store, records, apply, BigDecimal.ZERO, BigDecimal.ZERO, apply.getApplyTotal(), applyNo, zyApplyNo,null, scarceLimit, applyReason.toString());
                    }
                    continue;
                }
                List<StockGoodsBatchCodeSimpleInfo> goodsStocks = goodsStockMap.get(apply.getGoodsNo());
                if (CollectionUtils.isEmpty(goodsStocks) ) {
                    logger.info("企业:{},businessId:{},storeId:{},商品:{},没有查询到库存信息", companyCode, company.getOutId(), param.getStoreId(), apply.getGoodsNo());
                    // 总库存-(合格品区锁定库存+非合格品区库存)+在途库存
                    BigDecimal bdpUseStock = apply.getStock().subtract(apply.getLockStock().add(apply.getUnqualifiedStock())).add(apply.getTransitStock());
                    BigDecimal applyQty = apply.getApplyTotal();
                    if (apply.getApplyTotal().compareTo(BigDecimal.ZERO) <= 0) {
                        IscmRecalculationNoResultLog log = new IscmRecalculationNoResultLog();
                        BeanUtils.copyProperties(apply, log);
                        log.setReason(RecalculationNoResultEnum.GOODS_NO_STOCK.getCode().toString());
                        error.add(log);
                    } else {
                        if (ApplyGoodsTypeEnum.NORMAL_GOODS.getCode() == apply.getApplyGoodsType() && bdpUseStock.compareTo(max(apply.getStockLowerLimit(), apply.getMinDisplayQty())) >= 0) {
                            logger.info("企业:{}门店:{}商品:{}总库存-(合格品区锁定库存+非合格品区库存)+在途库存 >= max(库存下限,最小陈列量)", companyCode, apply.getStoreCode(), apply.getGoodsNo());
                            IscmRecalculationNoResultLog log = new IscmRecalculationNoResultLog();
                            BeanUtils.copyProperties(apply, log);
                            log.setReason(RecalculationNoResultEnum.BDP_NON_NECESSARY_APPLY.getCode().toString());
                            log.setApplyStock(bdpUseStock);
                            error.add(log);
                        } else {
                            if (whiteGoodsMap.containsKey(apply.getGoodsNo())) {
                                // 可用库存 + 请货数量 ≤ 请货修改白名单商品上限
                                scarceLimit = whiteGoodsMap.get(apply.getGoodsNo());
                                applyQty = BigDecimal.valueOf(scarceLimit).compareTo(applyQty) < 0 ? BigDecimal.valueOf(whiteGoodsMap.get(apply.getGoodsNo())) : applyQty;
                            }
                            applyReason.append(RecalculationReasonEnum.REASON_ELEVEN.getCode());
                            genRecord(company, store, records, apply, BigDecimal.ZERO, BigDecimal.ZERO, applyQty, applyNo, zyApplyNo,null, scarceLimit, applyReason.toString());
                        }
                    }
                    continue;
                }
                BigDecimal waitStock = goodsStocks.stream().map(StockGoodsBatchCodeSimpleInfo::getWaitStock).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal unqualifiedAreaStock = goodsStocks.stream().map(StockGoodsBatchCodeSimpleInfo::getUnqualifiedAreaStock).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal stock = goodsStocks.stream().map(StockGoodsBatchCodeSimpleInfo::getStock).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal transitStock = goodsStocks.stream().map(StockGoodsBatchCodeSimpleInfo::getTransitStock).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                // 替换码转换
                List<IscmApplyGoodsReplaceCode> replaceCodes = masterToReplaceMap.get(apply.getGoodsNo());
                if (CollectionUtils.isNotEmpty(replaceCodes)) {
                    for (IscmApplyGoodsReplaceCode replaceCode : replaceCodes) {
                        if (Objects.isNull(replaceCode.getChangeRatio()) || replaceCode.getChangeRatio().compareTo(BigDecimal.ZERO) <= 0) {
                            continue;
                        }
                        List<StockGoodsBatchCodeSimpleInfo> replaceGoodsStock = replaceGoodsStockMap.get(replaceCode.getReplaceGoodsNo());
                        if (CollectionUtils.isNotEmpty(replaceGoodsStock)) {
                            waitStock = waitStock.add(replaceGoodsStock.stream().map(StockGoodsBatchCodeSimpleInfo::getWaitStock).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(replaceCode.getChangeRatio()).setScale(4, RoundingMode.HALF_UP));
                            unqualifiedAreaStock = unqualifiedAreaStock.add(replaceGoodsStock.stream().map(StockGoodsBatchCodeSimpleInfo::getUnqualifiedAreaStock).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(replaceCode.getChangeRatio()).setScale( 4, RoundingMode.HALF_UP));
                            stock = stock.add(replaceGoodsStock.stream().map(StockGoodsBatchCodeSimpleInfo::getStock).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(replaceCode.getChangeRatio()).setScale( 4, RoundingMode.HALF_UP));
                            transitStock = transitStock.add(replaceGoodsStock.stream().map(StockGoodsBatchCodeSimpleInfo::getTransitStock).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(replaceCode.getChangeRatio()).setScale( 4, RoundingMode.HALF_UP));
                        }
                    }
                }
                // 5.	中台查询库存为“负数”，将数据修正为“0”
                waitStock = waitStock.compareTo(BigDecimal.ZERO) == -1 ? BigDecimal.ZERO : waitStock;
                unqualifiedAreaStock = unqualifiedAreaStock.compareTo(BigDecimal.ZERO) == -1 ? BigDecimal.ZERO : unqualifiedAreaStock;
                stock = stock.compareTo(BigDecimal.ZERO) == -1 ? BigDecimal.ZERO : stock;
                transitStock = transitStock.compareTo(BigDecimal.ZERO) == -1 ? BigDecimal.ZERO : transitStock;
                BigDecimal var1 = apply.getUnqualifiedAwaitStock().subtract(stock.subtract(waitStock.add(unqualifiedAreaStock)));
                BigDecimal var2 = apply.getTransitStock().subtract(transitStock);
                BigDecimal applyQty = apply.getApplyTotal().add(var1).add(var2);
                // 复算触发条件 总库存-(合格品区锁定库存+非合格品区库存)+在途库存 < max(库存下限,最小陈列量)
                BigDecimal useStock = stock.subtract(waitStock.add(unqualifiedAreaStock)).add(transitStock);
                try {
                    if (monitorStockSwitch && StringUtils.isNotBlank(monitorStockCompanys)) {
                        String[] split = StringUtils.split(monitorStockCompanys, ",");
                        if (Lists.newArrayList(split).contains(companyCode)) {
                            logger.info("企业编码:{},businessId:{},storeId:{},商品:{},库存:{}", companyCode, company.getOutId(), param.getStoreId(), apply.getGoodsNo(), JSON.toJSONString(goodsStocks));
                        }
                    }
                } catch (Exception e) {
                    logger.error("监控库存日志出错", e);
                }
                // 低动销、促销品类型不考虑当前库存是否低于下限，一律复算
                if (ApplyGoodsTypeEnum.NORMAL_GOODS.getCode() == apply.getApplyGoodsType() && useStock.compareTo(max(apply.getStockLowerLimit(), apply.getMinDisplayQty())) >= 0) {
                    logger.info("企业:{}门店:{}商品:{}总库存-(合格品区锁定库存+非合格品区库存)+在途库存 >= max(库存下限,最小陈列量)", companyCode, apply.getStoreCode(), apply.getGoodsNo());
                    IscmRecalculationNoResultLog log = new IscmRecalculationNoResultLog();
                    BeanUtils.copyProperties(apply, log);
                    log.setReason(RecalculationNoResultEnum.NON_NECESSARY_APPLY.getCode().toString());
                    log.setApplyStock(useStock);
                    error.add(log);
                    continue;
                }
                if (applyQty.compareTo(BigDecimal.ZERO) <= 0) {
                    logger.info("企业:{}门店:{}商品:{}BDP请货数量+变量A+变量B为0", companyCode, apply.getStoreCode(), apply.getGoodsNo());
                    IscmRecalculationNoResultLog log = new IscmRecalculationNoResultLog();
                    BeanUtils.copyProperties(apply, log);
                    log.setReason(RecalculationNoResultEnum.RECALCULATE_ZERO.getCode().toString());
                    log.setApplyStock(useStock);
                    error.add(log);
                    continue;
                }
                boolean scarce = false;
                if (whiteGoodsMap.containsKey(apply.getGoodsNo())) {
                    // 可用库存=总库存-非可用库存+总在途库存
                    // 非可用库存=合格品区锁定库存(WaitStock)+非合格品区库存（UnqualifiedAreaStock）
                    // 总在途库存=门店请货在途库存+调入在途库存+铺货在途库存
                    // 可用库存 + 请货数量 ≤ 请货修改白名单商品上限
                    scarceLimit = whiteGoodsMap.get(apply.getGoodsNo());
                    logger.info("商品:{}可用库存:{}白名单上限:{}:请货数量:{},是否小于白名单上限:{}", apply.getGoodsNo(), useStock, whiteGoodsMap.get(apply.getGoodsNo()), applyQty, BigDecimal.valueOf(whiteGoodsMap.get(apply.getGoodsNo())).compareTo(applyQty.add(useStock)) < 0);
                    if (BigDecimal.valueOf(scarceLimit).compareTo(applyQty.add(useStock)) < 0) {
                        scarce = true;
                    }
                    applyQty = BigDecimal.valueOf(scarceLimit).compareTo(applyQty.add(useStock)) < 0 ? BigDecimal.valueOf(scarceLimit).subtract(useStock) : applyQty;
                    logger.info("商品:{}可用库存:{}白名单上限:{}:最终请货数量:{}", apply.getGoodsNo(), useStock, whiteGoodsMap.get(apply.getGoodsNo()), applyQty);
                    if (applyQty.compareTo(BigDecimal.ZERO) <= 0) {
                        IscmRecalculationNoResultLog log = new IscmRecalculationNoResultLog();
                        BeanUtils.copyProperties(apply, log);
                        log.setReason(RecalculationNoResultEnum.WHITE_LIMIT.getCode().toString());
                        log.setApplyStock(useStock);
                        error.add(log);
                        continue;
                    }
                }
                if (apply.getStockLowerLimit().compareTo(apply.getMinDisplayQty()) > 0) {
                    if (!scarce) {
                        if (apply.getStockUpperLimit().compareTo(apply.getMinDisplayQty()) > 0){
                            applyReason.append(RecalculationReasonEnum.REASON_ONE.getCode());
                        } else {
                            applyReason.append(RecalculationReasonEnum.REASON_TWO.getCode());
                        }
                    } else {
                        applyReason.append(RecalculationReasonEnum.REASON_THREE.getCode());
                    }
                } else {
                    if (!scarce) {
                        if (apply.getStockUpperLimit().compareTo(apply.getMinDisplayQty()) > 0){
                            applyReason.append(RecalculationReasonEnum.REASON_FOUR.getCode());
                        } else {
                            applyReason.append(RecalculationReasonEnum.REASON_FIVE.getCode());
                        }
                    } else {
                        applyReason.append(RecalculationReasonEnum.REASON_SIX.getCode());
                    }
                }
                try {
                    // 常规补货且针对一周2配、一周3配、和一周7配门店减少2盒以下的非必要请货建议
//                    ArrayList<String> deliverycycles = Lists.newArrayList("1", "2", "3");
//                    logger.info("门店:{},商品:{},rate:{},applyQty:{},useStock:{}", apply.getStoreCode(), apply.getGoodsNo(), rate, applyQty, useStock);
                    if (null != rate
                            && null != useStock
                            && null != applyQty
                            && useStock.add(applyQty).compareTo(BigDecimal.ZERO) != 0
                            && apply.getBdpAverageDailySales().compareTo(BigDecimal.ZERO) != 0
                            && ApplyGoodsTypeEnum.NORMAL_GOODS.getCode() == apply.getApplyGoodsType()
//                            && deliverycycles.contains(apply.getDeliverycycleCode())
                            && applyQty.compareTo(BigDecimal.valueOf(2)) <= 0
                            && applyQty.multiply(BigDecimal.valueOf(100)).divide(useStock.add(applyQty), 1, RoundingMode.HALF_UP).compareTo(BigDecimal.valueOf(rate)) < 0
                            && useStock.divide(apply.getBdpAverageDailySales(), 1, RoundingMode.HALF_UP).compareTo(BigDecimal.valueOf(7)) > 0
                            && useStock.compareTo(apply.getThreeDaysSales()) > 0
                            && YNEnum.NO.getType().equals(apply.getNewProduct().intValue())
                            // 请货时点可用库存≤1，且最近30天销量≥1的 不做削减
                            && !(useStock.compareTo(BigDecimal.ONE) <= 0 && BigDecimal.ONE.compareTo(apply.getThirtyDaysSales()) <= 0)
                            // 加盟店的不削减
                            && !StoreAttrEnum.JM.getCode().equals(apply.getStoreAttr())
                            && YNEnum.YES.getType().equals(apply.getGoodsCutType())
                    ) {
                        if (CollectionUtils.isNotEmpty(deliverycycles)) {
                            if (deliverycycles.contains(apply.getDeliverycycleCode())) {
                                IscmRecalculationNoResultLog log = new IscmRecalculationNoResultLog();
                                BeanUtils.copyProperties(apply, log);
                                log.setReason(RecalculationNoResultEnum.CUT_APPLY.getCode().toString());
                                log.setApplyStock(useStock);
                                error.add(log);
                                continue;
                            }
                        } else {
                            IscmRecalculationNoResultLog log = new IscmRecalculationNoResultLog();
                            BeanUtils.copyProperties(apply, log);
                            log.setReason(RecalculationNoResultEnum.CUT_APPLY.getCode().toString());
                            log.setApplyStock(useStock);
                            error.add(log);
                            continue;
                        }
                    }
                } catch (Exception e) {
                    logger.error("门店:" + storeCode + "削减异常", e);
                }
                genRecord(company, store, records, apply, var1, var2, applyQty, applyNo, zyApplyNo, useStock, scarceLimit, applyReason.toString());
            }
            Map<String, BigDecimal> goodsPackageQtyMap = new HashMap<>();
            Iterator<IscmRecalculationRecord> iterator = records.iterator();
            while (iterator.hasNext()) {
                IscmRecalculationRecord record = iterator.next();
                // 企业中包装开关
                BigDecimal middleQty = record.getMiddlePackageQty();
                // 起请比例
                BigDecimal applyRatio = record.getApplyRatio();
                if (ApplyGoodsTypeEnum.GOOD_SUGGEST_GOODS.getCode() != record.getApplyGoodsType() && null != middleQty && null != applyRatio && record.getApplyTotal().compareTo(middleQty) <= 0) {
                    // 好品推荐不起请
                    // 起请逻辑处理条件：一个以内（包括一个）“中包装量
                    // 中包装数量 * 起请比例 > 请货数量 (舍去 不给建议)
                    if (record.getApplyTotal().compareTo(middleQty.multiply(applyRatio.divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP))) < 0) {
                        iterator.remove();
                        IscmRecalculationNoResultLog log = new IscmRecalculationNoResultLog();
                        BeanUtils.copyProperties(record, log);
                        log.setReason(RecalculationNoResultEnum.MIDDLE_PACKAGE_LIMIT.getCode().toString());
                        error.add(log);
                        continue;
                    }
                }
                Long categoryId = Optional.ofNullable(record.getCategoryId()).orElse(0L);
                if (!(ApplyGoodsTypeEnum.NORMAL_GOODS.getCode() == record.getApplyGoodsType() || ApplyGoodsTypeEnum.PROMOTION_GOODS.getCode() == record.getApplyGoodsType())) {
                    logger.info("企业:{}门店:{}商品:{}不是常规品或促销品不用中包装元整处理", companyCode, record.getStoreCode(), record.getGoodsNo());
                    continue;
                }
                if (categoryId.toString().startsWith("1202")) {
                    logger.info("企业:{}门店:{}商品:{}1202配方中药不用中包装元整处理", companyCode, record.getStoreCode(), record.getGoodsNo());
                    continue;
                }
                // 中包装取整规则 优先级 门店SKU维度 > 企业SKU维度 > 企业维度
                if (Objects.isNull(middleQty) || BigDecimal.ZERO.compareTo(middleQty) == 0) {
                    logger.info("企业:{}门店:{}商品:{}中包装数量为空或为0,不用复算", companyCode, record.getStoreCode(), record.getGoodsNo());
                    continue;
                }
                // 优先取一店一目商品的中包装数量,如果没有 则取企业级商品的中包装数量
                ComMiddleSwitchEnum switchEnum = ComMiddleSwitchEnum.getEnumByCode(record.getMiddlePackageSwitch());
                if (Objects.nonNull(switchEnum)) {
                    logger.info("企业:{}门店:{}商品:{}取一店一目商品或企业级商品的中包装处理方式:{}", companyCode, record.getStoreCode(), record.getGoodsNo(), switchEnum.getName());
                    if (switchEnum.equals(ComMiddleSwitchEnum.NO_APPROVE) || switchEnum.equals(ComMiddleSwitchEnum.HD_GJ9084)) {
                        // 这两个状态不处理
                        continue;
                    }
                    goodsPackageQtyMap.put(record.getStoreId().toString() + "-" + record.getGoodsNo(), middleQty);
                    if (switchEnum.equals(ComMiddleSwitchEnum.UP)) {
                        // 如果请货数量<=中包装数量, 则凑一个中包装数量
                        if (record.getApplyTotal().compareTo(middleQty) <= 0) {
                            logger.info("企业:{}门店:{}商品:{}请货数量:{}<=中包装数量:{}", companyCode, record.getStoreCode(), record.getApplyTotal().toPlainString(), middleQty.toPlainString());
                            record.setApplyTotal(middleQty);
                            record.setApplyReason(record.getApplyReason() + "," + RecalculationReasonEnum.REASON_TEN.getCode());
                            continue;
                        }
                        BigDecimal[] bigDecimals = record.getApplyTotal().divideAndRemainder(middleQty);
                        if (bigDecimals[1].compareTo(BigDecimal.ZERO) > 0) {
                            record.setApplyTotal(bigDecimals[0].multiply(middleQty).add(middleQty));
                        } else {
                            record.setApplyTotal(bigDecimals[0].multiply(middleQty));
                        }
                        record.setApplyReason(record.getApplyReason() + "," + RecalculationReasonEnum.REASON_TEN.getCode());
                        continue;
                    }
                    if (switchEnum.equals(ComMiddleSwitchEnum.HALF_UP)) {
                        // 如果请货数量<=中包装数量, 则凑一个中包装数量
                        if (record.getApplyTotal().compareTo(middleQty) <= 0) {
                            logger.info("企业:{}门店:{}商品:{}请货数量:{}<=中包装数量:{}", companyCode, record.getStoreCode(), record.getApplyTotal().toPlainString(), middleQty.toPlainString());
                            record.setApplyTotal(middleQty);
                            record.setApplyReason(record.getApplyReason() + "," + RecalculationReasonEnum.REASON_TEN.getCode());
                            continue;
                        }
                        BigDecimal[] bigDecimals = record.getApplyTotal().divideAndRemainder(middleQty);
                        if (bigDecimals[1].divide(middleQty, 2, RoundingMode.HALF_UP).compareTo(new BigDecimal("0.5")) < 0) {
                            record.setApplyTotal(bigDecimals[0].multiply(middleQty));
                        } else {
                            record.setApplyTotal(bigDecimals[0].multiply(middleQty).add(middleQty));
                        }
                        record.setApplyReason(record.getApplyReason() + "," + RecalculationReasonEnum.REASON_TEN.getCode());
                        continue;
                    }
                }
                // 如果一店一目以及企业均没有配置是否启用中包装,则取iscm的参数
                AutoApplyParamDTO switchDTO = autoApplyList.getMiddleDealSwitchDTO();
                if (Objects.isNull(switchDTO)) {
                    switchDTO = new AutoApplyParamDTO();
                    switchDTO.setParamValue("false");
                }
                if ("true".equals(switchDTO.getParamValue())) {
                    AutoApplyRemainderDealDTO remainderDealDTO = autoApplyList.getRemainderDealDTO();
                    ApplyParamRemainderDealEnum dealEnum = ApplyParamRemainderDealEnum.getEnumByCode(remainderDealDTO.getParamValue());
                    if (Objects.nonNull(dealEnum)) {
                        goodsPackageQtyMap.put(record.getStoreId().toString() + "-" + record.getGoodsNo(), middleQty);
                        logger.info("企业:{}门店:{}商品:{}取iscm的中包装处理方式:{}", companyCode, record.getStoreCode(), record.getGoodsNo(), dealEnum.getName());
                        // 如果请货数量<=中包装数量, 则凑一个中包装数量
                        if (record.getApplyTotal().compareTo(middleQty) <= 0) {
                            record.setApplyTotal(middleQty);
                            record.setApplyReason(record.getApplyReason() + "," + RecalculationReasonEnum.REASON_TEN.getCode());
                            continue;
                        }
                        if (dealEnum.equals(ApplyParamRemainderDealEnum.UP)) {
                            BigDecimal[] bigDecimals = record.getApplyTotal().divideAndRemainder(middleQty);
                            if (bigDecimals[1].compareTo(BigDecimal.ZERO) > 0) {
                                record.setApplyTotal(bigDecimals[0].multiply(middleQty).add(middleQty));
                            } else {
                                record.setApplyTotal(bigDecimals[0].multiply(middleQty));
                            }
                            record.setApplyReason(record.getApplyReason() + "," + RecalculationReasonEnum.REASON_TEN.getCode());
                        } else if (dealEnum.equals(ApplyParamRemainderDealEnum.HALF_UP)) {
                            BigDecimal[] bigDecimals = record.getApplyTotal().divideAndRemainder(middleQty);
                            if (bigDecimals[1].divide(middleQty, 2, RoundingMode.HALF_UP).compareTo(new BigDecimal("0.5")) < 0) {
                                record.setApplyTotal(bigDecimals[0].multiply(middleQty));
                            } else {
                                record.setApplyTotal(bigDecimals[0].multiply(middleQty).add(middleQty));
                            }
                            record.setApplyReason(record.getApplyReason() + "," + RecalculationReasonEnum.REASON_TEN.getCode());
                        } else if (dealEnum.equals(ApplyParamRemainderDealEnum.SELF)) {
                            BigDecimal[] bigDecimals = record.getApplyTotal().divideAndRemainder(middleQty);
                            logger.info("企业:{}门店:{}商品:{}商:{}余数:{},self:{},余数/中包装数量跟self的关系:{}", companyCode, record.getStoreCode(), record.getGoodsNo(), bigDecimals[0].toPlainString(), bigDecimals[1].toPlainString(), remainderDealDTO.getSelf(), bigDecimals[1].divide(middleQty, 2, RoundingMode.HALF_UP).compareTo(remainderDealDTO.getSelf()));
                            if (bigDecimals[1].divide(middleQty, 2, RoundingMode.HALF_UP).compareTo(remainderDealDTO.getSelf()) < 0) {
                                record.setApplyTotal(bigDecimals[0].multiply(middleQty));
                            } else {
                                record.setApplyTotal(bigDecimals[0].multiply(middleQty).add(middleQty));
                            }
                            record.setApplyReason(record.getApplyReason() + "," + RecalculationReasonEnum.REASON_TEN.getCode());
                        }
                    } else {
                        logger.info("企业:{}门店:{}商品:{}没有配置是否企业中包装参数", record.getCompanyCode(), record.getStoreCode(), record.getGoodsNo());
                        continue;
                    }
                } else {
                    logger.info("企业:{}门店:{}商品:{}没有配置是否企业中包装参数", record.getCompanyCode(), record.getStoreCode(), record.getGoodsNo());
                    continue;
                }
            }
            if (CollectionUtils.isNotEmpty(records)) {
                for (IscmRecalculationRecord record : records) {
                    logger.info("企业:{}门店:{}商品:{}specialCtrl:{}", companyCode, record.getStoreCode(), record.getGoodsNo(), record.getSpecialCtrl());
                    if (Constants.YES.equals(record.getSpecialCtrl())) {
                        BigDecimal once = null, thirty = null;
                        BigDecimal applyTotal = record.getApplyTotal();
                        logger.info("企业:{}门店:{}商品:{}是特管商品", companyCode, record.getStoreCode(), record.getGoodsNo());
                        if (Objects.nonNull(specialOnceLimit)) {
                            if (applyTotal.compareTo(specialOnceLimit) > 0) {
                                logger.info("企业:{}门店:{}商品:{}请货数量:{}大于单次请货上限:{}", companyCode, record.getStoreCode(), record.getGoodsNo(), applyTotal, specialOnceLimit);
                                once = specialOnceLimit;
                                record.setApplyTotal(specialOnceLimit);
                            }
                        } else {
                            logger.info("企业:{}门店:{}商品:{}没有配置企业级的单次请货上限制", companyCode, record.getStoreCode(), record.getGoodsNo());
                        }
                        if (Objects.nonNull(specialThirtyDaysLimit)) {
                            if (applyTotal.add(record.getSpecialThirtyDaysQty()).compareTo(specialThirtyDaysLimit) > 0) {
                                logger.info("企业:{}门店:{}商品:{}请货数量:{} + 30天请货数量:{}大于30天请货上限:{}", companyCode, record.getStoreCode(), record.getGoodsNo(), applyTotal, record.getSpecialThirtyDaysQty(), specialThirtyDaysLimit);
                                thirty = applyTotal.subtract(record.getSpecialThirtyDaysQty().add(applyTotal).subtract(specialThirtyDaysLimit));
                                logger.info("企业:{}门店:{}商品:{}请货数量thirty:{}", companyCode, record.getStoreCode(), record.getGoodsNo(), thirty);
                                record.setApplyTotal(thirty);
                            }
                        } else {
                            logger.info("企业:{}门店:{}商品:{}没有配置企业级的30天请货上限制", companyCode, record.getStoreCode(), record.getGoodsNo());
                        }
                        if (Objects.nonNull(once) && Objects.nonNull(thirty)) {
                            logger.info("企业:{}门店:{}商品:{}thirty:{}once:{}", companyCode, record.getStoreCode(), record.getGoodsNo(), thirty, once);
                            record.setApplyTotal(min(once, thirty));
                        }
                        // 如果是中包装商品 需要是中包装的整数倍
                        BigDecimal middleQty = goodsPackageQtyMap.get(record.getStoreId().toString() + "-" + record.getGoodsNo());
                        if (Objects.nonNull(middleQty)) {
                            logger.info("企业:{}门店:{}商品:{}middleQty:{}applyQty:{}", companyCode, record.getStoreCode(), record.getGoodsNo(), middleQty, record.getApplyTotal());
                            BigDecimal[] bigDecimals = record.getApplyTotal().divideAndRemainder(middleQty);
                            record.setApplyTotal(record.getApplyTotal().subtract(bigDecimals[1]));
                        }
                    }
                }
                insertRecords.addAll(records);
            }
            if (CollectionUtils.isNotEmpty(error)) {
                logger.info("企业:{}复算错误条数:{}第{}次", companyCode, error.size(), i);
                Lists.partition(error, Constants.BATCH_INSERT_ONCE_MAX_VALUE).forEach(k -> {
                    k.forEach(v -> {
                        if (StringUtils.isBlank(v.getReason())) {
                            v.setReason("");
                        }
                    });
                    iscmRecalculationNoResultLogExtendMapper.batchInsert(k);
                });
            }
        }
        insertRecords.forEach(v -> {
            if (null != v.getCategoryId() && !v.getCategoryId().toString().startsWith("1202")) {
                v.setApplyTotal(v.getApplyTotal().setScale(0, RoundingMode.UP));
            }
        });
        Lists.partition(insertRecords, Constants.BATCH_INSERT_ONCE_MAX_VALUE).forEach(v -> iscmRecalculationRecordExtendMapper.batchInsert(v));
        if (CollectionUtils.isNotEmpty(insertRecords) && pushHdSwitch) {
            logger.info("企业:{}门店:{}开始推送mb", companyCode, storeCode);
            pushToHd(companyCode, applyNo);
            pushToHd(companyCode, zyApplyNo);
            logger.info("企业:{}门店:{}推送完毕", companyCode, storeCode);
        }
        if (CollectionUtils.isNotEmpty(insertRecords) && pushWmsSwitch) {
            executor.execute(() -> {
                logger.info("企业:{}门店:{}开始推送wms", companyCode, storeCode);
                pushToWms(companyCode, applyNo);
                logger.info("企业:{}门店:{}推送wms完毕", companyCode, storeCode);
            });
        }
    }

    /**
     * 手工请货
     * @param platform
     * @param company
     * @param storeCode
     * @param autoApplyList
     * @param specialOnceLimit
     * @param specialThirtyDaysLimit
     * @throws Exception
     */
    @Override
    public List<IscmRecalculationRecord> recalculationManualByStore(Optional<OrgDTO> platform, OrgVO company, String storeCode, StoreApplyParamDTO autoApplyList, BigDecimal specialOnceLimit, BigDecimal specialThirtyDaysLimit) throws Exception {
        String companyCode = company.getSapcode();
        String applyNo = "BDP" + DateUtils.conventDateStrByDate(new Date(), DateUtils.DATE_SAP_PATTERN) + storeCode + DateUtils.conventDateStrByDate(new Date(), DateUtils.TIME_HHMM_PATTERN);
        String zyApplyNo = applyNo + "ZY";
        List<OrgVO> storeList = permissionService.queryOrgInfoBySapCodes(Lists.newArrayList(storeCode), OrgTypeEnum.ORG_TYPE_STORE.getCode());
        Map<String, Integer> whiteGoodsMap = getWhiteMap(company.getId(), storeCode);
        logger.info("whiteGoodsMap:{}", JSON.toJSONString(whiteGoodsMap));
        Integer rate = null;
        logger.info("companyRate:{}", companyRate);
        if (StringUtils.isNotBlank(companyRate)) {
            try {
                Optional<String> any = Arrays.stream(StringUtils.split(companyRate, ",")).filter(v -> v.contains(companyCode)).findAny();
                rate = any.isPresent() ? Integer.valueOf(any.get().split(":")[1]) : null;
                logger.info("rate:{}", rate);
            } catch (Exception e) {
                logger.error("企业:" + companyCode + "复算门店:" + storeCode+ "占比获取失败.", e);
            }
        }
        logger.info("companyDeliverycycles:{}", companyDeliverycycles);
        List<String> deliverycycles = new ArrayList<>();
        if (StringUtils.isNotBlank(companyDeliverycycles)) {
            try {
                Optional<String> any = Arrays.stream(StringUtils.split(companyDeliverycycles, ",")).filter(v -> v.contains(companyCode)).findAny();
                if (any.isPresent()) {
                    String deliverycycle = any.get().split(":")[1];
                    if (StringUtils.isNotBlank(deliverycycle)) {
                        deliverycycles.addAll(Arrays.stream(StringUtils.split(deliverycycle, "|")).collect(Collectors.toList()));
                    }
                }
            } catch (Exception e) {
                logger.error("企业:" + companyCode + "复算门店:" + storeCode+ "配送削减获取失败.", e);
            }
        }
        logger.info("deliverycycles:{}", deliverycycles);
        List<IscmRecalculationRecord> insertRecords = new ArrayList<>();
        for (int i = 0; ; i++) {
            IscmBdpApplyInfoExample example = new IscmBdpApplyInfoExample();
            example.createCriteria().andApplyDateEqualTo(new Date()).andCompanyCodeEqualTo(company.getSapcode()).andStoreCodeEqualTo(storeCode);
            example.setLimit(pageSize);
            example.setOffset(Long.valueOf(i * pageSize));
            example.setOrderByClause(" id asc");
            List<IscmBdpApplyInfo> applyInfos = iscmBdpApplyInfoMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(applyInfos)) {
                logger.info("企业:" + companyCode + "复算门店:" + storeCode+ "请货完毕.");
                break;
            }
            List<IscmRecalculationNoResultLog> error = new ArrayList<>();
            if (CollectionUtils.isEmpty(storeList)) {
                logger.warn("企业:{}下的门店编码:{},没有查询到对应的门店", companyCode, storeCode);
                error.addAll(applyInfos.stream().map(v -> {
                    IscmRecalculationNoResultLog log = new IscmRecalculationNoResultLog();
                    BeanUtils.copyProperties(v, log);
                    log.setReason(RecalculationNoResultEnum.NO_STORE.getCode().toString());
                    return log;
                }).collect(Collectors.toList()));
                continue;
            }
            OrgVO store = storeList.get(0);
            Map<String, List<StockGoodsBatchCodeSimpleInfo>> goodsStockMap = new HashMap<>();
            StockGoodsPagableQueryParam param = new StockGoodsPagableQueryParam();
            param.setBusinessId(company.getOutId());
            param.setStoreId(store.getOutId());
            // 不复算好品推荐
            List<String> goodsNos = applyInfos.stream().filter(v -> !v.getApplyGoodsType().equals(Byte.valueOf(ApplyGoodsTypeEnum.GOOD_SUGGEST_GOODS.getCode()))).map(IscmBdpApplyInfo::getGoodsNo).distinct().collect(Collectors.toList());
            // 中码组商品
            List<String> masterGoodsNos = applyInfos.stream().filter(v -> !v.getApplyGoodsType().equals(Byte.valueOf(ApplyGoodsTypeEnum.GOOD_SUGGEST_GOODS.getCode())) && YNEnum.YES.getType().byteValue() == v.getMiddleCodeFlag().byteValue()).map(IscmBdpApplyInfo::getGoodsNo).distinct().collect(Collectors.toList());
            // 主替换码码关系
            Map<String, List<IscmApplyGoodsReplaceCode>> masterToReplaceMap = new HashMap<>();
            // 替换码库存
            Map<String, List<StockGoodsBatchCodeSimpleInfo>> replaceGoodsStockMap = new HashMap<>();
            // 替换码数据
            Map<String, String> replaceGoodsNoMap = new HashMap<>();
            IscmBdpApplyInfo iscmBdpApplyInfo = applyInfos.get(0);
            if (CollectionUtils.isNotEmpty(goodsNos)) {
                List<List<String>> partition = Lists.partition(goodsNos, Constants.FEIGN_SEARCHAPI_SERVICE_ONCE_MAX_VALUE);
                for (List<String> goos : partition) {
                    param.setGoodsNos(goos);
                    goodsStockMap.putAll(stockcenterService.goodsBatchCodePage(param));
                }
                if (CollectionUtils.isNotEmpty(masterGoodsNos)) {
                    // 替换码
                    IscmApplyGoodsReplaceCodeExample replaceCodeExample = new IscmApplyGoodsReplaceCodeExample();
                    replaceCodeExample.createCriteria().andCompanyCodeEqualTo(companyCode).andMasterGoodsNoIn(masterGoodsNos);
                    List<IscmApplyGoodsReplaceCode> replaceList = iscmApplyGoodsReplaceCodeMapper.selectByExample(replaceCodeExample)
                            .stream().filter(v -> Objects.nonNull(v.getChangeRatio()) && StringUtils.isNotBlank(v.getMasterGoodsNo()) && StringUtils.isNotBlank(v.getReplaceGoodsNo()))
                            .filter(v -> !v.getMasterGoodsNo().equals(v.getReplaceGoodsNo())).collect(Collectors.toList());

                    IscmApplyGoodsReplaceCodeExample Example = new IscmApplyGoodsReplaceCodeExample();
                    Example.createCriteria().andCompanyCodeEqualTo(companyCode).andReplaceGoodsNoIn(masterGoodsNos);
                    replaceGoodsNoMap.putAll(iscmApplyGoodsReplaceCodeMapper.selectByExample(replaceCodeExample).stream().map(IscmApplyGoodsReplaceCode::getReplaceGoodsNo).collect(Collectors.toMap(v -> v, Function.identity(), (k1, k2) -> k1)));

                    if (CollectionUtils.isNotEmpty(replaceList)) {
                        List<List<IscmApplyGoodsReplaceCode>> replacePartition = Lists.partition(replaceList, Constants.FEIGN_SEARCHAPI_SERVICE_ONCE_MAX_VALUE);
                        for (List<IscmApplyGoodsReplaceCode> goos : replacePartition) {
                            param.setGoodsNos(goos.stream().map(IscmApplyGoodsReplaceCode::getReplaceGoodsNo).distinct().collect(Collectors.toList()));
                            replaceGoodsStockMap.putAll(stockcenterService.goodsBatchCodePage(param));
                        }
                        masterToReplaceMap.putAll(replaceList.stream().collect(Collectors.groupingBy(IscmApplyGoodsReplaceCode::getMasterGoodsNo)));
                    }
                }
            }
//                    复算请货数量=BDP请货数量+变量A+变量B
//                    1）变量取【BDP计算数量-复算时的数量】
//                    2）变量A： BDP计算时【变量1】- 复算时[总库存（Stock）-（合格品区锁定库存(WaitStock)+非合格品区库存（UnqualifiedAreaStock））]，来源：库存中台
//                    3）变量B：BDP计算时【变量2】-复算时总在途库存，来源：库存中台
            // 复算后不为0的集合
            List<IscmRecalculationRecord> records = new ArrayList<>();
            // 只复算常规品
            if (CollectionUtils.isNotEmpty(goodsNos)) {
                ItemBaseBatchQueryParamVO paramVo = new ItemBaseBatchQueryParamVO();
                paramVo.setBusinessId(company.getOutId());
                paramVo.setStoreId(store.getOutId());
                Lists.partition(goodsNos, searchPage).forEach(goods -> {
                    paramVo.setGoodsNoList(goods);
                });
            }
            for (IscmBdpApplyInfo apply : applyInfos) {
                StringBuilder applyReason = new StringBuilder();
                Integer scarceLimit = null;
                if (ApplyGoodsTypeEnum.GOOD_SUGGEST_GOODS.getCode() == apply.getApplyGoodsType()) {
                    logger.info("企业:{}门店:{}商品:{}好品推荐不用复算", companyCode, apply.getStoreCode(), apply.getGoodsNo());
                    if (apply.getApplyTotal().compareTo(BigDecimal.ZERO) <= 0) {
                        logger.info("企业:{},businessId:{},storeId:{},商品:{},没有查询到库存信息", companyCode, company.getOutId(), param.getStoreId(), apply.getGoodsNo());
                        IscmRecalculationNoResultLog log = new IscmRecalculationNoResultLog();
                        BeanUtils.copyProperties(apply, log);
                        log.setReason(RecalculationNoResultEnum.GOODS_SUGGEST_ZERO.getCode().toString());
                        error.add(log);
                    } else {
                        if (ApplyGoodsTypeEnum.GOOD_SUGGEST_GOODS.getCode() == apply.getApplyGoodsType()) {
                            applyReason.append(RecalculationReasonEnum.REASON_NINE.getCode());
                        }
                        if (ApplyGoodsTypeEnum.LOW_SELL_GOODS.getCode() == apply.getApplyGoodsType()) {
                            applyReason.append(RecalculationReasonEnum.REASON_SEVEN.getCode());
                        }
                        if (ApplyGoodsTypeEnum.PROMOTION_GOODS.getCode() == apply.getApplyGoodsType()) {
                            applyReason.append(RecalculationReasonEnum.REASON_EIGHT.getCode());
                        }
                        genRecord(company, store, records, apply, BigDecimal.ZERO, BigDecimal.ZERO, apply.getApplyTotal(), applyNo, zyApplyNo,null, scarceLimit, applyReason.toString());
                    }
                    continue;
                }
                List<StockGoodsBatchCodeSimpleInfo> goodsStocks = goodsStockMap.get(apply.getGoodsNo());
                if (CollectionUtils.isEmpty(goodsStocks) ) {
                    logger.info("企业:{},businessId:{},storeId:{},商品:{},没有查询到库存信息", companyCode, company.getOutId(), param.getStoreId(), apply.getGoodsNo());
                    // 总库存-(合格品区锁定库存+非合格品区库存)+在途库存
                    BigDecimal bdpUseStock = apply.getStock().subtract(apply.getLockStock().add(apply.getUnqualifiedStock())).add(apply.getTransitStock());
                    BigDecimal applyQty = apply.getApplyTotal();
                    if (apply.getApplyTotal().compareTo(BigDecimal.ZERO) <= 0) {
                        IscmRecalculationNoResultLog log = new IscmRecalculationNoResultLog();
                        BeanUtils.copyProperties(apply, log);
                        log.setReason(RecalculationNoResultEnum.GOODS_NO_STOCK.getCode().toString());
                        error.add(log);
                    } else {
                        if (ApplyGoodsTypeEnum.NORMAL_GOODS.getCode() == apply.getApplyGoodsType() && bdpUseStock.compareTo(max(apply.getStockLowerLimit(), apply.getMinDisplayQty())) >= 0) {
                            logger.info("企业:{}门店:{}商品:{}总库存-(合格品区锁定库存+非合格品区库存)+在途库存 >= max(库存下限,最小陈列量)", companyCode, apply.getStoreCode(), apply.getGoodsNo());
                            IscmRecalculationNoResultLog log = new IscmRecalculationNoResultLog();
                            BeanUtils.copyProperties(apply, log);
                            log.setReason(RecalculationNoResultEnum.BDP_NON_NECESSARY_APPLY.getCode().toString());
                            log.setApplyStock(bdpUseStock);
                            error.add(log);
                        } else {
                            if (whiteGoodsMap.containsKey(apply.getGoodsNo())) {
                                // 可用库存 + 请货数量 ≤ 请货修改白名单商品上限
                                scarceLimit = whiteGoodsMap.get(apply.getGoodsNo());
                                applyQty = BigDecimal.valueOf(scarceLimit).compareTo(applyQty) < 0 ? BigDecimal.valueOf(whiteGoodsMap.get(apply.getGoodsNo())) : applyQty;
                            }
                            applyReason.append(RecalculationReasonEnum.REASON_ELEVEN.getCode());
                            genRecord(company, store, records, apply, BigDecimal.ZERO, BigDecimal.ZERO, applyQty, applyNo, zyApplyNo,null, scarceLimit, applyReason.toString());
                        }
                    }
                    continue;
                }
                BigDecimal waitStock = goodsStocks.stream().map(StockGoodsBatchCodeSimpleInfo::getWaitStock).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal unqualifiedAreaStock = goodsStocks.stream().map(StockGoodsBatchCodeSimpleInfo::getUnqualifiedAreaStock).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal stock = goodsStocks.stream().map(StockGoodsBatchCodeSimpleInfo::getStock).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal transitStock = goodsStocks.stream().map(StockGoodsBatchCodeSimpleInfo::getTransitStock).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                // 替换码转换
                List<IscmApplyGoodsReplaceCode> replaceCodes = masterToReplaceMap.get(apply.getGoodsNo());
                if (CollectionUtils.isNotEmpty(replaceCodes)) {
                    for (IscmApplyGoodsReplaceCode replaceCode : replaceCodes) {
                        if (Objects.isNull(replaceCode.getChangeRatio()) || replaceCode.getChangeRatio().compareTo(BigDecimal.ZERO) <= 0) {
                            continue;
                        }
                        List<StockGoodsBatchCodeSimpleInfo> replaceGoodsStock = replaceGoodsStockMap.get(replaceCode.getReplaceGoodsNo());
                        if (CollectionUtils.isNotEmpty(replaceGoodsStock)) {
                            waitStock = waitStock.add(replaceGoodsStock.stream().map(StockGoodsBatchCodeSimpleInfo::getWaitStock).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(replaceCode.getChangeRatio()).setScale(4, RoundingMode.HALF_UP));
                            unqualifiedAreaStock = unqualifiedAreaStock.add(replaceGoodsStock.stream().map(StockGoodsBatchCodeSimpleInfo::getUnqualifiedAreaStock).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(replaceCode.getChangeRatio()).setScale( 4, RoundingMode.HALF_UP));
                            stock = stock.add(replaceGoodsStock.stream().map(StockGoodsBatchCodeSimpleInfo::getStock).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(replaceCode.getChangeRatio()).setScale( 4, RoundingMode.HALF_UP));
                            transitStock = transitStock.add(replaceGoodsStock.stream().map(StockGoodsBatchCodeSimpleInfo::getTransitStock).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(replaceCode.getChangeRatio()).setScale( 4, RoundingMode.HALF_UP));
                        }
                    }
                }
                // 5.	中台查询库存为“负数”，将数据修正为“0”
                waitStock = waitStock.compareTo(BigDecimal.ZERO) == -1 ? BigDecimal.ZERO : waitStock;
                unqualifiedAreaStock = unqualifiedAreaStock.compareTo(BigDecimal.ZERO) == -1 ? BigDecimal.ZERO : unqualifiedAreaStock;
                stock = stock.compareTo(BigDecimal.ZERO) == -1 ? BigDecimal.ZERO : stock;
                transitStock = transitStock.compareTo(BigDecimal.ZERO) == -1 ? BigDecimal.ZERO : transitStock;
                BigDecimal var1 = apply.getUnqualifiedAwaitStock().subtract(stock.subtract(waitStock.add(unqualifiedAreaStock)));
                BigDecimal var2 = apply.getTransitStock().subtract(transitStock);
                BigDecimal applyQty = apply.getApplyTotal().add(var1).add(var2);
                // 复算触发条件 总库存-(合格品区锁定库存+非合格品区库存)+在途库存 < max(库存下限,最小陈列量)
                BigDecimal useStock = stock.subtract(waitStock.add(unqualifiedAreaStock)).add(transitStock);
                // 低动销、促销品类型不考虑当前库存是否低于下限，一律复算
                if (ApplyGoodsTypeEnum.NORMAL_GOODS.getCode() == apply.getApplyGoodsType() && useStock.compareTo(max(apply.getStockLowerLimit(), apply.getMinDisplayQty())) >= 0) {
                    logger.info("企业:{}门店:{}商品:{}总库存-(合格品区锁定库存+非合格品区库存)+在途库存 >= max(库存下限,最小陈列量)", companyCode, apply.getStoreCode(), apply.getGoodsNo());
                    IscmRecalculationNoResultLog log = new IscmRecalculationNoResultLog();
                    BeanUtils.copyProperties(apply, log);
                    log.setReason(RecalculationNoResultEnum.NON_NECESSARY_APPLY.getCode().toString());
                    log.setApplyStock(useStock);
                    error.add(log);
                    continue;
                }
                if (applyQty.compareTo(BigDecimal.ZERO) <= 0) {
                    logger.info("企业:{}门店:{}商品:{}BDP请货数量+变量A+变量B为0", companyCode, apply.getStoreCode(), apply.getGoodsNo());
                    IscmRecalculationNoResultLog log = new IscmRecalculationNoResultLog();
                    BeanUtils.copyProperties(apply, log);
                    log.setReason(RecalculationNoResultEnum.RECALCULATE_ZERO.getCode().toString());
                    log.setApplyStock(useStock);
                    error.add(log);
                    continue;
                }
                boolean scarce = false;
                if (whiteGoodsMap.containsKey(apply.getGoodsNo())) {
                    // 可用库存=总库存-非可用库存+总在途库存
                    // 非可用库存=合格品区锁定库存(WaitStock)+非合格品区库存（UnqualifiedAreaStock）
                    // 总在途库存=门店请货在途库存+调入在途库存+铺货在途库存
                    // 可用库存 + 请货数量 ≤ 请货修改白名单商品上限
                    scarceLimit = whiteGoodsMap.get(apply.getGoodsNo());
                    logger.info("商品:{}可用库存:{}白名单上限:{}:请货数量:{},是否小于白名单上限:{}", apply.getGoodsNo(), useStock, whiteGoodsMap.get(apply.getGoodsNo()), applyQty, BigDecimal.valueOf(whiteGoodsMap.get(apply.getGoodsNo())).compareTo(applyQty.add(useStock)) < 0);
                    if (BigDecimal.valueOf(scarceLimit).compareTo(applyQty.add(useStock)) < 0) {
                        scarce = true;
                    }
                    applyQty = BigDecimal.valueOf(scarceLimit).compareTo(applyQty.add(useStock)) < 0 ? BigDecimal.valueOf(scarceLimit).subtract(useStock) : applyQty;
                    logger.info("商品:{}可用库存:{}白名单上限:{}:最终请货数量:{}", apply.getGoodsNo(), useStock, whiteGoodsMap.get(apply.getGoodsNo()), applyQty);
                    if (applyQty.compareTo(BigDecimal.ZERO) <= 0) {
                        IscmRecalculationNoResultLog log = new IscmRecalculationNoResultLog();
                        BeanUtils.copyProperties(apply, log);
                        log.setReason(RecalculationNoResultEnum.WHITE_LIMIT.getCode().toString());
                        log.setApplyStock(useStock);
                        error.add(log);
                        continue;
                    }
                }
                if (apply.getStockLowerLimit().compareTo(apply.getMinDisplayQty()) > 0) {
                    if (!scarce) {
                        if (apply.getStockUpperLimit().compareTo(apply.getMinDisplayQty()) > 0){
                            applyReason.append(RecalculationReasonEnum.REASON_ONE.getCode());
                        } else {
                            applyReason.append(RecalculationReasonEnum.REASON_TWO.getCode());
                        }
                    } else {
                        applyReason.append(RecalculationReasonEnum.REASON_THREE.getCode());
                    }
                } else {
                    if (!scarce) {
                        if (apply.getStockUpperLimit().compareTo(apply.getMinDisplayQty()) > 0){
                            applyReason.append(RecalculationReasonEnum.REASON_FOUR.getCode());
                        } else {
                            applyReason.append(RecalculationReasonEnum.REASON_FIVE.getCode());
                        }
                    } else {
                        applyReason.append(RecalculationReasonEnum.REASON_SIX.getCode());
                    }
                }
                try {
                    // 常规补货且针对一周2配、一周3配、和一周7配门店减少2盒以下的非必要请货建议
                    if (null != rate
                            && null != useStock
                            && null != applyQty
                            && useStock.add(applyQty).compareTo(BigDecimal.ZERO) != 0
                            && apply.getBdpAverageDailySales().compareTo(BigDecimal.ZERO) != 0
                            && ApplyGoodsTypeEnum.NORMAL_GOODS.getCode() == apply.getApplyGoodsType()
//                            && deliverycycles.contains(apply.getDeliverycycleCode())
                            && applyQty.compareTo(BigDecimal.valueOf(2)) <= 0
                            && applyQty.multiply(BigDecimal.valueOf(100)).divide(useStock.add(applyQty), 1, RoundingMode.HALF_UP).compareTo(BigDecimal.valueOf(rate)) < 0
                            && useStock.divide(apply.getBdpAverageDailySales(), 1, RoundingMode.HALF_UP).compareTo(BigDecimal.valueOf(7)) > 0
                            && useStock.compareTo(apply.getThreeDaysSales()) > 0
                            && YNEnum.NO.getType().equals(apply.getNewProduct().intValue())
                            // 请货时点可用库存≤1，且最近30天销量≥1的 不做削减
                            && !(useStock.compareTo(BigDecimal.ONE) <= 0 && BigDecimal.ONE.compareTo(apply.getThirtyDaysSales()) <= 0)
                            // 加盟店的不削减
                            && !StoreAttrEnum.JM.getCode().equals(apply.getStoreAttr())
                            && YNEnum.YES.getType().equals(apply.getGoodsCutType())
                    ) {
                        if (CollectionUtils.isNotEmpty(deliverycycles)) {
                            if (deliverycycles.contains(apply.getDeliverycycleCode())) {
                                IscmRecalculationNoResultLog log = new IscmRecalculationNoResultLog();
                                BeanUtils.copyProperties(apply, log);
                                log.setReason(RecalculationNoResultEnum.CUT_APPLY.getCode().toString());
                                log.setApplyStock(useStock);
                                error.add(log);
                                continue;
                            }
                        } else {
                            IscmRecalculationNoResultLog log = new IscmRecalculationNoResultLog();
                            BeanUtils.copyProperties(apply, log);
                            log.setReason(RecalculationNoResultEnum.CUT_APPLY.getCode().toString());
                            log.setApplyStock(useStock);
                            error.add(log);
                            continue;
                        }
                    }
                } catch (Exception e) {
                    logger.error("门店:" + storeCode + "削减异常", e);
                }
                genRecord(company, store, records, apply, var1, var2, applyQty, applyNo, zyApplyNo, useStock, scarceLimit, applyReason.toString());
            }
            Map<String, BigDecimal> goodsPackageQtyMap = new HashMap<>();
            Iterator<IscmRecalculationRecord> iterator = records.iterator();
            while (iterator.hasNext()) {
                IscmRecalculationRecord record = iterator.next();
                // 企业中包装开关
                BigDecimal middleQty = record.getMiddlePackageQty();
                // 起请比例
                BigDecimal applyRatio = record.getApplyRatio();
                if (ApplyGoodsTypeEnum.GOOD_SUGGEST_GOODS.getCode() != record.getApplyGoodsType() && null != middleQty && null != applyRatio && record.getApplyTotal().compareTo(middleQty) <= 0) {
                    // 好品推荐不起请
                    // 起请逻辑处理条件：一个以内（包括一个）“中包装量
                    // 中包装数量 * 起请比例 > 请货数量 (舍去 不给建议)
                    if (record.getApplyTotal().compareTo(middleQty.multiply(applyRatio.divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP))) < 0) {
                        iterator.remove();
                        IscmRecalculationNoResultLog log = new IscmRecalculationNoResultLog();
                        BeanUtils.copyProperties(record, log);
                        log.setReason(RecalculationNoResultEnum.MIDDLE_PACKAGE_LIMIT.getCode().toString());
                        error.add(log);
                        continue;
                    }
                }
                Long categoryId = Optional.ofNullable(record.getCategoryId()).orElse(0L);
                if (!(ApplyGoodsTypeEnum.NORMAL_GOODS.getCode() == record.getApplyGoodsType() || ApplyGoodsTypeEnum.PROMOTION_GOODS.getCode() == record.getApplyGoodsType())) {
                    logger.info("企业:{}门店:{}商品:{}不是常规品或促销品不用中包装元整处理", companyCode, record.getStoreCode(), record.getGoodsNo());
                    continue;
                }
                if (categoryId.toString().startsWith("1202")) {
                    logger.info("企业:{}门店:{}商品:{}1202配方中药不用中包装元整处理", companyCode, record.getStoreCode(), record.getGoodsNo());
                    continue;
                }
                // 中包装取整规则 优先级 门店SKU维度 > 企业SKU维度 > 企业维度
                if (Objects.isNull(middleQty) || BigDecimal.ZERO.compareTo(middleQty) == 0) {
                    logger.info("企业:{}门店:{}商品:{}中包装数量为空或为0,不用复算", companyCode, record.getStoreCode(), record.getGoodsNo());
                    continue;
                }
                // 优先取一店一目商品的中包装数量,如果没有 则取企业级商品的中包装数量
                ComMiddleSwitchEnum switchEnum = ComMiddleSwitchEnum.getEnumByCode(record.getMiddlePackageSwitch());
                if (Objects.nonNull(switchEnum)) {
                    logger.info("企业:{}门店:{}商品:{}取一店一目商品或企业级商品的中包装处理方式:{}", companyCode, record.getStoreCode(), record.getGoodsNo(), switchEnum.getName());
                    if (switchEnum.equals(ComMiddleSwitchEnum.NO_APPROVE) || switchEnum.equals(ComMiddleSwitchEnum.HD_GJ9084)) {
                        // 这两个状态不处理
                        continue;
                    }
                    goodsPackageQtyMap.put(record.getStoreId().toString() + "-" + record.getGoodsNo(), middleQty);
                    if (switchEnum.equals(ComMiddleSwitchEnum.UP)) {
                        // 如果请货数量<=中包装数量, 则凑一个中包装数量
                        if (record.getApplyTotal().compareTo(middleQty) <= 0) {
                            logger.info("企业:{}门店:{}商品:{}请货数量:{}<=中包装数量:{}", companyCode, record.getStoreCode(), record.getApplyTotal().toPlainString(), middleQty.toPlainString());
                            record.setApplyTotal(middleQty);
                            record.setApplyReason(record.getApplyReason() + "," + RecalculationReasonEnum.REASON_TEN.getCode());
                            continue;
                        }
                        BigDecimal[] bigDecimals = record.getApplyTotal().divideAndRemainder(middleQty);
                        if (bigDecimals[1].compareTo(BigDecimal.ZERO) > 0) {
                            record.setApplyTotal(bigDecimals[0].multiply(middleQty).add(middleQty));
                        } else {
                            record.setApplyTotal(bigDecimals[0].multiply(middleQty));
                        }
                        record.setApplyReason(record.getApplyReason() + "," + RecalculationReasonEnum.REASON_TEN.getCode());
                        continue;
                    }
                    if (switchEnum.equals(ComMiddleSwitchEnum.HALF_UP)) {
                        // 如果请货数量<=中包装数量, 则凑一个中包装数量
                        if (record.getApplyTotal().compareTo(middleQty) <= 0) {
                            logger.info("企业:{}门店:{}商品:{}请货数量:{}<=中包装数量:{}", companyCode, record.getStoreCode(), record.getApplyTotal().toPlainString(), middleQty.toPlainString());
                            record.setApplyTotal(middleQty);
                            record.setApplyReason(record.getApplyReason() + "," + RecalculationReasonEnum.REASON_TEN.getCode());
                            continue;
                        }
                        BigDecimal[] bigDecimals = record.getApplyTotal().divideAndRemainder(middleQty);
                        if (bigDecimals[1].divide(middleQty, 2, RoundingMode.HALF_UP).compareTo(new BigDecimal("0.5")) < 0) {
                            record.setApplyTotal(bigDecimals[0].multiply(middleQty));
                        } else {
                            record.setApplyTotal(bigDecimals[0].multiply(middleQty).add(middleQty));
                        }
                        record.setApplyReason(record.getApplyReason() + "," + RecalculationReasonEnum.REASON_TEN.getCode());
                        continue;
                    }
                }
                // 如果一店一目以及企业均没有配置是否启用中包装,则取iscm的参数
                AutoApplyParamDTO switchDTO = autoApplyList.getMiddleDealSwitchDTO();
                if (Objects.isNull(switchDTO)) {
                    switchDTO = new AutoApplyParamDTO();
                    switchDTO.setParamValue("false");
                }
                if ("true".equals(switchDTO.getParamValue())) {
                    AutoApplyRemainderDealDTO remainderDealDTO = autoApplyList.getRemainderDealDTO();
                    ApplyParamRemainderDealEnum dealEnum = ApplyParamRemainderDealEnum.getEnumByCode(remainderDealDTO.getParamValue());
                    if (Objects.nonNull(dealEnum)) {
                        goodsPackageQtyMap.put(record.getStoreId().toString() + "-" + record.getGoodsNo(), middleQty);
                        logger.info("企业:{}门店:{}商品:{}取iscm的中包装处理方式:{}", companyCode, record.getStoreCode(), record.getGoodsNo(), dealEnum.getName());
                        // 如果请货数量<=中包装数量, 则凑一个中包装数量
                        if (record.getApplyTotal().compareTo(middleQty) <= 0) {
                            record.setApplyTotal(middleQty);
                            record.setApplyReason(record.getApplyReason() + "," + RecalculationReasonEnum.REASON_TEN.getCode());
                            continue;
                        }
                        if (dealEnum.equals(ApplyParamRemainderDealEnum.UP)) {
                            BigDecimal[] bigDecimals = record.getApplyTotal().divideAndRemainder(middleQty);
                            if (bigDecimals[1].compareTo(BigDecimal.ZERO) > 0) {
                                record.setApplyTotal(bigDecimals[0].multiply(middleQty).add(middleQty));
                            } else {
                                record.setApplyTotal(bigDecimals[0].multiply(middleQty));
                            }
                            record.setApplyReason(record.getApplyReason() + "," + RecalculationReasonEnum.REASON_TEN.getCode());
                        } else if (dealEnum.equals(ApplyParamRemainderDealEnum.HALF_UP)) {
                            BigDecimal[] bigDecimals = record.getApplyTotal().divideAndRemainder(middleQty);
                            if (bigDecimals[1].divide(middleQty, 2, RoundingMode.HALF_UP).compareTo(new BigDecimal("0.5")) < 0) {
                                record.setApplyTotal(bigDecimals[0].multiply(middleQty));
                            } else {
                                record.setApplyTotal(bigDecimals[0].multiply(middleQty).add(middleQty));
                            }
                            record.setApplyReason(record.getApplyReason() + "," + RecalculationReasonEnum.REASON_TEN.getCode());
                        } else if (dealEnum.equals(ApplyParamRemainderDealEnum.SELF)) {
                            BigDecimal[] bigDecimals = record.getApplyTotal().divideAndRemainder(middleQty);
                            logger.info("企业:{}门店:{}商品:{}商:{}余数:{},self:{},余数/中包装数量跟self的关系:{}", companyCode, record.getStoreCode(), record.getGoodsNo(), bigDecimals[0].toPlainString(), bigDecimals[1].toPlainString(), remainderDealDTO.getSelf(), bigDecimals[1].divide(middleQty, 2, RoundingMode.HALF_UP).compareTo(remainderDealDTO.getSelf()));
                            if (bigDecimals[1].divide(middleQty, 2, RoundingMode.HALF_UP).compareTo(remainderDealDTO.getSelf()) < 0) {
                                record.setApplyTotal(bigDecimals[0].multiply(middleQty));
                            } else {
                                record.setApplyTotal(bigDecimals[0].multiply(middleQty).add(middleQty));
                            }
                            record.setApplyReason(record.getApplyReason() + "," + RecalculationReasonEnum.REASON_TEN.getCode());
                        }
                    } else {
                        logger.info("企业:{}门店:{}商品:{}没有配置是否企业中包装参数", record.getCompanyCode(), record.getStoreCode(), record.getGoodsNo());
                        continue;
                    }
                } else {
                    logger.info("企业:{}门店:{}商品:{}没有配置是否企业中包装参数", record.getCompanyCode(), record.getStoreCode(), record.getGoodsNo());
                    continue;
                }
            }
            if (CollectionUtils.isNotEmpty(records)) {
                for (IscmRecalculationRecord record : records) {
                    logger.info("企业:{}门店:{}商品:{}specialCtrl:{}", companyCode, record.getStoreCode(), record.getGoodsNo(), record.getSpecialCtrl());
                    if (Constants.YES.equals(record.getSpecialCtrl())) {
                        BigDecimal once = null, thirty = null;
                        BigDecimal applyTotal = record.getApplyTotal();
                        logger.info("企业:{}门店:{}商品:{}是特管商品", companyCode, record.getStoreCode(), record.getGoodsNo());
                        if (Objects.nonNull(specialOnceLimit)) {
                            if (applyTotal.compareTo(specialOnceLimit) > 0) {
                                logger.info("企业:{}门店:{}商品:{}请货数量:{}大于单次请货上限:{}", companyCode, record.getStoreCode(), record.getGoodsNo(), applyTotal, specialOnceLimit);
                                once = specialOnceLimit;
                                record.setApplyTotal(specialOnceLimit);
                            }
                        } else {
                            logger.info("企业:{}门店:{}商品:{}没有配置企业级的单次请货上限制", companyCode, record.getStoreCode(), record.getGoodsNo());
                        }
                        if (Objects.nonNull(specialThirtyDaysLimit)) {
                            if (applyTotal.add(record.getSpecialThirtyDaysQty()).compareTo(specialThirtyDaysLimit) > 0) {
                                logger.info("企业:{}门店:{}商品:{}请货数量:{} + 30天请货数量:{}大于30天请货上限:{}", companyCode, record.getStoreCode(), record.getGoodsNo(), applyTotal, record.getSpecialThirtyDaysQty(), specialThirtyDaysLimit);
                                thirty = applyTotal.subtract(record.getSpecialThirtyDaysQty().add(applyTotal).subtract(specialThirtyDaysLimit));
                                logger.info("企业:{}门店:{}商品:{}请货数量thirty:{}", companyCode, record.getStoreCode(), record.getGoodsNo(), thirty);
                                record.setApplyTotal(thirty);
                            }
                        } else {
                            logger.info("企业:{}门店:{}商品:{}没有配置企业级的30天请货上限制", companyCode, record.getStoreCode(), record.getGoodsNo());
                        }
                        if (Objects.nonNull(once) && Objects.nonNull(thirty)) {
                            logger.info("企业:{}门店:{}商品:{}thirty:{}once:{}", companyCode, record.getStoreCode(), record.getGoodsNo(), thirty, once);
                            record.setApplyTotal(min(once, thirty));
                        }
                        // 如果是中包装商品 需要是中包装的整数倍
                        BigDecimal middleQty = goodsPackageQtyMap.get(record.getStoreId().toString() + "-" + record.getGoodsNo());
                        if (Objects.nonNull(middleQty)) {
                            logger.info("企业:{}门店:{}商品:{}middleQty:{}applyQty:{}", companyCode, record.getStoreCode(), record.getGoodsNo(), middleQty, record.getApplyTotal());
                            BigDecimal[] bigDecimals = record.getApplyTotal().divideAndRemainder(middleQty);
                            record.setApplyTotal(record.getApplyTotal().subtract(bigDecimals[1]));
                        }
                    }
                }
                insertRecords.addAll(records);
            }
            if (CollectionUtils.isNotEmpty(error)) {
                logger.info("企业:{}复算错误条数:{}第{}次", companyCode, error.size(), i);
                Lists.partition(error, Constants.BATCH_INSERT_ONCE_MAX_VALUE).forEach(k -> {
                    k.forEach(v -> {
                        if (StringUtils.isBlank(v.getReason())) {
                            v.setReason("");
                        }
                    });
                    iscmRecalculationNoResultLogExtendMapper.batchInsert(k);
                });
            }
        }
        insertRecords.forEach(v -> {
            if (null != v.getCategoryId() && !v.getCategoryId().toString().startsWith("1202")) {
                v.setApplyTotal(v.getApplyTotal().setScale(0, RoundingMode.UP));
            }
        });
        Lists.partition(insertRecords, Constants.BATCH_INSERT_ONCE_MAX_VALUE).forEach(v -> iscmRecalculationRecordExtendMapper.batchInsert(v));
        return insertRecords;
    }

    @Override
    public void createRecalculationDelayJob() throws Exception {
        try {
            logger.info("开始创建复算任务");
            String[] split = StringUtils.split(recalculationCompany, ",");
            if (Objects.isNull(split) || split.length <= 0) {
                sendJobErrorMsg2Wechat(recalculationCompany, "没有需要创建复算任务的企业,请检查apollo配置");
                throw new BusinessErrorException("没有需要复算的企业");
            }
            logger.info("创建复算任务企业:{}", JSON.toJSONString(Arrays.stream(split).distinct().collect(Collectors.toList())));
            List<OrgVO> orgVOS = new ArrayList<>();
            try {
                orgVOS.addAll(retryService.retryQueryOrgInfoBySapCodes(Arrays.stream(split).distinct().collect(Collectors.toList()), OrgTypeEnum.ORG_TYPE_BUSINESS.getCode()));
            } catch (Exception e) {
                logger.error("需要复算的企业没有在权限中查询到信息,开始查询缓存", e);
                try {
                    orgVOS.addAll(permissionService.getOrgBaseCacheBySapCode(Arrays.stream(split).distinct().collect(Collectors.toList()), OrgTypeEnum.ORG_TYPE_BUSINESS.getCode())
                            .stream().map(v -> {
                                OrgVO orgVO = new OrgVO();
                                orgVO.setId(v.getBusinessOrgId());
                                orgVO.setName(v.getBusinessShortName());
                                orgVO.setShortName(v.getBusinessShortName());
                                orgVO.setOrgPath(v.getOrgPath());
                                orgVO.setType(v.getType());
                                orgVO.setParentId(v.getPlatformOrgId());
                                orgVO.setOutId(v.getBusinessId());
                                orgVO.setSapcode(v.getBusinessSapCode());
                                return orgVO;
                            }).collect(Collectors.toList()));
                } catch (Exception e1) {
                    sendJobErrorMsg2Wechat(recalculationCompany, "查询权限与缓存异常");
                    return;
                }
            }
            String callBackUrl = "http://iscm-sync/api/internal/recalculation/store/apply/tocCallbackRecalculation";
            List<String> successCompanys = new ArrayList<>();
            for (OrgVO orgVO : orgVOS) {
                Long tocId = getTocId(orgVO, DateUtils.DATE_SAP_PATTERN);
                StoreApplyParamDTO paramDTO = null;
                try {
                    paramDTO = storeApplyParamService.getAutoApplyList(orgVO.getId(), null,ApplyParamTypeEnum.AUTO_APPLY_TIME.getCode(), null,null);
                } catch (Exception e) {
                    sendJobErrorMsg2Wechat(orgVO.getSapcode(), "查询企业自动请货时间异常");
                    continue;
                }
                if (Objects.isNull(paramDTO)) {
                    sendJobErrorMsg2Wechat(orgVO.getSapcode(), "没有配置自动请货时间");
                    continue;
                }
                AutoApplyParamDTO autoApplyTimeDTO = paramDTO.getAutoApplyTimeDTO();
                if (Objects.isNull(autoApplyTimeDTO) || StringUtils.isBlank(autoApplyTimeDTO.getParamValue())) {
                    logger.info("企业编码:{}没有配置自动请货时间", orgVO.getSapcode());
                    sendJobErrorMsg2Wechat(orgVO.getSapcode(), "没有配置自动请货时间");
                    continue;
                }
                logger.info("企业:{} 请货时间配置:{}", orgVO.getSapcode(), JSON.toJSONString(autoApplyTimeDTO));
                String nowDate = DateUtils.conventDateStrByDate(new Date(), DateUtils.DATE_PATTERN);
                Date now = DateUtils.parseDate(nowDate + " " + autoApplyTimeDTO.getParamValue(), DateUtils.DATE_MINUTE_PATTERN);
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(now);
                calendar.add(Calendar.MINUTE, delayMinutes);
                try {
                    tocService.tocTaskDelete(tocId, "Recalculation");
                } catch (Exception e) {
                    logger.error("删除复算定时任务失败,直接创建", e);
                }
                try {
                    tocService.tocTaskPush(tocId, TocUnitEnum.MONTH, "Recalculation", DateUtils.conventDateStrByPattern(calendar.getTime(), DateUtils.DATETIME_PATTERN), callBackUrl, orgVO.getSapcode());
                    successCompanys.add(orgVO.getSapcode());
                } catch (Exception e) {
                    logger.error("创建定时任务异常", e);
                    sendJobErrorMsg2Wechat(orgVO.getSapcode(), "创建定时任务异常");
                }
            }
            List<String> allList = Arrays.stream(split).collect(Collectors.toList());
            List<String> queryList = orgVOS.stream().map(OrgVO::getSapcode).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            if (queryList.size() < allList.size()) {
                String warn = allList.stream().filter(v -> !queryList.contains(v)).distinct().collect(Collectors.joining(","));
                if (StringUtils.isNotBlank(warn)) {
                    sendJobErrorMsg2Wechat(warn, "权限没有查到,无法配置定时任务");
                }
            }
            if (CollectionUtils.isNotEmpty(successCompanys)) {
                sendJobSuccessMsg2Wechat(successCompanys.stream().collect(Collectors.joining(",")), DateUtils.conventDateStrByDate(new Date()) + "已成功创建了[" + successCompanys.size() +  "]家企业的定时任务");
            } else {
                sendJobErrorMsg2Wechat(recalculationCompany, "没有创建定时任务,请及时查询");
            }
        } catch (Exception e) {
            logger.error("根据配置表创建复算定时任务失败", e);
            throw e;
        }
    }

    private Long getTocId(OrgVO orgVO, String datePattern) {
        String tocIdStr = orgVO.getId().toString() + DateUtils.conventDateStrByDate(new Date(), datePattern);
        // 避免long溢出, 超出18位后截取后18位
        if (tocIdStr.length() > 18) {
            return Long.valueOf(tocIdStr.substring(tocIdStr.length() - 18, tocIdStr.length()));
        } else {
            return Long.valueOf(tocIdStr);
        }
    }

    @Override
    public void deleteByCompanyCodeAndApplyDate(String companyCode, String applyDate) throws Exception {
        try {
            IscmRecalculationRecordExample example = new IscmRecalculationRecordExample();
            example.createCriteria().andApplyDateEqualTo(DateUtils.parse(applyDate)).andCompanyCodeEqualTo(companyCode);
            iscmRecalculationRecordMapper.deleteByExample(example);
        } catch (Exception e) {
            logger.error("根据企业编码请货时间删除单据失败", e);
        }
    }

    @Override
    public void pushRecalculationData(List<String> applyNos) throws Exception {
        try {
            if (CollectionUtils.isNotEmpty(applyNos)) {
                logger.info("企业:{}开始推送mb, 待推送的单号:{}", applyNos);
                for (String applyNo : applyNos) {
                    pushToHd(null, applyNo);
                }
            }
        } catch (Exception e) {
            logger.error("推送复算数据至海典失败", e);
        }
    }

    @Override
    public void tocCallbackRecalculation(TocCallbackParam param) throws Exception {
        try {
            logger.info("企业:{}异步复算门店请货任务开始", param.getData());
            producer.send(param);

//            executor.execute(() -> {
//                try {
//                    logger.info("企业:{}异步复算门店请货任务开始", param.getData());
//                    recalculationByCompanyCode(param.getData());
//                    logger.info("企业:{}异步复算门店请货任务完毕", param.getData());
//                } catch (Exception e) {
//                    logger.error("企业:" + param.getData() + "异步复算门店请货任务失败:", e);
//                }
//            });
        } catch (Exception e) {
            logger.error("回调复算失败", e);
            throw e;
        }
    }

    @Override
    public Boolean deleteCache(String companyCode, String applyDate) throws Exception {
        try {
            String cacheKey =  recalculationKey + companyCode + "-" + (StringUtils.isBlank(applyDate) ? DateUtils.conventDateStrByDate(new Date(), DateUtils.DATE_SAP_PATTERN) : DateUtils.conventDateStrByDate(DateUtils.parseDate(applyDate, DateUtils.DATE_PATTERN), DateUtils.DATE_SAP_PATTERN));
            logger.info("删除企业:{},缓存key:{}", companyCode, cacheKey);
            RBucket<String> bucket = redissonClient.getBucket(cacheKey);
            return bucket.delete();
        } catch (Exception e) {
            logger.error("删除企业缓存失败", e);
            throw e;
        }
    }

    @Override
    public Long deleteRecalculationRecord(Integer days) throws Exception {
        try {
            if (Objects.isNull(days)) {
                throw new BusinessErrorException("定时删除复算记录天数不能为空");
            }
            Date fetureDate = DateUtils.getFetureDate(Math.negateExact(days));
            IscmRecalculationRecordExample example = new IscmRecalculationRecordExample();
            example.createCriteria().andApplyDateLessThanOrEqualTo(fetureDate);
            long count = iscmRecalculationRecordMapper.countByExample(example);
            if (count <= 0L) {
                return 0L;
            }
            example.setLimit(5000);
            long loopSize = (count / 5000) + 1L;
            for (int i = 0; i < loopSize; i++) {
                example.setLimit(5000);
                iscmRecalculationRecordExtendMapper.deleteByExample(example);
            }
            return count;
        } catch (Exception e) {
            logger.error("定时删除复算记录失败", e);
            throw e;
        }
    }

    @Override
    public void reRecalculationByCompanyCode(String companyCode, Boolean forceReAble) throws Exception {
        try {
            logger.info("企业:{}开始强制手工重新复算", companyCode);
            IscmRecalculationRecordExample example = new IscmRecalculationRecordExample();
            example.createCriteria().andApplyDateEqualTo(new Date()).andCompanyCodeEqualTo(companyCode);
            long count = iscmRecalculationRecordMapper.countByExample(example);
            if (!forceReAble && count > 0L) {
                throw new BusinessErrorException("企业" + companyCode + "今天已复算过,不能重推了");
            }
            String dateStr = DateUtils.conventDateStrByDate(new Date(), DateUtils.DATE_PATTERN);
            deleteCache(companyCode, dateStr);
            deleteByCompanyCodeAndApplyDate(companyCode, dateStr);
            // TODO: 9/19/22 删除复算log表
            recalculationByCompanyCode(companyCode);
            logger.info("企业:{}强制手工重新复算完毕", companyCode);
        }  catch (Exception e) {
            logger.error("企业" + companyCode + "强制手工重新复算失败", e);
            throw e;
        }
    }

    @Override
    public Long deleteRecalculationLog(Integer days) throws Exception {
        try {
            if (Objects.isNull(days)) {
                throw new BusinessErrorException("定时删除复算日志天数不能为空");
            }
            Date fetureDate = DateUtils.getFetureDate(Math.negateExact(days));
            IscmRecalculationNoResultLogExample example = new IscmRecalculationNoResultLogExample();
            example.createCriteria().andApplyDateLessThanOrEqualTo(fetureDate);
            long count = iscmRecalculationNoResultLogMapper.countByExample(example);
            example.setLimit(5000);
            if (count > 0L) {
                long loopSize = (count / 5000) + 1L;
                for (int i = 0; i < loopSize; i++) {
                    iscmRecalculationNoResultLogExtendMapper.deleteByExample(example);
                }
            }
            return count;
        } catch (Exception e) {
            logger.error("定时删除复算记录失败", e);
            throw e;
        }

    }

    @Override
    public void recalculationByCompanyCodeAndStoreCodes(String companyCode, String storeCodesStr) throws Exception {
        List<String> errorStoreCodes = new ArrayList<>();
        try {
            if (StringUtils.isBlank(storeCodesStr)) {
                logger.info("企业:{}没有需要复算的数据", companyCode);
                return;
            }
            List<String> storeCodes = Arrays.stream(storeCodesStr.split(",")).filter(StringUtils::isNotBlank).map(v -> v.trim()).collect(Collectors.toList());
            OrgVO company = permissionService.queryOrgInfoBySapCodes(Lists.newArrayList(companyCode), OrgTypeEnum.ORG_TYPE_BUSINESS.getCode()).stream().filter(v -> StringUtils.isNotBlank(v.getSapcode())).findFirst().orElseThrow(() -> new BusinessErrorException("企业编码:" + companyCode + "没有查询到对应企业"));
            // 取平台
            List<OrgDTO> orgDTOS = permissionService.listDirectParentOrgByOrgId(company.getId());
            Optional<OrgDTO> platform = orgDTOS.stream().filter(v -> v.getType().equals(OrgTypeEnum.ORG_TYPE_REGIONAL_PLATFORM.getCode())).findAny();
            StoreApplyParamDTO autoApplyList = storeApplyParamService.getAutoApplyList(company.getId(), null,ApplyParamTypeEnum.MIDDLE_PACKAGE_GOODS_APPLY.getCode(), null,null);
            StoreApplyParamDTO paramDTO = storeApplyParamService.getAutoApplyList(company.getId(),null, ApplyParamTypeEnum.AUTO_APPLY.getCode(), null, null);
            List<AutoApplyParamDTO> specialGoodsCtrlDTOS = paramDTO.getSpecialGoodsCtrlDTOS();
            BigDecimal specialOnceLimit = null;
            BigDecimal specialThirtyDaysLimit = null;
            logger.info("specialGoodsCtrlDTOS:{}", JSON.toJSONString(specialGoodsCtrlDTOS));
            if (CollectionUtils.isNotEmpty(specialGoodsCtrlDTOS)) {
                if ("true".equals(specialGoodsCtrlDTOS.stream().filter(v -> v.getParamCode().equals(ApplyParamCodeEnum.SPECIAL_CTRL_GOODS_SWITCH.getCode())).findFirst().orElse(new AutoApplyParamDTO()).getParamValue())) {
                    AutoApplyParamDTO specialOnceLimitDTO = specialGoodsCtrlDTOS.stream().filter(v -> v.getParamCode().equals(ApplyParamCodeEnum.SPECIAL_GOODS_ONCE_APPLY_LIMIT.getCode())).findFirst().orElse(null);
                    if (Objects.nonNull(specialOnceLimitDTO)) {
                        specialOnceLimit = new BigDecimal(specialOnceLimitDTO.getParamValue());
                    }
                    AutoApplyParamDTO specialThirtyDaysLimitDTO = specialGoodsCtrlDTOS.stream().filter(v -> v.getParamCode().equals(ApplyParamCodeEnum.SPECIAL_GOODS_PER_THIRTY_DAYS_APPLY_LIMIT.getCode())).findFirst().orElse(null);
                    if (Objects.nonNull(specialThirtyDaysLimitDTO)) {
                        specialThirtyDaysLimit = new BigDecimal(specialThirtyDaysLimitDTO.getParamValue());
                    }
                } else {
                    logger.info("企业:" + companyCode + "没有开启特管商品请货控制.");
                }
            } else {
                logger.info("企业:" + companyCode + "没有配置特管商品请货控制.");
            }
            logger.info("specialOnceLimit:{}, specialThirtyDaysLimit:{}", specialOnceLimit, specialThirtyDaysLimit);
            AutoApplyParamDTO combinedCodeRange = paramDTO.getCombinedCodeRange();
            logger.info("combinedCodeRange:{}", JSON.toJSONString(combinedCodeRange));
            CombinedCodeRangeEnum combinedCodeRangeEnum = CombinedCodeRangeEnum.getEnumByCode(null != combinedCodeRange ? combinedCodeRange.getParamValue() : null);
            for (String storeCode : storeCodes) {
                try {
//                    recalculationByStores(platform, company, storeCode, autoApplyList, specialOnceLimit, specialThirtyDaysLimit, combinedCodeRangeEnum);
                    recalculationByStores(platform, company, storeCode, autoApplyList, specialOnceLimit, specialThirtyDaysLimit);
                } catch (Exception e) {
                    logger.info("企业:" + company.getSapcode() + "门店:" + storeCode + "复算请货失败", e);
                    errorStoreCodes.add(storeCode);
                }
            }
            logger.info("企业:{}复算完毕", companyCode);
        } catch (Exception e) {
            throw e;
        } finally {
            if (CollectionUtils.isNotEmpty(errorStoreCodes)) {
                insertWarnReport(companyCode, errorStoreCodes);
                sendErrorMsg2Wechat(companyCode, errorStoreCodes);
            }
        }
    }

    @Override
    public List<WarnReportDTO> getStoreApplyRecord(WarnReportParam param) throws Exception {
        try {
            if (StringUtils.isBlank(param.getCompanyCode())) {
                throw new BusinessErrorException("请选择企业");
            }
            List<BdpWarnReportDTO> bdpWarnReportDTOS = iscmBdpApplyInfoExtendMapper.getBdpWarnByCompanyCode(param.getCompanyCode(), param.getStoreCodes(), new Date());
            if (CollectionUtils.isEmpty(bdpWarnReportDTOS)) {
                return new ArrayList<>();
            }
            Map<String, IscmWarnReportDTO> iscmWarnReportMap = iscmRecalculationRecordExtendMapper.getIscmWarnByCompanyCode(param.getCompanyCode(), param.getStoreCodes(), new Date()).stream().collect(Collectors.toMap(IscmWarnReportDTO::getStoreCode, Function.identity(), (k1,k2) -> k1));
            if (StringUtils.isNotBlank(param.getStatus())) {
                if (Constants.YES.equals(param.getStatus())) {
                    bdpWarnReportDTOS = bdpWarnReportDTOS.stream().filter(v -> iscmWarnReportMap.keySet().contains(v.getStoreCode())).collect(Collectors.toList());
                } else {
                    bdpWarnReportDTOS = bdpWarnReportDTOS.stream().filter(v -> !iscmWarnReportMap.keySet().contains(v.getStoreCode())).collect(Collectors.toList());
                }
            }
            Map<String, IscmWarnReportDTO> iscmUpdateWarnReportMap = iscmRecalculationRecordExtendMapper.getIscmWarnUpdateByCompanyCode(param.getCompanyCode(), param.getStoreCodes(), new Date()).stream().collect(Collectors.toMap(IscmWarnReportDTO::getStoreCode, Function.identity(), (k1,k2) -> k1));
            return bdpWarnReportDTOS.stream().map(v -> {
                WarnReportDTO dto = new WarnReportDTO();
                v.setBdpPushTimeStr(DateUtils.conventDateStrByDate(v.getBdpPushTime(), DateUtils.DATETIME_PATTERN));
                dto.setBdpWarnReportDTO(v);
                IscmWarnReportDTO iscmWarnReportDTO = iscmWarnReportMap.get(v.getStoreCode());
                if (Objects.nonNull(iscmWarnReportDTO)) {
                    Integer updateNum = Optional.ofNullable(iscmUpdateWarnReportMap.get(v.getStoreCode())).orElse(new IscmWarnReportDTO()).getRecalculUpdateNum();
                    iscmWarnReportDTO.setRecalculTimeStr(DateUtils.conventDateStrByDate(iscmWarnReportDTO.getRecalculTime(), DateUtils.DATETIME_PATTERN));
                    iscmWarnReportDTO.setIscmPushTimeStr(DateUtils.conventDateStrByDate(iscmWarnReportDTO.getIscmPushTime(), DateUtils.DATETIME_PATTERN));
                    iscmWarnReportDTO.setIscmPushStatus("是");
                    iscmWarnReportDTO.setRecalculUpdateNum(updateNum);
                    dto.setIscmWarnReportDTO(iscmWarnReportDTO);
                } else {
                    iscmWarnReportDTO = new IscmWarnReportDTO();
                    iscmWarnReportDTO.setIscmPushStatus("否");
                }
                return dto;
            }).collect(Collectors.toList());
        } catch (BusinessErrorException e) {
            logger.warn("获取复算预警报表失败", e);
            throw e;
        } catch (Exception e) {
            logger.error("获取复算预警报表失败", e);
            throw e;
        }
    }

    @Override
    public List<CompanyCommonDTO> getWarnReportComList() throws Exception {
        try {
            return permissionService.queryOrgInfoBySapCodes(Lists.newArrayList(StringUtils.split(recalculationCompany, ","))).stream().map(v -> {
                CompanyCommonDTO dto = new CompanyCommonDTO();
                dto.setCompanyOrgId(v.getId());
                dto.setBusinessId(v.getOutId());
                dto.setCompanyCode(v.getSapcode());
                dto.setCompanyName(v.getShortName());
                return dto;
            }).collect(Collectors.toList());
        } catch (BusinessErrorException e) {
            logger.warn("获取预警报表的企业列表失败", e);
            throw e;
        } catch (Exception e) {
            logger.error("获取预警报表的企业列表失败", e);
            throw e;
        }
    }

    @Override
    public List<StoreCommonDTO> getWarnReportStoreList(String companyCode) throws Exception{
        try {
            if (StringUtils.isBlank(companyCode)) {
                throw new BusinessErrorException("企业编码不能为空");
            }
            List<String> storeCodes = iscmBdpApplyInfoExtendMapper.getStoreCodesByCompanyCode(companyCode, new Date());
            if (CollectionUtils.isEmpty(storeCodes)) {
                return new ArrayList<>();
            }
            return permissionService.queryOrgInfoBySapCodes(storeCodes).stream().map(v -> {
                StoreCommonDTO dto = new StoreCommonDTO();
                dto.setStoreOrgId(v.getId());
                dto.setStoreId(v.getOutId());
                dto.setStoreCode(v.getSapcode());
                dto.setStoreName(v.getShortName());
                return dto;
            }).collect(Collectors.toList());
        } catch (BusinessErrorException e) {
            logger.warn("根据企业编码获取预警报表的门店列表失败", e);
            throw e;
        } catch (Exception e) {
            logger.error("根据企业编码获取预警报表的门店列表失败", e);
            throw e;
        }
    }

    @Override
    public IscmRecalculationWarnRecordDTO getRecalculationWarnReportByCompanyCode(String companyCode) throws Exception {
        try {
            if (StringUtils.isBlank(companyCode)) {
                throw new BusinessErrorException("请选择企业");
            }
            IscmRecalculationWarnRecordExample example = new IscmRecalculationWarnRecordExample();
            example.createCriteria().andCompanyCodeEqualTo(companyCode).andApplyDateEqualTo(new Date()).andDealStatusEqualTo(RecalculationDealStatusEnum.NO_DEAL.getCode());
            example.setOrderByClause(" id desc");
            example.setLimit(1);
            List<IscmRecalculationWarnRecord> records = iscmRecalculationWarnRecordMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(records)) {
                throw new BusinessErrorException("所选企业没有异常或正在处理中");
            }
            IscmRecalculationWarnRecord warnRecord = records.get(0);
            IscmRecalculationWarnRecordDTO recordDTO = new IscmRecalculationWarnRecordDTO();
            BeanUtils.copyProperties(warnRecord, recordDTO);
            recordDTO.setApplyDate(DateUtils.conventDateStrByDate(warnRecord.getApplyDate(), DateUtils.DATE_PATTERN));
            recordDTO.setDealStatusDesc(RecalculationDealStatusEnum.getEnumByCode(warnRecord.getDealStatus()).getName());
            recordDTO.setGmtCreate(DateUtils.conventDateStrByDate(warnRecord.getGmtCreate(), DateUtils.DATE_PATTERN));
            recordDTO.setGmtUpdate(DateUtils.conventDateStrByDate(warnRecord.getGmtUpdate(), DateUtils.DATE_PATTERN));
            return recordDTO;
        } catch (BusinessErrorException e) {
            logger.warn("移动端根据企业编码查询告警记录失败", e);
            throw e;
        } catch (Exception e) {
            logger.warn("移动端根据企业编码查询告警记录失败", e);
            throw e;
        }
    }

    @Override
    public void exportRecalculationWarnReport(String reportDateStart, String reportDateEnd, String companyCode, HttpServletResponse response) throws Exception {
        try {
            Date dateStart = Optional.ofNullable(DateUtils.parse(reportDateStart)).orElseThrow(() -> new BusinessErrorException("开始日期格式有误,正确格式yyyy-MM-dd"));
            Date dateEnd = Optional.ofNullable(DateUtils.parse(reportDateEnd)).orElseThrow(() -> new BusinessErrorException("结束日期格式有误,正确格式yyyy-MM-dd"));
            if(dateStart.after(dateEnd)) {
                throw new BusinessErrorException("开始日期大于结束日期");
            }
            IscmRecalculationWarnRecordExample example = new IscmRecalculationWarnRecordExample();
            IscmRecalculationWarnRecordExample.Criteria criteria = example.createCriteria();
            criteria.andApplyDateBetween(dateStart, dateEnd);
            if (StringUtils.isNotBlank(companyCode)) {
                criteria.andCompanyCodeEqualTo(companyCode);
            }
            example.setOrderByClause(" gmt_create desc");
            List<IscmRecalculationWarnRecordDTO> recordDTOS = iscmRecalculationWarnRecordMapper.selectByExample(example).stream().map(v -> {
                IscmRecalculationWarnRecordDTO dto = new IscmRecalculationWarnRecordDTO();
                BeanUtils.copyProperties(v, dto);
                dto.setApplyDate(DateUtils.conventDateStrByDate(v.getApplyDate(), DateUtils.DATE_PATTERN));
                dto.setDealStatusDesc(RecalculationDealStatusEnum.getEnumByCode(v.getDealStatus()).getName());
                dto.setGmtCreate(DateUtils.conventDateStrByDate(v.getGmtCreate(), DateUtils.DATE_PATTERN));
                dto.setGmtUpdate(DateUtils.conventDateStrByDate(v.getGmtUpdate(), DateUtils.DATE_PATTERN));
                return dto;
            }).collect(Collectors.toList());
            String fileName = "告警记录" + DateUtils.conventDateStrByDate(new Date(), DateUtils.DATETIME_PATTERN_COMPACT);
            fileName = URLEncoder.encode(fileName, "UTF-8");
            List<ListToExcelMultiSheetDTO> listDto = new ArrayList<>();
            ListToExcelMultiSheetDTO dto = new ListToExcelMultiSheetDTO();
            dto.setSheetName("sheet0");
            dto.setFieldMap(IscmRecalculationWarnRecordDTO.getExportMap());
            dto.setListGroup(recordDTOS);
            listDto.add(dto);
            HutoolUtil.listToExcelMultiSheet(fileName, listDto, response);
        } catch (BusinessErrorException e) {
            logger.warn("导出查询告警记录失败", e);
            throw e;
        } catch (Exception e) {
            logger.error("导出查询告警记录失败", e);
            throw e;
        }
    }

    @Override
    public void exportStoreApplyReport(WarnReportParam param, HttpServletResponse response) throws Exception {
        try {
            List<ExportWarnReportDTO> storeApplyRecords = getStoreApplyRecord(param).stream().map(v -> {
                ExportWarnReportDTO dto = new ExportWarnReportDTO();
                dto.setCompanyCode(param.getCompanyCode());
                if (Objects.nonNull(v.getBdpWarnReportDTO())) {
                    BeanUtils.copyProperties(v.getBdpWarnReportDTO(), dto);
                }
                if (Objects.nonNull(v.getIscmWarnReportDTO())) {
                    BeanUtils.copyProperties(v.getIscmWarnReportDTO(), dto);
                }
                if (Objects.nonNull(v.getPosWarnReportDTO())) {
                    BeanUtils.copyProperties(v.getPosWarnReportDTO(), dto);
                }
                return dto;
            }).collect(Collectors.toList());
            String fileName = "门店请货数据" + DateUtils.conventDateStrByDate(new Date(), DateUtils.DATETIME_PATTERN_COMPACT);
            fileName = URLEncoder.encode(fileName, "UTF-8");
            List<ListToExcelMultiSheetDTO> listDto = new ArrayList<>();
            ListToExcelMultiSheetDTO dto = new ListToExcelMultiSheetDTO();
            dto.setSheetName("sheet0");
            dto.setFieldMap(ExportWarnReportDTO.getExportMap());
            dto.setListGroup(storeApplyRecords);
            listDto.add(dto);
            HutoolUtil.listToExcelMultiSheet(fileName, listDto, response);

        } catch (BusinessErrorException e) {
            logger.warn("导出复算预警报表-门店请货记录失败", e);
            throw e;
        } catch (Exception e) {
            logger.error("导出复算预警报表-门店请货记录失败", e);
            throw e;
        }
    }

    @Override
    public void recalculationByWarnReport(Long warnReportId) throws Exception {
        IscmRecalculationWarnRecord warnRecord = Optional.ofNullable(iscmRecalculationWarnRecordMapper.selectByPrimaryKey(warnReportId)).orElseThrow(() -> new BusinessErrorException("请选择正确的记录"));
        if (!warnRecord.getDealStatus().equals(RecalculationDealStatusEnum.NO_DEAL.getCode())) {
            throw new BusinessErrorException("所选报警记录已经在处理");
        }
        if (!DateUtils.conventDateStrByDate(warnRecord.getApplyDate(), DateUtils.DATE_PATTERN).equals(DateUtils.conventDateStrByDate(new Date(), DateUtils.DATE_PATTERN))) {
            throw new BusinessErrorException("只能处理当天的记录");
        }
        warnRecord.setDealStatus(RecalculationDealStatusEnum.DEALING.getCode());
        iscmRecalculationWarnRecordMapper.updateByPrimaryKeySelective(warnRecord);
        executor.execute(() -> {
            try {
                if (StringUtils.isNotBlank(warnRecord.getErrorStoreCodes())) {
                    recalculationByCompanyCodeAndStoreCodes(warnRecord.getCompanyCode(), warnRecord.getErrorStoreCodes());
                } else {
                    recalculationByCompanyCode(warnRecord.getCompanyCode());
                }
            } catch (Exception e) {
                logger.error("根据预警记录复算失败", e);
            } finally {
                warnRecord.setDealStatus(RecalculationDealStatusEnum.DEALED.getCode());
                iscmRecalculationWarnRecordMapper.updateByPrimaryKeySelective(warnRecord);
            }
        });
    }

    @Override
    public PageResponse<List<IscmRecalculationWarnRecordDTO>> getRecalculationWarnReport(String reportDateStart, String reportDateEnd, String companyCode, Integer page, Integer pageSize) throws Exception {
        try {
            if(null == page || null == pageSize){
                throw new BusinessErrorException("分页参数不能为空");
            }
            if(page < 0 || pageSize <= 0){
                throw new BusinessErrorException("分页参数有误");
            }
            Date dateStart = Optional.ofNullable(DateUtils.parse(reportDateStart)).orElseThrow(() -> new BusinessErrorException("开始日期格式有误,正确格式yyyy-MM-dd"));
            Date dateEnd = Optional.ofNullable(DateUtils.parse(reportDateEnd)).orElseThrow(() -> new BusinessErrorException("结束日期格式有误,正确格式yyyy-MM-dd"));
            if(dateStart.after(dateEnd)) {
                throw new BusinessErrorException("开始日期大于结束日期");
            }
            PageResponse<List<IscmRecalculationWarnRecordDTO>> pageResponse = new PageResponse<>();
            pageResponse.setPageSize(pageSize);
            pageResponse.setPage(page);
            pageResponse.setTotalSize(0L);
            pageResponse.setCode(HttpStatus.OK.value());
            pageResponse.setResult(new ArrayList<>());
            IscmRecalculationWarnRecordExample example = new IscmRecalculationWarnRecordExample();
            IscmRecalculationWarnRecordExample.Criteria criteria = example.createCriteria();
            criteria.andApplyDateBetween(dateStart, dateEnd);
            if (StringUtils.isNotBlank(companyCode)) {
                criteria.andCompanyCodeEqualTo(companyCode);
            }
            long count = iscmRecalculationWarnRecordMapper.countByExample(example);
            if (count <= 0L) {
                return pageResponse;
            }
            pageResponse.setTotalSize(count);
            example.setLimit(pageSize);
            example.setOffset(Long.valueOf(page * pageSize));
            example.setOrderByClause(" gmt_create desc");
            pageResponse.setResult(iscmRecalculationWarnRecordMapper.selectByExample(example).stream().map(v -> {
                IscmRecalculationWarnRecordDTO dto = new IscmRecalculationWarnRecordDTO();
                BeanUtils.copyProperties(v, dto);
                dto.setApplyDate(DateUtils.conventDateStrByDate(v.getApplyDate(), DateUtils.DATE_PATTERN));
                dto.setDealStatusDesc(RecalculationDealStatusEnum.getEnumByCode(v.getDealStatus()).getName());
                dto.setGmtCreate(DateUtils.conventDateStrByDate(v.getGmtCreate(), DateUtils.DATE_PATTERN));
                dto.setGmtUpdate(DateUtils.conventDateStrByDate(v.getGmtUpdate(), DateUtils.DATE_PATTERN));
                return dto;
            }).collect(Collectors.toList()));
            return pageResponse;
        } catch (BusinessErrorException e) {
            logger.warn("查询告警记录失败", e);
            throw e;
        } catch (Exception e) {
            logger.warn("查询告警记录失败", e);
            throw e;
        }
    }

    @Override
    public void pushToHd(String companyCode, String applyNo) throws Exception {
        IscmRecalculationRecordExample recordExample = new IscmRecalculationRecordExample();
        IscmRecalculationRecordExample.Criteria criteria = recordExample.createCriteria();
        if(StringUtils.isNotBlank(companyCode)) {
            criteria.andCompanyCodeEqualTo(companyCode);
        }
        criteria.andApplyNoEqualTo(applyNo).andApplyDateEqualTo(new Date());
        List<IscmRecalculationRecord> records = iscmRecalculationRecordMapper.selectByExample(recordExample);
        if (CollectionUtils.isEmpty(records)) {
            logger.info("企业:{}单号:{}没有数据无需推送", companyCode, applyNo);
            return;
        }
        List<Map<String, Object>> details = new ArrayList<>();
        records.forEach(data -> {
            Map<String, Object> detailList = new HashMap<>();
            detailList.put("bdpbillno", data.getApplyNo());
            detailList.put("bdpbillrow", data.getApplyLine());
            detailList.put("warecode", data.getGoodsNo());
            detailList.put("wareqty", data.getBdpApplyTotal());
            detailList.put("confidence_pos", "");
            detailList.put("apptype", data.getApplyGoodsType());
            detailList.put("final_wareqty", data.getApplyTotal());
            boolean isGoodsSuggest = !ApplyGoodsTypeEnum.getEnumByCode(data.getApplyGoodsType()).equals(ApplyGoodsTypeEnum.GOOD_SUGGEST_GOODS);
            detailList.put("recommendReason", isGoodsSuggest ? "" : data.getRecommendReason());
            detailList.put("promotionDesc", isGoodsSuggest ? "" : data.getPromotionDesc());
            detailList.put("compositeNew", isGoodsSuggest ? null : data.getCompositeNew());
            detailList.put("thirtySalesQuantity", isGoodsSuggest ? null : data.getThirtySalesQuantity());
            detailList.put("promotionName", data.getPromotionTitle());
            detailList.put("promotionDateStart", data.getPromotionStartDate());
            detailList.put("promotionDateEnd", data.getPromotionEndDate());
            detailList.put("applyReason", data.getApplyReason());
            detailList.put("bdp_lowestqty", data.getMinDisplayQty());
            detailList.put("bdp_storeqty", data.getApplyStock());
            detailList.put("field_one", null == data.getDealSuggest() ? "" : data.getDealSuggest().toString());
            if (StringUtils.isNotBlank(data.getApplyReason()) && (data.getApplyReason().contains(RecalculationReasonEnum.REASON_THREE.getCode().toString()) || data.getApplyReason().contains(RecalculationReasonEnum.REASON_SIX.getCode().toString()))) {
                detailList.put("bdp_overqty", data.getScarceLimit());
            }
            details.add(detailList);
        });
        Map<String, Object> headerjson = new HashMap<>();
        IscmRecalculationRecord record = records.get(0);
        headerjson.put("POSTYPE", "1001");
        headerjson.put("bdpbillno", record.getApplyNo());
        headerjson.put("applyno", record.getApplyNo());
        headerjson.put("busno", record.getStoreCode());
        headerjson.put("BEDAT", DateUtils.conventDateStrByDate(new Date(), DateUtils.DATETIME_PATTERN));
        headerjson.put("EXPECTEDDATE", DateUtils.conventDateStrByDate(DateUtils.getFetureDateBydays(new Date(), planTimeDelayDays), DateUtils.DATETIME_PATTERN));
        headerjson.put("DETAILS", details);

        // 取配置
        boolean flag = false;
        if (StringUtils.isNotBlank(mbHotReturnProperties)) {
            JSONObject returnJson = JSONObject.parseObject(mbHotReturnProperties);
            flag = returnJson.getBoolean("return");
        }
        // 如果回滚的话, 走原下发海典方法
        if (flag) {
            Map<String, Object> mbConfig = MBUtils.getMbConfig(recalculationPrpoBak);
            String pushData = MBUtils.assembleParam(mbConfig, JSON.toJSONString(headerjson));
            mbUtils.pushToMB(MBUtils.getMbUrl(recalculationPrpoBak), pushData);
        } else {
            // 增加 compId
            IscmRecalculationRecord record1 = records.get(0);
            Long storeOrgId = record1.getStoreOrgId();
            List<OrgInfoBaseCache> cacheStoreList = permissionService.getOrgInfoBaseCacheListByStoreOrgIds(Collections.singletonList(storeOrgId));
            String comId = "";
            if (CollectionUtils.isEmpty(cacheStoreList)) {
                MdmStoreBaseDTO mdmStoreBaseDTO = storeService.findByStoreNo(record1.getStoreCode());
                if (null == mdmStoreBaseDTO || StringUtils.isBlank(mdmStoreBaseDTO.getComId())) {
                    throw new BusinessErrorException("无法找到门店ID为: ["+ storeOrgId +"]的缓存信息以及store服务信息");
                }
                comId = mdmStoreBaseDTO.getComId();
            } else {
                comId = cacheStoreList.get(0).getComId();
            }
            Map<String, Object> mbConfig = MBUtils.getMbConfig(recalculationPrpo);
            String pushData = MBUtils.assembleParam(mbConfig, JSON.toJSONString(headerjson), comId);
            mbUtils.pushToMB(MBUtils.getMbUrl(recalculationPrpo), pushData);
        }
    }

    @Override
    public void pushToWmsByCompany(String companyCode, String applyNo) {
        if (StringUtils.isBlank(companyCode)) {
            throw new BusinessErrorException("企业编码不能为空");
        }
        if (StringUtils.isNotBlank(applyNo)) {
            pushToWms(companyCode, applyNo);
            return;
        } else {
            List<String> applyNos = iscmRecalculationRecordExtendMapper.getApplyNosByCompanyCode(companyCode, new Date());
            if (CollectionUtils.isEmpty(applyNos)) {
                throw new BusinessErrorException("没有需要推送的数据");
            }
            for (String no : applyNos) {
                Arrays.stream(StringUtils.split(no, ",")).forEach(v -> {
                    if (!v.endsWith("ZY")) {
                        pushToWms(companyCode, v);
                    }
                });
            }
        }
    }
    public void pushToWms(String companyCode, String applyNo) {
        if (StringUtils.isBlank(companyCode) && StringUtils.isBlank(applyNo)) {
            throw new BusinessErrorException("企业编码/请货单号不能都为空");
        }
        IscmRecalculationRecordExample recordExample = new IscmRecalculationRecordExample();
        IscmRecalculationRecordExample.Criteria criteria = recordExample.createCriteria();
        if(StringUtils.isNotBlank(companyCode)) {
            criteria.andCompanyCodeEqualTo(companyCode);
        }
        if(StringUtils.isNotBlank(applyNo)) {
            criteria.andApplyNoIn(Lists.newArrayList(applyNo, applyNo + "ZY"));
        }
        criteria.andApplyDateEqualTo(new Date()).andPurchaseTypeNotEqualTo(PurchaseTypeEnum.MDCG.getCode()).andPurchaseChannelNotEqualTo("B");
        List<IscmRecalculationRecord> records = iscmRecalculationRecordMapper.selectByExample(recordExample);
        Map<String, List<IscmRecalculationRecord>> recordsMap = records.stream().filter(v -> YNEnum.YES.getType().equals(v.getLawful()) && StringUtils.isNotBlank(v.getWarehouseCode())).collect(Collectors.groupingBy(IscmRecalculationRecord::getWarehouseCode));
        if (MapUtils.isEmpty(recordsMap)) {
            logger.info("没有需要推送的数据");
            return;
        }
        List<Long> ids = new ArrayList<>();
        try {
            ids.addAll(tocService.getDistributedIDList(IdGenConfig.IDGEN_RECALCULATION_RECORD, recordsMap.size())) ;
        } catch (Exception e) {
            sendWmsErrorMsg2Wechat(companyCode, applyNo, null, "分布式发号器有误,请重新执行");
            return;
        }
        pushToWms(recordsMap, ids);
    }

    @Override
    public void monitorNonGen() {
        IscmRecalculationRecordExample example = new IscmRecalculationRecordExample();
        example.createCriteria().andApplyDateEqualTo(new Date());
        long count = iscmRecalculationRecordMapper.countByExample(example);
        if (count <= 0L) {
            sendJobErrorMsg2Wechat("全国",  "截止到目前没有生成复算记录,请排查问题");
        }
    }

    private void pushToWms(Map<String, List<IscmRecalculationRecord>> recordsMap, List<Long> ids) {
        int index = 0;
        if (StringUtils.isBlank(pushwmsDcCodes)) {
            logger.info("没有需要推送的dc仓");
            return;
        }
        List<String> dcList = Arrays.stream(StringUtils.split(pushwmsDcCodes.trim(), ",")).distinct().collect(Collectors.toList());
        for (Map.Entry<String, List<IscmRecalculationRecord>> entry : recordsMap.entrySet()) {
            if (!dcList.contains(entry.getKey())) {
                logger.info("dc编码:{}没有在apollo配置,不需要推送");
                continue;
            }
            List<Map<String, Object>> details = new ArrayList<>();
            entry.getValue().forEach(data -> {
                Map<String, Object> detailList = new HashMap<>();
                detailList.put("ZLINENO", data.getApplyLine());
                detailList.put("BUKRS", ""); //企业编码 非必填
                detailList.put("ZWERKS", data.getWarehouseCode());
                detailList.put("ZPARTNER", data.getStoreCode());
                detailList.put("ZMATNR", data.getGoodsNo());
                detailList.put("ZTOTAL", data.getApplyTotal());
                detailList.put("ZLGORT", "1000"); // 库存地点 默认1000
                details.add(detailList);
            });
            Map<String, Object> headerjson = new HashMap<>();
            IscmRecalculationRecord record = entry.getValue().get(0);
            headerjson.put("ZBGUID", "ISCM" + UUID.randomUUID().toString().replace("-", ""));
            headerjson.put("ZPROID", "59_01");
            Long id = ids.get(index++);
            if (null == id) {
                sendWmsErrorMsg2Wechat(record.getCompanyCode(), record.getApplyNo(), null, "分布式发号器有误,请重新执行");
                return;
            }
            headerjson.put("ZBILLNO", "C" + String.format("%09d", id));
            headerjson.put("ZWERKS", record.getWarehouseCode());
            headerjson.put("ZPARTNER", record.getStoreCode());
            headerjson.put("ZEDDAT", record.getGmtCreate());
            headerjson.put("ZERNAM", "");
            headerjson.put("ZCOUNT", entry.getValue().size());
            headerjson.put("ITEM", details);
            Map<String, Object> mbConfig = MBUtils.getMbConfig(pushwmsPrpo);
            try {
                String pushData = MBUtils.assembleParam2Obj(mbConfig, headerjson);
                mbUtils.pushToMB(MBUtils.getMbUrl(pushwmsPrpo), pushData);
            } catch (Exception e) {
                sendWmsErrorMsg2Wechat(record.getCompanyCode(), record.getApplyNo(), record.getWarehouseCode(), "");
            }
        }
    }

    private void genRecord(OrgVO company, OrgVO store, List<IscmRecalculationRecord> records, IscmBdpApplyInfo apply, BigDecimal var1, BigDecimal var2, BigDecimal applyQty, String applyNo, String zyApplyNo, BigDecimal applyStock, Integer scarceLimit, String applyReason) {
        IscmRecalculationRecord record = new IscmRecalculationRecord();
        BeanUtils.copyProperties(apply, record);
        List<String> promotions = new ArrayList<>();
        promotions.add(apply.getPromotionName());
        promotions.add(apply.getPromotionWay());
        promotions.add(apply.getThresholdInfo());
        promotions.add(apply.getFavInfo());
        record.setPromotionDesc(promotions.stream().filter(StringUtils::isNotBlank).collect(Collectors.joining(",")));
        record.setBdpApplyNo(apply.getApplyNo());
        List<String> splitCompanyCodes = Arrays.stream(StringUtils.split(splitCompanys, ",")).collect(Collectors.toList());
        Long categoryId = Optional.ofNullable(apply.getCategoryId()).orElse(0L);
        if(splitCompanyCodes.contains(company.getSapcode()) && categoryId.toString().startsWith("1202")) {
            record.setApplyNo(zyApplyNo);
        } else {
            record.setApplyNo(applyNo);
        }
        record.setCompanyOrgId(company.getId());
        record.setBusinessId(company.getOutId());
        record.setCompanyOrgName(company.getShortName());
        record.setCompanyCode(company.getSapcode());
        record.setStoreOrgId(store.getId());
        record.setStoreId(store.getOutId());
        record.setStoreCode(store.getSapcode());
        record.setStoreName(store.getShortName());
        record.setBdpApplyTotal(apply.getApplyTotal());
        record.setApplyTotal(applyQty);
        record.setVarA(var1);
        record.setVarB(var2);
        record.setMiddlePackageSwitch(apply.getMiddlePackageSwitch());
        record.setMiddlePackageQty(apply.getMiddlePackageQty());
        record.setPurchaseType(apply.getPurchaseType());
        record.setPurchaseChannel(apply.getPurchaseChannel());
        record.setWarehouseCode(StringUtils.isBlank(apply.getWarehouseCode()) ? "" : apply.getWarehouseCode());
        record.setMiddlePackageQty(apply.getMiddlePackageQty());
        record.setPromotionTitle(apply.getPromotionTitle());
        record.setPromotionStartDate(apply.getPromotionStartDate());
        record.setPromotionEndDate(apply.getPromotionEndDate());
        record.setApplyStock(applyStock);
        record.setScarceLimit(scarceLimit);
        record.setApplyReason(applyReason);
        record.setDealSuggest(apply.getDealSuggest());
        records.add(record);
    }

    public static BigDecimal max(BigDecimal v1, BigDecimal v2) {
        if (v1.compareTo(v2) > 0) {
            return v1;
        }
        return v2;
    }

    public static BigDecimal min(BigDecimal v1, BigDecimal v2) {
        if (v1.compareTo(v2) < 0) {
            return v1;
        }
        return v2;
    }
}

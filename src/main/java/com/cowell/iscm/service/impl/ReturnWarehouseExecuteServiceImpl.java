package com.cowell.iscm.service.impl;

import com.alibaba.fastjson.JSON;
import com.cowell.iscm.config.Constants;
import com.cowell.iscm.entity.*;
import com.cowell.iscm.enums.*;
import com.cowell.iscm.mapper.IscmParamOrgManagementMapper;
import com.cowell.iscm.mapper.IscmStoreReturnExecuteOrderDetailMapper;
import com.cowell.iscm.mapper.IscmStoreReturnExecuteOrderMainMapper;
import com.cowell.iscm.mapper.extend.IscmStoreReturnExecuteOrderDetailExtendMapper;
import com.cowell.iscm.mapper.extend.IscmStoreReturnExecuteOrderExtendMapper;
import com.cowell.iscm.rest.errors.BusinessErrorException;
import com.cowell.iscm.service.AsyncExportFileService;
import com.cowell.iscm.service.ReturnWarehouseExecutePushService;
import com.cowell.iscm.service.ReturnWarehouseExecuteService;
import com.cowell.iscm.service.ReturnWarehouseExecuteUpdateService;
import com.cowell.iscm.service.dto.FieldMapper;
import com.cowell.iscm.service.dto.PaginationQuery;
import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.returnWarehouse.*;
import com.cowell.iscm.service.feign.*;
import com.cowell.iscm.service.feign.dto.*;
import com.cowell.iscm.service.feign.response.PageResponse;
import com.cowell.iscm.service.thread.HandlerDataExportService;
import com.cowell.iscm.utils.CommonUtil;
import com.cowell.iscm.utils.CriteriaUtils;
import com.cowell.iscm.utils.DateUtils;
import com.cowell.permission.dto.OrgDTO;
import com.cowell.permission.dto.mdm.MdmStoreBaseDTO;
import com.cowell.permission.vo.OrgTreeVO;
import com.cowell.permission.vo.OrgVO;
import com.google.common.base.CaseFormat;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 退仓执行 服务实现类
 *
 * <AUTHOR>
 */
@Service
public class ReturnWarehouseExecuteServiceImpl implements ReturnWarehouseExecuteService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private IscmStoreReturnExecuteOrderMainMapper iscmStoreReturnExecuteOrderMainMapper;
    @Autowired
    private IscmStoreReturnExecuteOrderDetailMapper iscmStoreReturnExecuteOrderDetailMapper;

    @Autowired
    private IscmStoreReturnExecuteOrderExtendMapper iscmStoreReturnExecuteOrderExtendMapper;
    @Autowired
    private IscmStoreReturnExecuteOrderDetailExtendMapper iscmStoreReturnExecuteOrderDetailExtendMapper;

    @Autowired
    private IscmParamOrgManagementMapper iscmParamOrgManagementMapper;

    @Autowired
    private ReturnWarehouseExecutePushService returnWarehouseExecutePushService;
    @Autowired
    private ReturnWarehouseExecuteUpdateService returnWarehouseExecuteUpdateService;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private PosService posService;

    @Autowired
    private StoreService storeService;

    @Autowired
    private StockcenterService stockcenterService;

    @Autowired
    private AsyncExportFileService asyncExportFileService;

    @Autowired
    private RedissonClient redissonClient;

    // 发送消息到OA的编号缓存Key(大仓+日期+001)
    private final String ISCM_PUSH_TO_OA_CODE_CACHE_KEY = "ISCM-PUSH-TO-OA-CODE-CACHE-";

    @Value("${iscm.es.query.stock.switch:true}")
    private boolean esAble;

    @Value("${iscm.mb.return_warehouse_order_to_oa.switch:true}")
    private boolean enableToOA;

    // 审批中,下发中Id缓存 Set<month+id>
    private final Set<String> issuingIdCache = Collections.synchronizedSet(new HashSet<>());

    /**
     * 获取主单缓存状态(未获取到则返回原始状态)
     *
     * @param order
     * @return
     */
    private Byte getCachedStatus(IscmStoreReturnExecuteOrderMain order) {
        Byte originStatus = order.getProcessStatus();
        if (!issuingIdCache.contains(order.getCreatedMonth() + "-" + order.getId())) {
            return originStatus;
        }
        if (ObjectUtils.equals(originStatus, StoreReturnExecuteProcessStatusEnum.NON_APPROVED.getCode())) {
            // 商品渠道属性=全国统采&采购组织=1000,审批中,否则 下发中
            if (orderToOA(order)) {
                return StoreReturnExecuteProcessStatusEnum.IN_APPROVAL.getCode();
            } else {
                return StoreReturnExecuteProcessStatusEnum.ISSUING.getCode();
            }
        }
        if (ObjectUtils.equals(originStatus, StoreReturnExecuteProcessStatusEnum.ISSUE_FAILED.getCode())) {
            return StoreReturnExecuteProcessStatusEnum.ISSUING.getCode();
        }
        return originStatus;
    }

    /**
     * 单据是否发至OA(true:商品渠道属性=全国统采&采购组织=1000)
     *
     * @param order
     * @return
     */
    private boolean orderToOA(IscmStoreReturnExecuteOrderMain order) {
        return enableToOA;
//        return enableToOA && GoodsChannelAttrEnum.PURCHASE_NATIONAL_CENTRAL.getName().equals(order.getGoodsPurChannel())
//                && GoodsPurchaseOrgEnum.PURCHASE_ORG_HEADQUARTERS.getCode().equals(order.getPurchaseOrgCode());
    }

    @Override
    public Set<WarehouseDTO> getWarehouseInfos(Integer month) {
        checkMonth(month);
        Set<WarehouseDTO> warehouseSet = iscmStoreReturnExecuteOrderExtendMapper.selectWarehouseInfos(month);
        return Optional.ofNullable(warehouseSet).orElseGet(HashSet::new);
    }

    @Override
    public Set<Long> getCheckedIds(TokenUserDTO userDTO, ReturnWarehouseExecuteParam param) throws Exception {
        checkParam(param);
        param.setStoreOrgIds(checkPerm(userDTO, param.getCompanyOrgIds(), param.getStoreOrgIds()));
        param.setPageSize(Constants.MYSQL_CRUD_ONCE_MAX_SIZE);
        Set<Long> idSet = new HashSet<>();
        int i = 0;
        for (; ; i++) {
            param.setPage(i);
            Set<Long> ids = iscmStoreReturnExecuteOrderExtendMapper.selectCheckedIds(param);
            if (CollectionUtils.isEmpty(ids)) {
                break;
            }
            idSet.addAll(ids);
        }
        // 去掉缓存中的id
        idSet.removeIf(v -> issuingIdCache.contains(param.getMonth() + "-" + v));
        return idSet;
    }

    @Override
    public Set<String> getCheckedOrderNos(TokenUserDTO userDTO, ReturnWarehouseExecuteParam param) throws Exception {
        checkParam(param);
        if (CollectionUtils.isEmpty(param.getProcessStatusList())) {
            throw new BusinessErrorException("请选择导出状态");
        }
        Byte processStatus = param.getProcessStatusList().get(0);
        StringBuilder where = new StringBuilder("(");
        for(int i = 0; i < param.getProcessStatusList().size(); i++) {
            where.append(" process_status_all like \"%|").append(param.getProcessStatusList().get(i).toString()).append("|%\"");
            if ( i < param.getProcessStatusList().size() - 1) {
                where.append(" or ");
            }
        }
        where.append(")");
        param.setStoreOrgIds(checkPerm(userDTO, param.getCompanyOrgIds(), param.getStoreOrgIds()));
        param.setPageSize(Constants.MYSQL_CRUD_ONCE_MAX_SIZE);
        param.setProcessStatusAll(where.toString());
        Set<String> orderNoSet = new HashSet<>();
        int i = 0;
        for (; ; i++) {
            param.setPage(i);
            Set<String> orderNos = iscmStoreReturnExecuteOrderExtendMapper.selectCheckedOrderNos(param);
            if (CollectionUtils.isEmpty(orderNos)) {
                break;
            }
            int perSize = orderNoSet.size();
            orderNoSet.addAll(orderNos);
            if (orderNos.size() < param.getPageSize() || orderNoSet.size() == perSize) {
                break;
            }
        }
        return orderNoSet;
    }

    @Override
    public PageResponse<List<StoreReturnExecuteOrderDTO>> pageExecuteList(TokenUserDTO userDTO, ReturnWarehouseExecuteParam param) throws Exception {
        try {
            checkParam(param);
            param.setStoreOrgIds(checkPerm(userDTO, param.getCompanyOrgIds(), param.getStoreOrgIds()));

            PageResponse<List<StoreReturnExecuteOrderDTO>> pageResponse = new PageResponse<>();
            pageResponse.setPage(param.getPage());
            pageResponse.setPageSize(param.getPageSize());
            pageResponse.setTotalSize(0L);
            pageResponse.setResult(new ArrayList<>());

            IscmStoreReturnExecuteOrderMainExample example = new IscmStoreReturnExecuteOrderMainExample();
            IscmStoreReturnExecuteOrderMainExample.Criteria criteria = example.createCriteria();
            criteria.andCreatedMonthEqualTo(param.getMonth());
            criteria.andGmtCreateBetween(param.getStartDate(), param.getEndDate());
            if (StringUtils.isNotBlank(param.getCreatedName())) {
                criteria.andCreatedNameEqualTo(param.getCreatedName().trim());
            }
            if (null != param.getPlatformOrgId()) {
                criteria.andPlatformOrgIdEqualTo(param.getPlatformOrgId());
            }
            if (CollectionUtils.isNotEmpty(param.getCompanyOrgIds())) {
                criteria.andCompanyOrgIdIn(param.getCompanyOrgIds());
            }
            if (CollectionUtils.isNotEmpty(param.getStoreOrgIds())) {
                criteria.andStoreOrgIdIn(param.getStoreOrgIds());
            }
            if (ComparisonOperator.GT.name().equals(param.getReturnGoodsQuantityTotalCq()) && null != param.getReturnGoodsQuantityTotal()) {
                criteria.andReturnGoodsQuantityTotalGreaterThan(param.getReturnGoodsQuantityTotal());
            }
            if (ComparisonOperator.LT.name().equals(param.getReturnGoodsQuantityTotalCq()) && null != param.getReturnGoodsQuantityTotal()) {
                criteria.andReturnGoodsQuantityTotalLessThan(param.getReturnGoodsQuantityTotal());
            }
            if (ComparisonOperator.GT.name().equals(param.getReturnCostAmountTotalCq()) && null != param.getReturnCostAmountTotal()) {
                criteria.andReturnCostAmountTotalGreaterThan(param.getReturnCostAmountTotal());
            }
            if (ComparisonOperator.LT.name().equals(param.getReturnCostAmountTotalCq()) && null != param.getReturnCostAmountTotal()) {
                criteria.andReturnCostAmountTotalLessThan(param.getReturnCostAmountTotal());
            }
            if (StringUtils.isNotBlank(param.getWarehouseCode())) {
                criteria.andWarehouseCodeEqualTo(param.getWarehouseCode().trim());
            }
            if (null != param.getReturnType()) {
                criteria.andReturnTypeEqualTo(param.getReturnType());
            }
            if (CollectionUtils.isNotEmpty(param.getProcessStatusList())) {
//                criteria.andProcessStatusIn(param.getProcessStatusList());
                if (param.getProcessStatusList().size() == 1) {
                    criteria.andProcessStatusAllLike("%|" + param.getProcessStatusList().get(0) + "|%");
                } else {
                    example.clear();
                    for (Byte processStatus : param.getProcessStatusList()) {
                        IscmStoreReturnExecuteOrderMainExample.Criteria criteria2 = example.or();
                        CriteriaUtils.copyCriteria(criteria, criteria2);
                        criteria2.andProcessStatusAllLike("%|" + processStatus + "|%");
                    }
                }
            }
            long count = iscmStoreReturnExecuteOrderMainMapper.countByExample(example);
            if (count <= 0L) {
                return pageResponse;
            }
            example.setLimit(param.getPageSize());
            example.setOffset(Long.valueOf(param.getPage() * param.getPageSize()));
            example.setOrderByClause("gmt_update desc,platform_org_id,company_org_id,store_org_id,warehouse_code");
            List<IscmStoreReturnExecuteOrderMain> orderList = iscmStoreReturnExecuteOrderMainMapper.selectByExample(example);

            // 汇总
            List<String> orderNoList = orderList.stream().map(IscmStoreReturnExecuteOrderMain::getReturnOrderNo).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(orderList)) {
                return pageResponse;
            }
            List<StoreReturnExecuteOrderSum> sumList = this.sumOrder(userDTO, orderNoList, param.getProcessStatusList(), param.getMonth());
            Map<String, StoreReturnExecuteOrderSum> sumMap = sumList.stream().collect(Collectors.toMap(StoreReturnExecuteOrderSum::getReturnOrderNo, Function.identity(), (k1, k2) -> k1));

            List<StoreReturnExecuteOrderDTO> orderDTOList = orderList.stream().map(v -> {
                // 设置缓存状态
                v.setProcessStatus(getSourceStatus(v.getProcessStatus(), v.getProcessStatusAll(), param.getProcessStatusList()));
                v.setProcessStatus(getCachedStatus(v));
                StoreReturnExecuteOrderDTO orderDTO = new StoreReturnExecuteOrderDTO();
                orderDTO.transferToDTO(v);
                orderDTO.setReturnTypeDesc(Optional.ofNullable(ReturnTypeEnum.getEnumByType(v.getReturnType())).orElse(ReturnTypeEnum.CALCULATE).getDesc());
                StoreReturnExecuteOrderSum sum = sumMap.get(v.getReturnOrderNo());
                if (sum != null) {
                    orderDTO.setReturnGoodsQuantityTotal(sum.getReturnGoodsQuantityTotal());
                    orderDTO.setReturnQuantityTotal(sum.getReturnQuantityTotal());
                    orderDTO.setReturnCostAmountTotal(sum.getReturnCostAmountTotal());
                    orderDTO.setIssueReturnQuantityTotal(sum.getIssueReturnQuantityTotal());
                    orderDTO.setIssueReturnAmountTotal(sum.getIssueReturnAmountTotal());
                }
                return orderDTO;
            }).collect(Collectors.toList());
            pageResponse.setTotalSize(count);
            pageResponse.setResult(orderDTOList);
            return pageResponse;
        } catch (BusinessErrorException e) {
            logger.warn("获取退仓执行列表失败", e);
            throw e;
        } catch (Exception e) {
            logger.error("获取退仓执行列表失败", e);
            throw e;
        }
    }

    @Override
    public StoreReturnExecuteOrderDetailDTO getExecuteDetail(TokenUserDTO userDTO, ReturnWarehouseExecuteIdParam param) throws Exception {
        try {
            checkMonth(param.getMonth());

            if (null == param.getId()) {
                throw new BusinessErrorException("Id参数不能为空");
            }
            if (null == param.getProcessStatus()) {
                throw new BusinessErrorException("退仓状态[processStatus]参数不能为空");
            }

            StoreReturnExecuteOrderDetailDTO detailDTO = new StoreReturnExecuteOrderDetailDTO();
            IscmStoreReturnExecuteOrderMainExample example = new IscmStoreReturnExecuteOrderMainExample();
            example.createCriteria().andCreatedMonthEqualTo(param.getMonth()).andIdEqualTo(param.getId());
            List<IscmStoreReturnExecuteOrderMain> orderList = iscmStoreReturnExecuteOrderMainMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(orderList)) {
                return detailDTO;
            }

            IscmStoreReturnExecuteOrderMain order = orderList.get(0);
            checkPerm(userDTO, null,  Lists.newArrayList(order.getStoreOrgId()));
            List<StoreReturnExecuteOrderSum> sumList = this.sumOrder(userDTO, Lists.newArrayList(order.getReturnOrderNo()), Lists.newArrayList(param.getProcessStatus()), param.getMonth());
            Map<String, StoreReturnExecuteOrderSum> sumMap = sumList.stream().collect(Collectors.toMap(StoreReturnExecuteOrderSum::getReturnOrderNo, Function.identity(), (k1, k2) -> k1));

            detailDTO.setOrderId(order.getId());
            detailDTO.setOrderNo(order.getReturnOrderNo());
            detailDTO.setPlatformOrgId(order.getPlatformOrgId());
            detailDTO.setPlatformOrgName(order.getPlatformOrgName());
            detailDTO.setWarehouseCode(order.getWarehouseCode());
            detailDTO.setWarehouseName(order.getWarehouseCode() + "-" + order.getWarehouseName());
            detailDTO.setStoreOrgId(order.getStoreOrgId());
            detailDTO.setStoreCode(order.getStoreCode());
            detailDTO.setStoreName(order.getStoreCode() + "-" + order.getStoreName());
            detailDTO.setReturnGoodsQuantityTotal(order.getReturnGoodsQuantityTotal());
            detailDTO.setReturnQuantityTotal(order.getReturnQuantityTotal());
            detailDTO.setReturnCostAmountTotal(order.getReturnCostAmountTotal());
            StoreReturnExecuteOrderSum sum = sumMap.get(order.getReturnOrderNo());
            if (null != sum) {
                detailDTO.setReturnGoodsQuantityTotal(sum.getReturnGoodsQuantityTotal());
                detailDTO.setReturnQuantityTotal(sum.getReturnQuantityTotal());
                detailDTO.setReturnCostAmountTotal(sum.getReturnCostAmountTotal());
            }
            detailDTO.setProcessStatus(param.getProcessStatus());
            detailDTO.setProcessStatusDesc(StoreReturnExecuteProcessStatusEnum.getNameByCode(param.getProcessStatus()));
//            detailDTO.setDetailList(pageExecuteDetailList(userDTO, new ReturnWarehouseExecuteDetailParam(param.getMonth(), order.getReturnOrderNo())).getResult());
            return detailDTO;
        } catch (BusinessErrorException e) {
            logger.warn("获取退仓执行明细抬头失败", e);
            throw e;
        } catch (Exception e) {
            logger.error("获取退仓执行明细抬头失败", e);
            throw e;
        }
    }

    @Override
    public PageResponse<List<StoreReturnExecuteOrderDetailListDTO>> pageExecuteDetailList(TokenUserDTO userDTO, ReturnWarehouseExecuteDetailParam param) {
        try {
            checkMonth(param.getMonth());

            if (StringUtils.isBlank(param.getOrderNo())) {
                throw new BusinessErrorException("退仓单号[orderNo]参数不能为空");
            }

            if (null == param.getProcessStatus()) {
                throw new BusinessErrorException("退仓状态[processStatus]参数不能为空");
            }

            PageResponse<List<StoreReturnExecuteOrderDetailListDTO>> pageResponse = new PageResponse<>();
            pageResponse.setPage(param.getPage());
            pageResponse.setPageSize(param.getPageSize());
            pageResponse.setTotalSize(0L);
            pageResponse.setResult(new ArrayList<>());

            IscmStoreReturnExecuteOrderMainExample orderExample = new IscmStoreReturnExecuteOrderMainExample();
            orderExample.createCriteria().andCreatedMonthEqualTo(param.getMonth()).andReturnOrderNoEqualTo(param.getOrderNo());
            List<IscmStoreReturnExecuteOrderMain> orderList = iscmStoreReturnExecuteOrderMainMapper.selectByExample(orderExample);
            if (CollectionUtils.isEmpty(orderList)) {
                return pageResponse;
            }

            IscmStoreReturnExecuteOrderMain order = orderList.get(0);
            order.setProcessStatus(param.getProcessStatus());// 当前主单状态设置为查询参数的状态

            IscmStoreReturnExecuteOrderDetailExample example = new IscmStoreReturnExecuteOrderDetailExample();
            IscmStoreReturnExecuteOrderDetailExample.Criteria criteria = example.createCriteria();
            criteria.andCreatedMonthEqualTo(param.getMonth());
            criteria.andReturnOrderNoEqualTo(param.getOrderNo());
            if (CollectionUtils.isNotEmpty(param.getGoodsNos())) {
                criteria.andGoodsNoIn(param.getGoodsNos().stream().map(String::trim).collect(Collectors.toList()));
            }
            if (null != param.getGoodsChannelAttr()) {
                criteria.andGoodsPurChannelEqualTo(GoodsChannelAttrEnum.getNameByCode(param.getGoodsChannelAttr()));
            }
            criteria.andProcessStatusEqualTo(param.getProcessStatus());
            long count = iscmStoreReturnExecuteOrderDetailMapper.countByExample(example);
            if (count <= 0L) {
                return pageResponse;
            }
            example.setLimit(param.getPageSize());
            example.setOffset(Long.valueOf(param.getPage() * param.getPageSize()));
            example.setOrderByClause("platform_org_id,warehouse_code,goods_no,batch_no");
            if (StringUtils.isNotBlank(param.getSortField()) && StringUtils.isNotBlank(param.getSortType())) {
                String sortField = FieldMapper.getFieldMapFrontendToBackend().get(param.getSortField());
                sortField = Optional.ofNullable(sortField).orElse(param.getSortField());
                example.setOrderByClause(CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, sortField) + " " + param.getSortType());
            }
            List<IscmStoreReturnExecuteOrderDetail> detailList = iscmStoreReturnExecuteOrderDetailMapper.selectByExample(example);

            Map<String, StoreReturnExecuteOrderQty> qtyMap = new HashMap<>();
            if (StoreReturnExecuteProcessStatusEnum.enableUpdateStock(param.getProcessStatus())) {
                List<String> orderNoList = detailList.stream().map(IscmStoreReturnExecuteOrderDetail::getReturnOrderNo).distinct().collect(Collectors.toList());
                List<StoreReturnExecuteOrderSum> sumList = sumOrder(null, orderNoList, Lists.newArrayList(param.getProcessStatus()), param.getMonth(), false);
                Map<String, StoreReturnExecuteOrderSum> sumMap = sumList.stream().collect(Collectors.toMap(StoreReturnExecuteOrderSum::getReturnOrderNo, Function.identity(), (k1, k2) -> k1));
                StoreReturnExecuteOrderSum sum = Optional.ofNullable(sumMap.get(order.getReturnOrderNo())).orElseGet(StoreReturnExecuteOrderSum::new);
                List<StoreReturnExecuteOrderQty> qtyList = Optional.ofNullable(sum.getOrderQtyList()).orElseGet(Collections::emptyList);
                qtyMap.putAll(qtyList.stream().collect(Collectors.toMap(this::genBatchNoKey, Function.identity(), (k1, k2) -> k2)));
            }

            Byte statusCache = getCachedStatus(order);
            List<StoreReturnExecuteOrderDetailListDTO> detailListDTOS = detailList.stream().map(v -> {
                // 设置缓存状态
                if (ObjectUtils.equals(param.getProcessStatus(), v.getProcessStatus())) v.setProcessStatus(statusCache);
                StoreReturnExecuteOrderDetailListDTO detailListDTO = new StoreReturnExecuteOrderDetailListDTO();
                detailListDTO.transferToDTO(v);
                // [未审批]和[下发失败]状态的单据设置实时库存
                if (StoreReturnExecuteProcessStatusEnum.enableUpdateStock(v.getProcessStatus())) {
                    StoreReturnExecuteOrderQty qty = qtyMap.get(genBatchNoKey(v));
                    if (null != qty) {
                        // 实时库存
                        detailListDTO.setBatchStock(qty.getBatchStock());
                        // 下发退仓数量 = min(实时批号库存,确定退仓数量)
                        detailListDTO.setIssueReturnQuantity(qty.getIssueReturnQuantity());
                        // 下发退仓金额 = 登记时成本金额/登记数量 * 下发退仓数量
                        detailListDTO.setIssueReturnAmount(qty.getIssueReturnAmount());
                    }
                }
                if (ReturnWarehouseBusinessTypeEnum.IMPORT_RETURN.getCode() == v.getReturnBusinessType().byteValue()) {
                    detailListDTO.setReturnSource("手工导入");
                } else if (ReturnWarehouseBusinessTypeEnum.DEFECTIVE_RETURN.getCode() == v.getReturnBusinessType().byteValue()) {
                    detailListDTO.setReturnSource("不良品处理-效期&定淘品");
                }else if (ReturnWarehouseBusinessTypeEnum.UNMANAGE_GOODS_RETURN.getCode() == v.getReturnBusinessType().byteValue()) {
                    detailListDTO.setReturnSource("不良品处理-不经营品");
                }else {
                    detailListDTO.setReturnSource("系统建议");
                }
                if (v.getReturnBusinessType().equals(ReturnWarehouseBusinessTypeEnum.DEFECTIVE_RETURN.getCode())) {
                    detailListDTO.setHandleQuantity(null == v.getHandleQuantity() ? "全部" : v.getHandleQuantity().toString());
                    DisposalOpinionEnum disposalOpinionEnum = DisposalOpinionEnum.getByCode(v.getDisposalOpinion());
                    if (disposalOpinionEnum != null) {
                        detailListDTO.setDisposalOpinionDesc(disposalOpinionEnum.getName());
                    }
                }
                if (v.getReturnBusinessType().equals(ReturnWarehouseBusinessTypeEnum.DEFECTIVE_RETURN.getCode())) {
                    detailListDTO.setAutoAuditLevel(2);
                    if (null == v.getHandleQuantity()) {
                        detailListDTO.setModifyLevel(1);
                    } else {
                        detailListDTO.setModifyLevel(2);
                    }
                }else if (v.getReturnBusinessType().equals(ReturnWarehouseBusinessTypeEnum.UNMANAGE_GOODS_RETURN.getCode())) {
                    detailListDTO.setAutoAuditLevel(2);
                    detailListDTO.setModifyLevel(1);
                } else if (v.getReturnBusinessType().equals(ReturnWarehouseBusinessTypeEnum.IMPORT_RETURN.getCode())) {
                    detailListDTO.setAutoAuditLevel(0);
                    detailListDTO.setModifyLevel(0);
                } else {
                    detailListDTO.setAutoAuditLevel(2);
                    detailListDTO.setModifyLevel(2);
                }
                detailListDTO.setReturnCostAmount(v.getCostAmount());
                detailListDTO.setIssueReturnAmount(v.getIssueReturnAmount());
                detailListDTO.setValidityDate(null == v.getValidityDate() ? "" : DateUtils.conventDateStrByDate(v.getValidityDate(), DateUtils.DATE_PATTERN));
                detailListDTO.setProduceDate(null == v.getProduceDate() ? "" : DateUtils.conventDateStrByDate(v.getProduceDate(), DateUtils.DATE_PATTERN));
                return detailListDTO;
            }).collect(Collectors.toList());
            pageResponse.setTotalSize(count);
            pageResponse.setResult(detailListDTOS);
            return pageResponse;
        } catch (BusinessErrorException e) {
            logger.warn("获取退仓执行明细列表失败", e);
            throw e;
        } catch (Exception e) {
            logger.error("获取退仓执行明细列表失败", e);
            throw e;
        }
    }

    @Override
    public PageResponse<List<StoreReturnExecuteOrderDetailListDTO>> pageExecuteDetailList(TokenUserDTO userDTO, ReturnWarehouseExecuteParam param) throws Exception {
        ReturnWarehouseExecuteParam paramKey = new ReturnWarehouseExecuteParam();
        // 查总单时set了分页参数, 所以这里先取出来
        Integer page = param.getPage();
        Integer pageSize = param.getPageSize();
        com.cowell.iscm.utils.BeanUtils.copyProperties(param, paramKey);
        paramKey.setPage(null);
        paramKey.setPageSize(null);
        Set<String> orderNos;
        RBucket<Set<String>> bucket = redissonClient.getBucket("iscm-returnWarehouse-execute-orderNos-" + userDTO.getUserId() + "-" + CommonUtil.md5(paramKey));
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(bucket.get())) {
            orderNos = bucket.get();
        } else {
            orderNos = this.getCheckedOrderNos(userDTO, param);
            bucket.set(orderNos, 1L, TimeUnit.HOURS);
        }
        if (CollectionUtils.isEmpty(orderNos)) {
            PageResponse<List<StoreReturnExecuteOrderDetailListDTO>> pageResponse = new PageResponse<>();
            pageResponse.setPage(param.getPage());
            pageResponse.setPageSize(param.getPageSize());
            pageResponse.setTotalSize(0L);
            pageResponse.setResult(new ArrayList<>());
            return pageResponse;
        }
        ReturnWarehouseExecuteIdBatchParam batchParam = new ReturnWarehouseExecuteIdBatchParam();
        batchParam.setMonth(param.getMonth());
        batchParam.setOrderNos(new ArrayList<>(orderNos));
        batchParam.setProcessStatusList(param.getProcessStatusList());
        return this.pageExecuteDetailList(userDTO, batchParam, new PaginationQuery(page, pageSize));
    }

    @Override
    public PageResponse<List<StoreReturnExecuteOrderDetailListDTO>> pageExecuteDetailList(TokenUserDTO userDTO, ReturnWarehouseExecuteIdBatchParam param, PaginationQuery paginationQuery) throws Exception {
        try {
            checkMonth(param.getMonth());

            if (CollectionUtils.isEmpty(param.getProcessStatusList())) {
                throw new BusinessErrorException("退仓状态[processStatusList]参数不能为空");
            }

            PageResponse<List<StoreReturnExecuteOrderDetailListDTO>> pageResponse = new PageResponse<>();
            pageResponse.setPage(paginationQuery.getPage());
            pageResponse.setPageSize(paginationQuery.getPageSize());
            pageResponse.setTotalSize(0L);
            pageResponse.setResult(new ArrayList<>());

            IscmStoreReturnExecuteOrderDetailExample example = new IscmStoreReturnExecuteOrderDetailExample();
            IscmStoreReturnExecuteOrderDetailExample.Criteria criteria = example.createCriteria();
            criteria.andCreatedMonthEqualTo(param.getMonth());
            criteria.andReturnOrderNoIn(param.getOrderNos());
            criteria.andProcessStatusIn(param.getProcessStatusList());
            if (CollectionUtils.isNotEmpty(param.getIds())) {
                criteria.andIdIn(param.getIds());
            }
            long count = iscmStoreReturnExecuteOrderDetailMapper.countByExample(example);
            if (count <= 0L) {
                return pageResponse;
            }
            example.setLimit(paginationQuery.getPageSize());
            example.setOffset(Long.valueOf(paginationQuery.getPage() * paginationQuery.getPageSize()));
//            example.setOrderByClause("platform_org_id,warehouse_code,goods_no,batch_no,id");
            List<IscmStoreReturnExecuteOrderDetail> detailList = iscmStoreReturnExecuteOrderDetailMapper.selectByExample(example);

            List<StoreReturnExecuteOrderDetailListDTO> detailListDTOS = detailList.stream().map(v -> {
                StoreReturnExecuteOrderDetailListDTO detailListDTO = new StoreReturnExecuteOrderDetailListDTO();
                detailListDTO.transferToDTO(v);
                if (ReturnWarehouseBusinessTypeEnum.IMPORT_RETURN.getCode() == v.getReturnBusinessType().byteValue()) {
                    detailListDTO.setReturnSource("手工导入");
                } else if (ReturnWarehouseBusinessTypeEnum.DEFECTIVE_RETURN.getCode() == v.getReturnBusinessType().byteValue()) {
                    detailListDTO.setReturnSource("不良品处理-效期&定淘品");
                }else if (ReturnWarehouseBusinessTypeEnum.UNMANAGE_GOODS_RETURN.getCode() == v.getReturnBusinessType().byteValue()) {
                    detailListDTO.setReturnSource("不良品处理-不经营品");
                }else {
                    detailListDTO.setReturnSource("系统建议");
                }
                if (v.getReturnBusinessType().equals(ReturnWarehouseBusinessTypeEnum.DEFECTIVE_RETURN.getCode())) {
                    detailListDTO.setHandleQuantity(null == v.getHandleQuantity() ? "全部" : v.getHandleQuantity().toString());
                    DisposalOpinionEnum disposalOpinionEnum = DisposalOpinionEnum.getByCode(v.getDisposalOpinion());
                    if (disposalOpinionEnum != null) {
                        detailListDTO.setDisposalOpinionDesc(disposalOpinionEnum.getName());
                    }
                }
                if (v.getReturnBusinessType().equals(ReturnWarehouseBusinessTypeEnum.DEFECTIVE_RETURN.getCode())) {
                    detailListDTO.setAutoAuditLevel(2);
                    if (null == v.getHandleQuantity()) {
                        detailListDTO.setModifyLevel(1);
                    } else {
                        detailListDTO.setModifyLevel(2);
                    }
                }else if (v.getReturnBusinessType().equals(ReturnWarehouseBusinessTypeEnum.UNMANAGE_GOODS_RETURN.getCode())) {
                    detailListDTO.setAutoAuditLevel(2);
                    detailListDTO.setModifyLevel(1);
                } else if (v.getReturnBusinessType().equals(ReturnWarehouseBusinessTypeEnum.IMPORT_RETURN.getCode())) {
                    detailListDTO.setAutoAuditLevel(0);
                    detailListDTO.setModifyLevel(0);
                } else {
                    detailListDTO.setAutoAuditLevel(2);
                    detailListDTO.setModifyLevel(2);
                }
                detailListDTO.setReturnCostAmount(v.getCostAmount());
                detailListDTO.setIssueReturnAmount(v.getIssueReturnAmount());
                detailListDTO.setValidityDate(null == v.getValidityDate() ? "" : DateUtils.conventDateStrByDate(v.getValidityDate(), DateUtils.DATE_PATTERN));
                detailListDTO.setProduceDate(null == v.getProduceDate() ? "" : DateUtils.conventDateStrByDate(v.getProduceDate(), DateUtils.DATE_PATTERN));
                return detailListDTO;
            }).collect(Collectors.toList());
            pageResponse.setTotalSize(count);
            pageResponse.setResult(detailListDTOS);
            return pageResponse;
        } catch (BusinessErrorException e) {
            logger.warn("批量获取退仓执行明细列表失败", e);
            throw e;
        } catch (Exception e) {
            logger.error("批量获取退仓执行明细列表失败", e);
            throw e;
        }
    }

    @Transactional(rollbackFor = {Exception.class})
    @Override
    public void deleteExecute(TokenUserDTO userDTO, ReturnWarehouseExecuteIdParam param) throws Exception {
        try {
            deleteExecute(userDTO, new ReturnWarehouseExecuteIdBatchParam(param.getMonth(), Lists.newArrayList(param.getId())));
        } catch (BusinessErrorException e) {
            logger.warn("删除退仓执行异常", e);
            throw e;
        } catch (Exception e) {
            logger.error("删除退仓执行异常", e);
            throw e;
        }
    }

    @Transactional(rollbackFor = {Exception.class})
    @Override
    public void deleteExecute(TokenUserDTO userDTO, ReturnWarehouseExecuteIdBatchParam param) throws Exception {
        try {
            checkMonth(param.getMonth());
            checkIdLen(param.getIds());
            param.setStoreOrgIds(checkPerm(userDTO, null, param.getStoreOrgIds()));

            IscmStoreReturnExecuteOrderMainExample example = new IscmStoreReturnExecuteOrderMainExample();
            example.createCriteria().andIdIn(param.getIds()).andStoreOrgIdIn(param.getStoreOrgIds()).andCreatedMonthEqualTo(param.getMonth());
            List<IscmStoreReturnExecuteOrderMain> orderList = iscmStoreReturnExecuteOrderMainMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(orderList)) {
                throw new BusinessErrorException("删除失败,没找到需要删除的记录");
            }

            for (IscmStoreReturnExecuteOrderMain order : orderList) {
                Byte processStatus = order.getProcessStatus();
                if (null != processStatus && !StoreReturnExecuteProcessStatusEnum.enableDel(processStatus)) {
                    throw new BusinessErrorException("所选单据中包含["+StoreReturnExecuteProcessStatusEnum.getNameByCode(processStatus)+"]状态的单据不能删除");
                }
            }

            Date now = new Date();
            Byte processStatus = StoreReturnExecuteProcessStatusEnum.DELETED.getCode();
            IscmStoreReturnExecuteOrderMain record = new IscmStoreReturnExecuteOrderMain();
            record.setProcessStatus(processStatus);
            record.setUpdatedBy(userDTO.getUserId());
            record.setUpdatedName(userDTO.getName());
            record.setGmtUpdate(now);
            iscmStoreReturnExecuteOrderMainMapper.updateByExampleSelective(record, example);

            // 更新明细单
            List<StoreReturnExecuteOrderUpdateDTO> orderUpdateDTOS = orderList.stream().map(v -> {
                StoreReturnExecuteOrderUpdateDTO orderUpdateDTO = new StoreReturnExecuteOrderUpdateDTO();
                orderUpdateDTO.setMonth(param.getMonth());
                orderUpdateDTO.setOrderNo(v.getReturnOrderNo());
                orderUpdateDTO.setProcessStatus(processStatus);
                orderUpdateDTO.setSourceStatus(getSourceStatus(v.getProcessStatus(), v.getProcessStatusAll(), param.getProcessStatusList()));
                orderUpdateDTO.setUpdatedBy(record.getUpdatedBy());
                orderUpdateDTO.setUpdatedName(record.getUpdatedName());
                orderUpdateDTO.setGmtUpdate(record.getGmtUpdate());
                return orderUpdateDTO;
            }).collect(Collectors.toList());
            returnWarehouseExecuteUpdateService.updateOrderDetail(orderUpdateDTOS);
        } catch (BusinessErrorException e) {
            logger.warn("删除退仓执行异常", e);
            throw e;
        } catch (Exception e) {
            logger.error("删除退仓执行异常", e);
            throw e;
        }
    }

    @Transactional(rollbackFor = {Exception.class})
    @Override
    public void deleteExecuteDetail(TokenUserDTO userDTO, ReturnWarehouseExecuteIdParam param) throws Exception {
        try {
            deleteExecuteDetail(userDTO, new ReturnWarehouseExecuteIdBatchParam(param.getMonth(), Lists.newArrayList(param.getId())));
        } catch (BusinessErrorException e) {
            logger.warn("删除退仓执行明细异常", e);
            throw e;
        } catch (Exception e) {
            logger.error("删除退仓执行明细异常", e);
            throw e;
        }
    }

    @Transactional(rollbackFor = {Exception.class})
    @Override
    public void deleteExecuteDetail(TokenUserDTO userDTO, ReturnWarehouseExecuteIdBatchParam param) throws Exception {
        try {
            checkMonth(param.getMonth());
            checkIdLen(param.getIds());
            param.setStoreOrgIds(checkPerm(userDTO, null, param.getStoreOrgIds()));

            IscmStoreReturnExecuteOrderDetailExample example = new IscmStoreReturnExecuteOrderDetailExample();
            example.createCriteria().andIdIn(param.getIds()).andStoreOrgIdIn(param.getStoreOrgIds()).andCreatedMonthEqualTo(param.getMonth());
            List<IscmStoreReturnExecuteOrderDetail> orderDetails = iscmStoreReturnExecuteOrderDetailMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(orderDetails)) {
                throw new BusinessErrorException("删除失败,没找到需要删除的记录");
            }
            Set<Byte> processStatusSet = orderDetails.stream().map(IscmStoreReturnExecuteOrderDetail::getProcessStatus).collect(Collectors.toSet());
            for (Byte processStatus : processStatusSet) {
                if (null != processStatus && !StoreReturnExecuteProcessStatusEnum.enableDel(processStatus)) {
                    throw new BusinessErrorException("["+StoreReturnExecuteProcessStatusEnum.getNameByCode(processStatus)+"]状态的单据不能删除");
                }
            }

            Date now = new Date();
            Byte processStatus = StoreReturnExecuteProcessStatusEnum.DELETED.getCode();
            IscmStoreReturnExecuteOrderDetail record = new IscmStoreReturnExecuteOrderDetail();
            record.setProcessStatus(processStatus);
            record.setUpdatedBy(userDTO.getUserId());
            record.setUpdatedName(userDTO.getName());
            record.setGmtUpdate(now);
            iscmStoreReturnExecuteOrderDetailMapper.updateByExampleSelective(record, example);

            // 更新主单
            StoreReturnExecuteOrderUpdateDTO orderUpdateDTO = new StoreReturnExecuteOrderUpdateDTO();
            orderUpdateDTO.setMonth(param.getMonth());
            orderUpdateDTO.setOrderNo(orderDetails.get(0).getReturnOrderNo());
            orderUpdateDTO.setProcessStatus(processStatus);
            orderUpdateDTO.setUpdatedBy(record.getUpdatedBy());
            orderUpdateDTO.setUpdatedName(record.getUpdatedName());
            orderUpdateDTO.setGmtUpdate(record.getGmtUpdate());
            returnWarehouseExecuteUpdateService.updateOrderMain(orderUpdateDTO);

            // 汇总
            this.sumOrder(userDTO, Lists.newArrayList(orderDetails.get(0).getReturnOrderNo()), param.getProcessStatusList(), param.getMonth(), false);

            // 更新退仓状态
            returnWarehouseExecuteUpdateService.updateReturnWarehouseStatus(orderDetails, RegisterReturnWarehouseTypeEnum.NO_RETURN_WAREHOUSE);
        } catch (BusinessErrorException e) {
            logger.warn("删除退仓执行明细异常", e);
            throw e;
        } catch (Exception e) {
            logger.error("删除退仓执行明细异常", e);
            throw e;
        }
    }

    @Override
    public void exportExecuteList(TokenUserDTO userDTO, ReturnWarehouseExecuteParam param) throws Exception {
        try {
            checkMonth(param.getMonth());
            param.setStoreOrgIds(checkPerm(userDTO, param.getCompanyOrgIds(), param.getStoreOrgIds()));

            String fileName = DateUtils.conventDateStrByDate(new Date(), DateUtils.DATETIME_PATTERN_COMPACT) + ".xls";
            asyncExportFileService.asyncExportToCos(fileName, AsyncExportActionEnum.STORE_RETURN_WAREHOUSE_EXECUTE_LIST, userDTO, new HandlerDataExportService<StoreReturnExecuteOrderDTO>() {
                @Override
                public List<StoreReturnExecuteOrderDTO> getDataToExport() {
                    return null;
                }

                @Override
                public List<StoreReturnExecuteOrderDTO> getDataToExport(Integer page, Integer pageSize) {
                    param.setPage(page);
                    param.setPageSize(pageSize);
                    try {
                        return pageExecuteList(userDTO, param).getResult();
                    } catch (Exception e) {
                        logger.error("export|获取退仓执行列表异常: {}-{}条", page*pageSize, (page+1)*pageSize, e);
                    }
                    return new ArrayList<>();
                }

                @Override
                public boolean isPageable() {
                    return true;
                }

                @Override
                public LinkedHashMap<String, String> getFieldMap() {
                    return StoreReturnExecuteOrderDTO.getFieldMap(param.getProcessStatusList());
                }
            });
        } catch (BusinessErrorException e) {
            logger.warn("导出退仓执行列表异常", e);
            throw e;
        } catch (Exception e) {
            logger.error("导出退仓执行列表异常", e);
            throw e;
        }
    }

    @Async
    @Override
    public void issue(TokenUserDTO userDTO, ReturnWarehouseExecuteIssueParam param) {
        List<String> pushToOaOrderNoList = new ArrayList<>();
        List<String> pushToPosOrderNoList = new ArrayList<>();
        Lists.partition(param.getIds(), Constants.MYSQL_CRUD_ONCE_MAX_SIZE).forEach(subIds -> {
            try {
                IscmStoreReturnExecuteOrderMainExample example = new IscmStoreReturnExecuteOrderMainExample();
                example.createCriteria().andIdIn(subIds).andStoreOrgIdIn(param.getStoreOrgIds()).andCreatedMonthEqualTo(param.getMonth());
                List<IscmStoreReturnExecuteOrderMain> orderList = iscmStoreReturnExecuteOrderMainMapper.selectByExample(example);
                if (CollectionUtils.isEmpty(orderList)) {
                    return;
                }
                // 过滤掉不是[未审批]状态的
                orderList.removeIf(v -> ObjectUtils.notEqual(v.getProcessStatus(), StoreReturnExecuteProcessStatusEnum.NON_APPROVED.getCode()));
                if (CollectionUtils.isEmpty(orderList)) {
                    return;
                }
                // 商品渠道属性=全国统采&采购组织=1000 提交OA,否则下发POS
                List<String> pushToOaOrderNoSubList = orderList.stream().filter(this::orderToOA).map(IscmStoreReturnExecuteOrderMain::getReturnOrderNo).distinct().collect(Collectors.toList());
                List<String> pushToPosOrderNoSubList = orderList.stream().filter(v -> !orderToOA(v)).map(IscmStoreReturnExecuteOrderMain::getReturnOrderNo).distinct().collect(Collectors.toList());
                List<String> orderNoList = orderList.stream().map(IscmStoreReturnExecuteOrderMain::getReturnOrderNo).distinct().collect(Collectors.toList());
                try {
                    sumOrder(userDTO, orderNoList, Lists.newArrayList(StoreReturnExecuteProcessStatusEnum.NON_APPROVED.getCode()), param.getMonth(), false);
                } catch (Exception e) {
                    logger.error("issue|汇总退仓单异常", e);
                    return;
                }
                if (CollectionUtils.isNotEmpty(pushToOaOrderNoSubList)) {
                    try {
                        updateOrder(userDTO, pushToOaOrderNoSubList, param.getMonth(), StoreReturnExecuteProcessStatusEnum.IN_APPROVAL.getCode(), StoreReturnExecuteProcessStatusEnum.NON_APPROVED.getCode(), param.getValidityDays());
                        pushToOaOrderNoList.addAll(pushToOaOrderNoSubList);
                    } catch (Exception e) {
                        logger.error("issue|更新[审批中]状态异常", e);
                    }
                }
                if (CollectionUtils.isNotEmpty(pushToPosOrderNoSubList)) {
                    try {
                        updateOrder(userDTO, pushToPosOrderNoSubList, param.getMonth(), StoreReturnExecuteProcessStatusEnum.ISSUING.getCode(), StoreReturnExecuteProcessStatusEnum.NON_APPROVED.getCode(), param.getValidityDays());
                        pushToPosOrderNoList.addAll(pushToPosOrderNoSubList);
                    } catch (Exception e) {
                        logger.error("issue|更新[下发中]状态异常", e);
                    }
                }
            } finally {
                subIds.forEach(id -> issuingIdCache.remove(param.getMonth() + "-" + id));
            }
        });
        // 下发
        if (enableToOA) {
            pushToOA(userDTO, pushToOaOrderNoList, param.getMonth());
        }
        pushToPOS(pushToPosOrderNoList, param.getMonth());
    }

    @Async
    @Override
    public void reIssue(TokenUserDTO userDTO, ReturnWarehouseExecuteIssueParam param) {
        List<String> pushToPosOrderNoList = new ArrayList<>();
        Lists.partition(param.getIds(), Constants.MYSQL_CRUD_ONCE_MAX_SIZE).forEach(subIds -> {
            try {
                IscmStoreReturnExecuteOrderMainExample example = new IscmStoreReturnExecuteOrderMainExample();
                example.createCriteria().andIdIn(subIds).andStoreOrgIdIn(param.getStoreOrgIds()).andCreatedMonthEqualTo(param.getMonth());
                List<IscmStoreReturnExecuteOrderMain> orderList = iscmStoreReturnExecuteOrderMainMapper.selectByExample(example);
                if (CollectionUtils.isEmpty(orderList)) {
                    return;
                }
                // 过滤掉不是[下发失败]状态的
                orderList.removeIf(v -> ObjectUtils.notEqual(v.getProcessStatus(), StoreReturnExecuteProcessStatusEnum.ISSUE_FAILED.getCode()));

                List<String> orderNoList = orderList.stream().map(IscmStoreReturnExecuteOrderMain::getReturnOrderNo).distinct().collect(Collectors.toList());
                try {
                    sumOrder(userDTO, orderNoList, Lists.newArrayList(StoreReturnExecuteProcessStatusEnum.ISSUE_FAILED.getCode()), param.getMonth(), false);
                } catch (Exception e) {
                    logger.error("reIssue|汇总退仓单异常", e);
                    return;
                }
                try {
                    updateOrder(userDTO, orderNoList, param.getMonth(), StoreReturnExecuteProcessStatusEnum.ISSUING.getCode(), StoreReturnExecuteProcessStatusEnum.ISSUE_FAILED.getCode(), param.getValidityDays());
                } catch (Exception e) {
                    logger.error("reIssue|更新[下发中]状态异常", e);
                    return;
                }
                pushToPosOrderNoList.addAll(orderNoList);
            } finally {
                subIds.forEach(id -> issuingIdCache.remove(param.getMonth() + "-" + id));
            }
        });
        // 下发
        pushToPOS(pushToPosOrderNoList, param.getMonth());
    }

    @Async
    @Override
    public void issueReceiveOA(ReturnWarehouseExecuteIssueFromOaDTO param) {
        try {
            returnWarehouseExecuteUpdateService.updateByOA(param.getDetails());
        } catch (Exception e) {
            logger.error("issueReceiveOA|异常", e);
        }
        // 下发海典POS
        param.getDetails().forEach(v -> {
            if (ObjectUtils.equals(v.getProcessStatus(), StoreReturnExecuteProcessStatusEnum.ISSUING.getCode())) {
                pushToPOS(Lists.newArrayList(v.getOrderNo()), v.getMonth());
            }
        });
    }

    @Async
    @Override
    public void issueReceivePOS(List<ReturnWarehouseExecuteIssueFromPosDTO> fromPosDTOList) {
        try {
            returnWarehouseExecuteUpdateService.updateByPOS(fromPosDTOList);
        } catch (Exception e) {
            logger.error("issueReceivePOS|异常", e);
        }
    }

    @Override
    public void checkReceiveOA(ReturnWarehouseExecuteIssueFromOaDTO param) throws Exception {
        List<ReturnWarehouseExecuteIssueFromOaDetailDTO> fromOaDetailDTOList = param.getDetails();
        if (CollectionUtils.isEmpty(fromOaDetailDTOList)) {
            throw new BusinessErrorException("checkReceivePOS|单据明细不能为空");
        }
        fromOaDetailDTOList.forEach(v -> {
            String orderNo = v.getOrderNo();
            int month = checkReceivedOrderNo(orderNo);
            StoreReturnExecuteProcessStatusEnum processStatus = null;
            if (ObjectUtils.equals(param.getAuditStatus(), 2)) {// 流程创建失败
                processStatus = StoreReturnExecuteProcessStatusEnum.NON_APPROVED;
            }
            if (ObjectUtils.equals(param.getAuditStatus(), 3) && ObjectUtils.equals(v.getAuditResult(), 0)) {// 通过
                processStatus = StoreReturnExecuteProcessStatusEnum.ISSUING;
            }
            if (ObjectUtils.equals(param.getAuditStatus(), 4) || ObjectUtils.equals(v.getAuditResult(), 1)) {// 拒绝
                processStatus = StoreReturnExecuteProcessStatusEnum.APPROVAL_REFUSED;
            }
            if (null == processStatus) {
                throw new BusinessErrorException("checkReceiveOA|未收到审核结果,单号=" + orderNo);
            }
            v.setMonth(month);
            v.setProcessStatus(processStatus.getCode());
        });
    }

    @Override
    public void checkReceivePOS(List<ReturnWarehouseExecuteIssueFromPosDTO> fromPosDTOList) {
        if (CollectionUtils.isEmpty(fromPosDTOList)) {
            throw new BusinessErrorException("checkReceivePOS|门店退仓结果不能为空");
        }
        fromPosDTOList.forEach(v -> {
            String orderNo = v.getOrderNo();
            int month = checkReceivedOrderNo(orderNo);
            if (StringUtils.isBlank(v.getRowNo())) {
                throw new BusinessErrorException("checkReceivePOS|ISCM退仓单行号不能为空");
            }
            if (null == v.getRealReturnQuantity() || v.getRealReturnQuantity().compareTo(BigDecimal.ZERO) < 0) {
                throw new BusinessErrorException("checkReceivePOS|实际退仓数量不能为空,并且不能小于0,orderNo="+orderNo+"rowNo="+v.getRowNo());
            }
            v.setMonth(month);
            v.setProcessStatus(StoreReturnExecuteProcessStatusEnum.ISSUE_COMPLETED.getCode());
        });
    }

    @Transactional(rollbackFor = {Exception.class})
    @Override
    public void autoVoid() {
        IscmParamOrgManagementExample example = new IscmParamOrgManagementExample();
        example.createCriteria()
                .andParamLevelEqualTo(OrgTypeEnum.ORG_TYPE_WAREHOUSE.getCode())
                .andOrgParamStatusEqualTo(ParamConfigStatusEnum.OPEN.getCode())
                .andParamCodeEqualTo("C001014")
                .andStatusEqualTo(Constants.NORMAL_STATUS);
        List<IscmParamOrgManagement> paramList = iscmParamOrgManagementMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(paramList)) {
            logger.info("autoVoid|未找到仓库级别参数C001014的参数值");
            return;
        }
        Date now = new Date();
        // 根据仓库编码分组
        Map<String, List<IscmParamOrgManagement>> paramMap = paramList.stream().collect(Collectors.groupingBy(IscmParamOrgManagement::getSapCode));
        paramMap.forEach((k, v) -> {
            Date minDate;
            Date autoVoidDate;
            int autoVoidDays;
            Set<Integer> months;
            try {
                Optional<IscmParamOrgManagement> optional = v.stream().max(Comparator.comparing(IscmParamOrgManagement::getGmtUpdate));
                autoVoidDays = Integer.parseInt(optional.orElseGet(IscmParamOrgManagement::new).getParamValue());
                autoVoidDate = DateUtils.addDays(now, -autoVoidDays);
                minDate = DateUtils.addDays(now, -(autoVoidDays+1));
                months = DateUtils.getRangeMonths(minDate, autoVoidDate);
                logger.info("autoVoidDays:{},autoVoidDate:{},minDate:{},months:{}", autoVoidDays, DateUtils.conventDateStrByDate(autoVoidDate, DateUtils.DATE_PATTERN), DateUtils.conventDateStrByDate(minDate, DateUtils.DATE_PATTERN), months);
            } catch (NumberFormatException e) {
                logger.info("autoVoid|C001014自动作废天数参数值解析异常,仓库编码={}", k);
                return;
            }
            try {
                months.forEach(month -> {
                    logger.info("autoVoid|month={}", month);
                    IscmStoreReturnExecuteOrderMainExample orderExample = new IscmStoreReturnExecuteOrderMainExample();
                    orderExample.createCriteria()
                            .andCreatedMonthEqualTo(month)
                            .andWarehouseCodeEqualTo(k)
                            .andGmtCreateBetween(DateUtils.getStartOfDay(minDate), DateUtils.getEndOfDay(autoVoidDate))
//                            .andGmtCreateLessThan(DateUtils.getEndOfDay(now))
                            .andProcessStatusEqualTo(StoreReturnExecuteProcessStatusEnum.NON_APPROVED.getCode());
                    IscmStoreReturnExecuteOrderMain orderRecord = new IscmStoreReturnExecuteOrderMain();
                    orderRecord.setProcessStatus(StoreReturnExecuteProcessStatusEnum.DELETED.getCode());
                    orderRecord.setGmtUpdate(now);
                    orderRecord.setUpdatedName(Constants.DEFAULT_CREATE_NAME);
                    int count = iscmStoreReturnExecuteOrderMainMapper.updateByExampleSelective(orderRecord, orderExample);
                    logger.info("autoVoid|自动作废成功单据数量={},仓库编码={}", count, k);

                    IscmStoreReturnExecuteOrderDetailExample orderDetailExample = new IscmStoreReturnExecuteOrderDetailExample();
                    orderDetailExample.createCriteria()
                            .andCreatedMonthEqualTo(month)
                            .andWarehouseCodeEqualTo(k)
                            .andGmtCreateBetween(DateUtils.getStartOfDay(minDate), DateUtils.getEndOfDay(autoVoidDate))
//                            .andGmtCreateLessThan(DateUtils.getEndOfDay(now))
                            .andProcessStatusEqualTo(StoreReturnExecuteProcessStatusEnum.NON_APPROVED.getCode());
                    List<IscmStoreReturnExecuteOrderDetail> details = iscmStoreReturnExecuteOrderDetailMapper.selectByExample(orderDetailExample);
                    if (CollectionUtils.isNotEmpty(details)) {
                        IscmStoreReturnExecuteOrderDetail orderDetailRecord = new IscmStoreReturnExecuteOrderDetail();
                        orderDetailRecord.setProcessStatus(StoreReturnExecuteProcessStatusEnum.DELETED.getCode());
                        orderDetailRecord.setGmtUpdate(now);
                        orderDetailRecord.setUpdatedName(Constants.DEFAULT_CREATE_NAME);
                        count = iscmStoreReturnExecuteOrderDetailMapper.updateByExampleSelective(orderDetailRecord, orderDetailExample);
                        logger.info("autoVoid|自动作废成功单据明细数量={},仓库编码={}", count, k);

                        // 更新 process_status_all
                        Set<String> orderNos = details.stream().map(IscmStoreReturnExecuteOrderDetail::getReturnOrderNo).collect(Collectors.toSet());
                        returnWarehouseExecuteUpdateService.updateProcessStatusAll(month, new ArrayList<>(orderNos));

                        // 更新退仓状态
                        returnWarehouseExecuteUpdateService.updateReturnWarehouseStatus(details, RegisterReturnWarehouseTypeEnum.NO_RETURN_WAREHOUSE);
                    } else {
                        logger.info("autoVoid|自动作废成功单据明细数量={},仓库编码={}", 0, k);
                    }
                });
            } catch (Exception e) {
                logger.error("autoVoid|自动作废发生异常,仓库编码={}", k, e);
                throw e;
            }
        });
    }

    /**
     * 退仓单汇总(直接汇总明细数据)
     *
     * @param userDTO
     * @param orderNoList
     * @param processStatusList
     * @param month
     * @return
     */
    @Override
    public List<StoreReturnExecuteOrderSum> sumOrder(TokenUserDTO userDTO, List<String> orderNoList, List<Byte> processStatusList, Integer month) {
        List<StoreReturnExecuteOrderSum> sumList = new ArrayList<>();
        int limit = Constants.MYSQL_CRUD_ONCE_MAX_SIZE;
        for (int i = 0; ; i++) {
            int offset = i * limit;
            List<StoreReturnExecuteOrderSum> subSumList = iscmStoreReturnExecuteOrderDetailExtendMapper.selectSumGroupByOrderNo(orderNoList, processStatusList, month, offset, limit);
            if (CollectionUtils.isEmpty(subSumList)) {
                break;
            }
            sumList.addAll(subSumList);
        }
        return sumList;
    }

    /**
     * 退仓单汇总(包括汇总实时库存)
     *
     * @param orderNoList
     * @param processStatusList
     * @param month
     * @param asyncUpdate       是否异步更新实时库存以及汇总(null|true|false)
     * @return
     */
    @Override
    public List<StoreReturnExecuteOrderSum> sumOrder(TokenUserDTO userDTO, List<String> orderNoList, List<Byte> processStatusList, Integer month, Boolean asyncUpdate) {
        if (CollectionUtils.isEmpty(orderNoList) || CollectionUtils.isEmpty(processStatusList)) {
            return new ArrayList<>();
        }

        if (!StoreReturnExecuteProcessStatusEnum.enableUpdateStock(processStatusList)) {
            logger.info("sumOrder|状态{}无需进行汇总", processStatusList);
            return new ArrayList<>();
        }

        List<StoreReturnExecuteOrderSum> sumList = new ArrayList<>();
        Map<String, BigDecimal> stockMap = new HashMap<>();

        List<StoreReturnExecuteOrderQty> qtyList = new ArrayList<>();
        int limit = Constants.MYSQL_CRUD_ONCE_MAX_SIZE;
        for (int i = 0; ; i++) {
            int offset = i * limit;
            List<StoreReturnExecuteOrderQty> subQtyList = iscmStoreReturnExecuteOrderDetailExtendMapper.selectQtyGroupByBatchNo(orderNoList, processStatusList, month, offset, limit);
            if (CollectionUtils.isEmpty(subQtyList)) {
                break;
            }
            qtyList.addAll(subQtyList);
        }
        logger.info("sumOrder|qtyList.size={}", qtyList.size());
        if (CollectionUtils.isEmpty(qtyList)) {
            return new ArrayList<>();
        }
        // 按单号分组
        Map<String, List<StoreReturnExecuteOrderQty>> orderQtyMap = qtyList.stream().collect(Collectors.groupingBy(StoreReturnExecuteOrderQty::getReturnOrderNo));
        // 按连锁+门店分组
        Map<String, List<StoreReturnExecuteOrderQty>> storeQtyMap = qtyList.stream().collect(Collectors.groupingBy(v -> v.getCompanyOrgId() + "-" + v.getStoreOrgId() + "-" + v.getStoreCode()));
        if (StoreReturnExecuteProcessStatusEnum.enableUpdateStock(processStatusList)) {
            if (esAble) {
                List<Long> companyOrgIdList = qtyList.stream().map(StoreReturnExecuteOrderQty::getCompanyOrgId).distinct().collect(Collectors.toList());
                List<Long> storeOrgIdList = qtyList.stream().map(StoreReturnExecuteOrderQty::getStoreOrgId).distinct().collect(Collectors.toList());
                List<String> goodsNoList = qtyList.stream().map(StoreReturnExecuteOrderQty::getGoodsNo).distinct().collect(Collectors.toList());
                stockMap.putAll(getBatchStockEs(companyOrgIdList, storeOrgIdList, goodsNoList));
            } else {
                storeQtyMap.forEach((k, v) -> {
                    String[] ks = k.split("-");
                    stockMap.putAll(getBatchStock(Long.valueOf(ks[0]), Long.valueOf(ks[1]), v.stream().map(StoreReturnExecuteOrderQty::getGoodsNo).distinct().collect(Collectors.toList())));
                });
            }
        }
        logger.info("sumOrder|stockMap.size={}", stockMap.size());
        stockMap.forEach((k, v) -> {
            logger.info("sumOrder|stock -> batch={}, qty={}", k, v);
        });
        orderQtyMap.forEach((k, orderQtyList) -> {
            StoreReturnExecuteOrderSum orderSum = new StoreReturnExecuteOrderSum(k);
            if (CollectionUtils.isNotEmpty(orderQtyList)) {
                for (StoreReturnExecuteOrderQty orderQty : orderQtyList) {
                    BigDecimal stock = stockMap.get(genBatchNoKey(orderQty));
                    if (null != stock) {
                        BigDecimal qty = genIssueReturnQuantity(orderQty.getReturnQuantity(), stock);
                        BigDecimal amount = genIssueReturnAmount(orderQty.getCostAmount(), orderQty.getRegisterQuantity(), qty);
                        orderQty.setBatchStock(stock);
                        orderQty.setIssueReturnQuantity(qty);
                        orderQty.setIssueReturnAmount(amount);
                    }
                }
            }
            orderSum.sum(orderQtyList, StoreReturnExecuteProcessStatusEnum.isIssue(processStatusList));
            orderSum.setOrderQtyList(Optional.ofNullable(orderQtyList).orElseGet(Collections::emptyList));

            sumList.add(orderSum);

        });
        if (BooleanUtils.isTrue(asyncUpdate)) {
            applicationContext.getBean(this.getClass()).asyncUpdateSummedQty(userDTO, month, sumList);
        }
        if (BooleanUtils.isFalse(asyncUpdate)) {
            returnWarehouseExecuteUpdateService.updateSummedQty(userDTO, month, sumList);
        }
        return sumList;
    }

    @Async
    void asyncUpdateSummedQty(TokenUserDTO userDTO, Integer month, List<StoreReturnExecuteOrderSum> sumList) {
        returnWarehouseExecuteUpdateService.updateSummedQty(userDTO, month, sumList);
    }

    @Async
    void pushToOA(TokenUserDTO userDTO, List<String> orderNoList, int month) {
        List<EmployeeInfoVO> employeeInfoVOS = new ArrayList<>();
        try {
            employeeInfoVOS.addAll(permissionService.getEmployeeInfoByUserIdList(Lists.newArrayList(userDTO.getUserId())));
        } catch (Exception e) {
            logger.error("根据用户Id获取员工信息异常,userId={}", userDTO.getUserId(), e);
        }
        Date now = new Date();
        String nowStr1 = DateUtils.conventDateStrByPattern(now, "yyyy-MM-dd");
        String nowStr2 = DateUtils.conventDateStrByPattern(now, "yyMMdd");
        List<ReturnWarehouseExecuteIssueToOaDetailDTO> toOaDetailDTOList = new ArrayList<>();
        Lists.partition(orderNoList, Constants.MYSQL_CRUD_ONCE_MAX_SIZE).forEach(subOrderNoList -> {
            IscmStoreReturnExecuteOrderMainExample example = new IscmStoreReturnExecuteOrderMainExample();
            example.createCriteria()
                    .andReturnOrderNoIn(subOrderNoList)
                    .andProcessStatusEqualTo(StoreReturnExecuteProcessStatusEnum.IN_APPROVAL.getCode())
                    .andCreatedMonthEqualTo(month);
            List<IscmStoreReturnExecuteOrderMain> orderList = iscmStoreReturnExecuteOrderMainMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(orderList)) {
                return;
            }

            List<ReturnWarehouseExecuteIssueToOaDetailDTO> subToOaDetailDTOList = orderList.stream().map(v -> {
                ReturnWarehouseExecuteIssueToOaDetailDTO toOaDetailDTO = new ReturnWarehouseExecuteIssueToOaDetailDTO();
                BeanUtils.copyProperties(v, toOaDetailDTO);
                toOaDetailDTO.setOrderNo(v.getReturnOrderNo());
                return toOaDetailDTO;
            }).collect(Collectors.toList());
            toOaDetailDTOList.addAll(subToOaDetailDTOList);
        });
        // 按仓库分组
        Map<String, List<ReturnWarehouseExecuteIssueToOaDetailDTO>> toOaDetailDTOMap = toOaDetailDTOList.stream().collect(Collectors.groupingBy(v -> v.getWarehouseCode() + "-" + v.getWarehouseName()));
        toOaDetailDTOMap.forEach((k ,v) -> Lists.partition(v, Constants.PUSH_TO_MB_ONCE_MAX_SIZE).forEach(subList -> {
            String[] ks = k.split("-");
            RBucket<String> bucket = redissonClient.getBucket(ISCM_PUSH_TO_OA_CODE_CACHE_KEY + ks[0] + nowStr2);
            if (null == bucket.get()) {
                bucket.set("001", 1L, TimeUnit.DAYS);
            } else {
                bucket.set(String.format("%03d", Integer.parseInt(bucket.get()) + 1), 1L, TimeUnit.DAYS);
            }
            List<String> subOrderNoList = subList.stream().map(ReturnWarehouseExecuteIssueToOaDetailDTO::getOrderNo).collect(Collectors.toList());
            String purchaseOrgCode = subList.get(0).getPurchaseOrgCode();
            String goodsPurChannel = subList.get(0).getGoodsPurChannel();
            ReturnWarehouseExecuteIssueToOaDTO toOaDTO = new ReturnWarehouseExecuteIssueToOaDTO();
            toOaDTO.setCode(ks[0] + nowStr2 + bucket.get());
            toOaDTO.setApplyDate(nowStr1);
            toOaDTO.setApplyUser(CollectionUtils.isNotEmpty(employeeInfoVOS) ? employeeInfoVOS.get(0).getEmpCode() : userDTO.getName());
            toOaDTO.setWarehouseName(ks[0] + ks[1]);
            toOaDTO.setPurchaseOrgName(purchaseOrgCode + GoodsPurchaseOrgEnum.getNameByCode(purchaseOrgCode));
            toOaDTO.setGoodsPurChannel(goodsPurChannel);
            toOaDTO.setDetails(subList);
            try {
                returnWarehouseExecutePushService.pushToOA(userDTO, toOaDTO);
                logger.info("提审成功,toOaDTO={}", toOaDTO);
            } catch (Exception e) {
                logger.error("提审失败,toOaDTO={}", toOaDTO, e);
                try {
                    updateOrder(userDTO, subOrderNoList, month, StoreReturnExecuteProcessStatusEnum.NON_APPROVED.getCode(), StoreReturnExecuteProcessStatusEnum.IN_APPROVAL.getCode(), null);
                } catch (Exception e1) {
                    logger.error("提审失败更新状态异常,涉及单号[{}]", subOrderNoList, e1);
                }
            }
        }));
    }

    @Async
    void pushToPOS(List<String> orderNoList, int month) {
        List<ReturnWarehouseExecuteIssueToPosDTO> toPosDTOList = new ArrayList<>();
        Lists.partition(orderNoList, Constants.MYSQL_CRUD_ONCE_MAX_SIZE).forEach(subOrderNoList -> {
            IscmStoreReturnExecuteOrderDetailExample example = new IscmStoreReturnExecuteOrderDetailExample();
            example.createCriteria()
                    .andReturnOrderNoIn(subOrderNoList)
                    .andProcessStatusEqualTo(StoreReturnExecuteProcessStatusEnum.ISSUING.getCode())
                    .andCreatedMonthEqualTo(month);
            List<IscmStoreReturnExecuteOrderDetail> orderList = iscmStoreReturnExecuteOrderDetailMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(orderList)) {
                return;
            }
            Map<String, MdmStoreBaseDTO> storeMap = storeService.findStoreByStoreNos(orderList.stream().map(IscmStoreReturnExecuteOrderDetail::getStoreCode).distinct().collect(Collectors.toList())).stream().collect(Collectors.toMap(v -> v.getStoreNo(), Function.identity(), (k1, k2) -> k1));
            List<ReturnWarehouseExecuteIssueToPosDTO> subToPosDTOList = orderList.stream().map(v -> {
                ReturnWarehouseExecuteIssueToPosDTO toPosDTO = new ReturnWarehouseExecuteIssueToPosDTO();
                BeanUtils.copyProperties(v, toPosDTO);
                toPosDTO.setOrderNo(v.getReturnOrderNo());
                toPosDTO.setValidityDays(v.getIssueValidityDays());
                if (v.getReturnBusinessType().equals(ReturnWarehouseBusinessTypeEnum.DEFECTIVE_RETURN.getCode())) {
                    toPosDTO.setAutoAuditLevel(2);
                    if (null == v.getHandleQuantity()) {
                        toPosDTO.setModifyLevel(1);
                    } else {
                        toPosDTO.setModifyLevel(2);
                    }
                    toPosDTO.setWarehouseCode(null);// 不良品不传仓库
                }else if ( v.getReturnBusinessType().equals(ReturnWarehouseBusinessTypeEnum.UNMANAGE_GOODS_RETURN.getCode()) ) {
                    toPosDTO.setAutoAuditLevel(2);
                    toPosDTO.setModifyLevel(1);
                    toPosDTO.setWarehouseCode(null);// 不良品不传仓库
                } else if (v.getReturnBusinessType().equals(ReturnWarehouseBusinessTypeEnum.IMPORT_RETURN.getCode())) {
                    toPosDTO.setAutoAuditLevel(0);
                    toPosDTO.setModifyLevel(0);
                } else {
                    toPosDTO.setAutoAuditLevel(2);
                    toPosDTO.setModifyLevel(2);
                }
                MdmStoreBaseDTO mdmStoreBaseDTO = storeMap.get(v.getStoreCode());
                if (null != mdmStoreBaseDTO) {
                    toPosDTO.setCompanyCode(mdmStoreBaseDTO.getComId());
                }
                return toPosDTO;
            }).collect(Collectors.toList());
            toPosDTOList.addAll(subToPosDTOList);
        });
        // 按单号分组
        Map<String, List<ReturnWarehouseExecuteIssueToPosDTO>> toPosDTOMap = toPosDTOList.stream().collect(Collectors.groupingBy(ReturnWarehouseExecuteIssueToPosDTO::getOrderNo));
        toPosDTOMap.forEach((k ,v) -> Lists.partition(v, Constants.PUSH_TO_MB_ONCE_MAX_SIZE).forEach(subList -> {
            List<String> rowNos = subList.stream().map(ReturnWarehouseExecuteIssueToPosDTO::getRowNo).collect(Collectors.toList());
            try {
                returnWarehouseExecutePushService.pushToPOS(null, subList);
                logger.info("下发成功,toPosDTOList={}", subList);
            } catch (Exception e) {
                logger.error("下发失败,toPosDTOList={}", subList, e);
                try {
                    returnWarehouseExecuteUpdateService.updateProcessStatus(k, rowNos, month, StoreReturnExecuteProcessStatusEnum.ISSUE_FAILED.getCode());
                } catch (Exception e1) {
                    logger.error("单号[{}],下发失败更新状态异常,涉及行号[{}]", k, rowNos, e1);
                }
            }
        }));
    }

    /**
     * 获取库存信息(pos-es)
     *
     * @param businessIds
     * @param goodsNos
     * @return
     */
    private List<StockGoodsBatchCode> getStockEs(List<Long> businessIds, List<Long> storeIds, List<String> goodsNos) {
        // 查pos-es中台的库存
        StockGoodsBatchCodeEsParam stockParam = new StockGoodsBatchCodeEsParam();
        stockParam.setBusinessIdList(businessIds);
        stockParam.setStoreIdList(storeIds);
        stockParam.setGoodsNoList(goodsNos);
        List<StockGoodsBatchCode> stockList = new ArrayList<>();
        for (int i = 0; ; i++) {
            stockParam.setPageNum(i + 1);
            stockParam.setPageSize(Constants.FEIGN_SERVICE_ONCE_MAX_VALUE);
            logger.info("getStockEs|查询库存信息 param -> {}", JSON.toJSONString(stockParam));
            List<StockGoodsBatchCode> stockTemp;
            try {
                stockTemp = posService.getGoodsStockByCompany(stockParam);
            } catch (Exception e) {
                logger.error("getStockEs|查询库存信息异常,第{}次", i + 1, e);
                break;
            }
            if (CollectionUtils.isEmpty(stockTemp)) {
                logger.info("getStockEs|库存信息查询完毕 次数:{}", i + 1);
                break;
            }
            stockList.addAll(stockTemp);
        }
        logger.info("getStockEs|stockList.size={}", stockList.size());
        return stockList;
    }

    /**
     * 获取库存信息(库存中台)
     *
     * @param businessId
     * @param storeId
     * @param goodsNos
     * @return
     */
    private List<StockGoodsBatchCodeSimpleInfo> getStock(Long businessId, Long storeId, List<String> goodsNos) {
        // 查库存中台的库存
        StockGoodsPagableQueryParam stockParam = new StockGoodsPagableQueryParam();
        stockParam.setGoodsNos(goodsNos);
        stockParam.setBusinessId(businessId);
        stockParam.setStoreId(storeId);
        logger.info("从库存中台查询库存信息 param -> {}", JSON.toJSONString(stockParam));
        List<StockGoodsBatchCodeSimpleInfo> stockList = new ArrayList<>();
        try {
            stockList = stockcenterService.goodsBatchCodePage(stockParam).values().stream().flatMap(List::stream).collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("从库存中台查询库存信息异常", e);
        }
        return stockList;
    }

    private Map<String, BigDecimal> getBatchStockEs(List<Long> companyOrgIds, List<Long> storeOrgIds, List<String> goodsNos) {
        List<OrgDTO> companyOrgDTOList;
        List<OrgDTO> storeOrgDTOList;
        try {
            companyOrgDTOList = permissionService.listOrgInfoByIds(companyOrgIds, false);
        } catch (Exception e) {
            logger.error("获取公司组织信息异常", e);
            return Collections.emptyMap();
        }
        try {
            storeOrgDTOList = permissionService.listOrgInfoByIds(storeOrgIds, false);
        } catch (Exception e) {
            logger.error("获取门店组织信息异常", e);
            return Collections.emptyMap();
        }

        List<Long> businessIds = companyOrgDTOList.stream().map(OrgDTO::getOutId).filter(Objects::nonNull).collect(Collectors.toList());
        List<Long> storeIds = storeOrgDTOList.stream().map(OrgDTO::getOutId).filter(Objects::nonNull).collect(Collectors.toList());

        List<StockGoodsBatchCode> stockList = getStockEs(businessIds, storeIds, goodsNos);
        if (CollectionUtils.isEmpty(stockList)) {
            logger.info("从es没有获取到库存信息");
            return Collections.emptyMap();
        }
        Map<Long, OrgDTO> companyOrgDTOMap = companyOrgDTOList.stream().collect(Collectors.toMap(OrgDTO::getOutId, Function.identity(), (k1,k2) -> k1));
        Map<Long, OrgDTO> storeOrgDTOMap = storeOrgDTOList.stream().collect(Collectors.toMap(OrgDTO::getOutId, Function.identity(), (k1,k2) -> k1));
        return stockList.stream().collect(Collectors.groupingBy(
                v -> companyOrgDTOMap.get(v.getBusinessId()).getId() + "-" + storeOrgDTOMap.get(v.getStoreId()).getId() + "-" + v.getSkuMerchantCode() + "-" + v.getBatchNo(),
                Collectors.reducing(BigDecimal.ZERO, v -> Optional.ofNullable(v.getStock()).orElse(BigDecimal.ZERO), BigDecimal::add)
        ));
    }

    private Map<String, BigDecimal> getBatchStock(Long companyOrgId, Long storeOrgId, List<String> goodsNos) {
        OrgVO companyOrgVO;
        OrgVO storeOrgVO;
        try {
            companyOrgVO = permissionService.getOrgInfoById(companyOrgId);
        } catch (Exception e) {
            logger.error("获取公司组织信息异常", e);
            return Collections.emptyMap();
        }
        try {
            storeOrgVO = permissionService.getOrgInfoById(storeOrgId);
        } catch (Exception e) {
            logger.error("获取门店组织信息异常", e);
            return Collections.emptyMap();
        }

        List<StockGoodsBatchCodeSimpleInfo> stockList = getStock(companyOrgVO.getOutId(), storeOrgVO.getOutId(), goodsNos);
        if (CollectionUtils.isEmpty(stockList)) {
            logger.info("从库存中台没有获取到库存信息");
            return Collections.emptyMap();
        }
        return stockList.stream().collect(Collectors.groupingBy(
                v -> companyOrgId + "-" + storeOrgId + "-" + v.getSkuMerchantCode() + "-" + v.getBatchNo(),
                Collectors.reducing(BigDecimal.ZERO, v -> v.getStock().subtract(v.getWaitStock().add(v.getUnqualifiedAreaStock())).add(v.getTransitStock()), BigDecimal::add)
        ));
    }

    @Transactional(rollbackFor = {Exception.class})
    @Override
    public void beforeIssue(TokenUserDTO userDTO, ReturnWarehouseExecuteIssueParam param, boolean reIssue) throws Exception {
        checkMonth(param.getMonth());
        param.setStoreOrgIds(checkPerm(userDTO, null, param.getStoreOrgIds()));
        if (CollectionUtils.isEmpty(param.getIds())) {
            throw new BusinessErrorException("参数[ids]不能为空");
        }
        if (null == param.getValidityDays()) {
            throw new BusinessErrorException("请输入失效天数");
        }
        if (param.getValidityDays() <= 0) {
            throw new BusinessErrorException("失效天数只能输入大于0的整数");
        }
        param.getIds().forEach(id -> {
            if (issuingIdCache.contains(param.getMonth() + "-" + id)) {
                if (reIssue) {
                    throw new BusinessErrorException("下发中...请勿重复操作,id=" + id);
                } else {
                    throw new BusinessErrorException("提审或下发中...请勿重复操作,id=" + id);
                }
            }
            issuingIdCache.add(param.getMonth() + "-" + id);
        });
        // 更新行号
        if (!reIssue) {
            try {
                returnWarehouseExecuteUpdateService.updateRowNo(param.getIds(), param.getMonth());
            } catch (Exception e) {
                logger.error("下发生成单号行号失败", e);
                throw e;
            }
        }
    }

    @Override
    public void autoUpdateIssuingOrder(byte processStatus) {
        Date now = new Date();
        Date prevDate = DateUtils.addDays(now, -1);
        Set<Integer> months = DateUtils.getRangeMonths(prevDate, now);

        months.forEach(month -> {
            logger.info("autoUpdateIssuingOrder|month={}", month);
            IscmStoreReturnExecuteOrderMainExample orderExample = new IscmStoreReturnExecuteOrderMainExample();
            orderExample.createCriteria()
                    .andCreatedMonthEqualTo(month)
                    .andGmtCreateGreaterThan(DateUtils.getStartOfDay(prevDate))
                    .andGmtCreateLessThan(DateUtils.getEndOfDay(prevDate))
                    .andProcessStatusEqualTo(StoreReturnExecuteProcessStatusEnum.ISSUING.getCode());
            List<IscmStoreReturnExecuteOrderMain> orderList = iscmStoreReturnExecuteOrderMainMapper.selectByExample(orderExample);

            IscmStoreReturnExecuteOrderMain orderRecord = new IscmStoreReturnExecuteOrderMain();
            orderRecord.setProcessStatus(StoreReturnExecuteProcessStatusEnum.ISSUE_FAILED.getCode());
            orderRecord.setGmtUpdate(now);
            orderRecord.setUpdatedName(Constants.DEFAULT_CREATE_NAME);
            int count = iscmStoreReturnExecuteOrderMainMapper.updateByExampleSelective(orderRecord, orderExample);
            logger.info("autoUpdateIssuingOrder|自动更新成功单据数量={}", count);

            IscmStoreReturnExecuteOrderDetailExample orderDetailExample = new IscmStoreReturnExecuteOrderDetailExample();
            orderDetailExample.createCriteria()
                    .andCreatedMonthEqualTo(month)
                    .andGmtCreateGreaterThan(DateUtils.getStartOfDay(prevDate))
                    .andGmtCreateLessThan(DateUtils.getEndOfDay(prevDate))
                    .andProcessStatusEqualTo(StoreReturnExecuteProcessStatusEnum.ISSUING.getCode());
            IscmStoreReturnExecuteOrderDetail orderDetailRecord = new IscmStoreReturnExecuteOrderDetail();
            orderDetailRecord.setProcessStatus(StoreReturnExecuteProcessStatusEnum.ISSUE_FAILED.getCode());
            orderDetailRecord.setGmtUpdate(now);
            orderDetailRecord.setUpdatedName(Constants.DEFAULT_CREATE_NAME);
            count = iscmStoreReturnExecuteOrderDetailMapper.updateByExampleSelective(orderDetailRecord, orderDetailExample);
            logger.info("autoUpdateIssuingOrder|自动更新成功单据明细数量={}", count);

            // 更新 process_status_all
            Set<String> orderNos = orderList.stream().map(IscmStoreReturnExecuteOrderMain::getReturnOrderNo).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(orderNos)) {
                returnWarehouseExecuteUpdateService.updateProcessStatusAll(month, new ArrayList<>(orderNos));
            }
        });
    }

    /**
     * 校验权限(门店级)
     *
     * @param userDTO
     * @param storeOrgIds
     * @throws Exception
     */
    private List<Long> checkPerm(TokenUserDTO userDTO, List<Long> companyOrgIds, List<Long> storeOrgIds) throws Exception {

        if (CollectionUtils.isEmpty(companyOrgIds) && CollectionUtils.isEmpty(storeOrgIds)) {
            List<OrgTreeVO> orgTreeList = permissionService.getAbsScopeOrgTrees(userDTO.getUserId(), null, Lists.newArrayList(OrgTypeEnum.ORG_TYPE_STORE.getCode()));
            if (CollectionUtils.isEmpty(orgTreeList)) {
                throw new BusinessErrorException("当前登录人员没有连锁或门店级别的数据权限,请联系管理员");
            }
            return orgTreeList.stream().map(OrgTreeVO::getId).distinct().collect(Collectors.toList());
        }
        List<OrgTreeVO> orgTreeList = permissionService.getAbsScopeOrgTrees(userDTO.getUserId(), null, Lists.newArrayList(OrgTypeEnum.ORG_TYPE_BUSINESS.getCode(), OrgTypeEnum.ORG_TYPE_STORE.getCode()));
        if (CollectionUtils.isEmpty(orgTreeList)) {
            throw new BusinessErrorException("当前登录人员没有连锁或门店级别的数据权限,请联系管理员");
        }
        List<Long> returnOrgIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(companyOrgIds)) {
            Map<Long, OrgTreeVO> companyMap = orgTreeList.stream().filter(v -> companyOrgIds.contains(v.getId())).collect(Collectors.toMap(v -> v.getId(), Function.identity(), (k1, k2) -> k1));
            if (MapUtils.isEmpty(companyMap)) {
                throw new BusinessErrorException("当前登录人员没有连锁级别的数据权限,请联系管理员");
            }
            companyMap.forEach((k,v) -> {
                if (CollectionUtils.isNotEmpty(v.getChildren())) {
                    returnOrgIds.addAll(v.getChildren().stream().map(OrgTreeVO::getId).distinct().collect(Collectors.toList()));
                }
            });
        }
        if (CollectionUtils.isNotEmpty(storeOrgIds)) {
            if (CollectionUtils.isNotEmpty(returnOrgIds)) {
                Iterator<Long> iterator = returnOrgIds.iterator();
                while (iterator.hasNext()) {
                    if (!storeOrgIds.contains(iterator.next())) {
                        iterator.remove();
                    }
                }
                if(CollectionUtils.isEmpty(returnOrgIds)) {
                    throw new BusinessErrorException("您没有查询门店权限,请联系管理员");
                }
            } else {
                returnOrgIds.addAll(orgTreeList.stream().filter(v -> CollectionUtils.isNotEmpty(v.getChildren())).map(OrgTreeVO::getChildren).flatMap(v -> v.stream()).map(OrgTreeVO::getId).filter(v -> storeOrgIds.contains(v)).collect(Collectors.toList()));
                if (CollectionUtils.isEmpty(returnOrgIds)) {
                    throw new BusinessErrorException("您没有查询门店权限,请联系管理员");
                }
            }
        }
        return returnOrgIds;
    }

    /**
     * 校验参数
     *
     * @param param
     * @throws BusinessErrorException
     * @throws NullPointerException
     */
    private void checkParam(ReturnWarehouseExecuteParam param) throws BusinessErrorException, NullPointerException {
        logger.info("checkParam|param before check -> {}", JSON.toJSONString(param));
        if (null == param.getMonth()) {
            int thisMonth = DateUtils.thisMonth();
            param.setMonth(Integer.parseInt(DateUtils.thisYear() + (thisMonth < 10 ? "0" : "") + thisMonth));
        } else {
            checkMonth(param.getMonth());
        }
        if (null == param.getPage()) {
            param.setPage(0);
        }
        if (null == param.getPageSize()) {
            param.setPageSize(50);
        }
        if (param.getPage() < 0 || param.getPageSize() <= 0) {
            throw new BusinessErrorException("分页参数有误");
        }
        if (StringUtils.isBlank(param.getGmtCreateStart())) {
            Date firstDay = DateUtils.getFirstDayOfYearMonth(DateUtils.thisYear(), DateUtils.thisMonth());
            param.setStartDate(DateUtils.getStartOfDay(firstDay));
        } else {
            if (!DateUtils.isValidDate(param.getGmtCreateStart(), DateUtils.DATE_PATTERN)) {
                throw new BusinessErrorException("创建日期格式有误,正确格式[yyyy-MM-dd]");
            }
            Date date = DateUtils.parseDate(param.getGmtCreateStart(), DateUtils.DATE_PATTERN);
            param.setStartDate(DateUtils.getStartOfDay(Objects.requireNonNull(date)));
        }
        if (StringUtils.isBlank(param.getGmtCreateEnd())) {
            Date lastDay = DateUtils.getLastDayOfYearMonth(DateUtils.thisYear(), DateUtils.thisMonth());
            param.setEndDate(DateUtils.getEndOfDay(lastDay));
        } else {
            if (!DateUtils.isValidDate(param.getGmtCreateEnd(), DateUtils.DATE_PATTERN)) {
                throw new BusinessErrorException("创建日期格式有误,正确格式[yyyy-MM-dd]");
            }
            Date date = DateUtils.parseDate(param.getGmtCreateEnd(), DateUtils.DATE_PATTERN);
            param.setEndDate(DateUtils.getEndOfDay(Objects.requireNonNull(date)));
        }
        if (param.getStartDate().after(param.getEndDate())) {
            throw new BusinessErrorException("起始时间不能大于结束时间");
        }
        if (param.getEndDate().after(new Date())) {
            logger.info("checkParam|endDate大于当前时间,只统计到当前时间");
            param.setEndDate(new Date());
        }
        logger.info("checkParam|param after check -> {}", JSON.toJSONString(param));
    }

    private void checkMonth(Integer month) throws BusinessErrorException {
        if (null == month) {
            throw new BusinessErrorException("[month]参数不能为空");
        }
        if (!DateUtils.isValidDate(month+"", "yyyyMM")) {
            throw new BusinessErrorException("[month]参数格式有误,正确格式[yyyyMM]");
        }
        int thisMonth = DateUtils.thisMonth();
        int currMonth = Integer.parseInt(DateUtils.thisYear() + (thisMonth < 10 ? "0" : "") + thisMonth);
        if (month > currMonth) {
            throw new BusinessErrorException("[month]参数不能大于当前月");
        }
    }

    /**
     * 校验id集合长度
     *
     * @param ids
     * @throws BusinessErrorException
     */
    private void checkIdLen(List<Long> ids) throws BusinessErrorException {
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessErrorException("参数[ids]不能为空");
        }
        if (ids.size() > Constants.MAX_PAGE_SIZE) {
            throw new BusinessErrorException("参数[ids]长度不能超过" + Constants.MAX_PAGE_SIZE);
        }
    }

    /**
     * 校验接收的单号
     *
     * @param orderNo
     * @throws BusinessErrorException
     */
    private int checkReceivedOrderNo(String orderNo) throws BusinessErrorException {
        int month;
        try {
            String yearFirstTwoDigit = String.valueOf(DateUtils.thisYear()).substring(0, 2);
            month = Integer.parseInt(yearFirstTwoDigit + orderNo.substring(orderNo.length()-10, orderNo.length()-6));
            checkMonth(month);
        } catch (Exception e) {
            throw new BusinessErrorException("单号["+orderNo+"]解析错误");
        }
        return month;
    }

    /**
     * 下发退仓数量 = min(实时批号库存,确定退仓数量)
     *
     * @param returnQuantity
     * @param batchStock
     * @return
     */
    private BigDecimal genIssueReturnQuantity(BigDecimal returnQuantity, BigDecimal batchStock) {
        if (null == returnQuantity || null == batchStock) {
            return null;
        }
        return returnQuantity.min(batchStock);
    }

    /**
     * 下发退仓金额 = 登记时成本金额/登记数量 * 下发退仓数量
     *
     * @param costAmount
     * @param registerQuantity
     * @param issueReturnQuantity
     * @return
     */
    private BigDecimal genIssueReturnAmount(BigDecimal costAmount, BigDecimal registerQuantity, BigDecimal issueReturnQuantity) {
        if (null == costAmount || null == registerQuantity || null == issueReturnQuantity) {
            return BigDecimal.ZERO;
        }
        return costAmount.divide(registerQuantity, 4, RoundingMode.HALF_UP).multiply(issueReturnQuantity);
    }

    /**
     * 明细批号key
     *
     * @param detail
     * @return
     */
    private String genBatchNoKey(IscmStoreReturnExecuteOrderDetail detail) {
        return detail.getCompanyOrgId() + "-" + detail.getStoreOrgId() + "-" + detail.getGoodsNo() + "-" + detail.getBatchNo();
    }

    private Byte getSourceStatus(Byte processStatus, String processStatusAll, List<Byte> paramStatusList) {
        if (StringUtils.isBlank(processStatusAll) || CollectionUtils.isEmpty(paramStatusList)) {
            return processStatus;
        }
        String[] processStatusArr = processStatusAll.split("\\|");
        List<Byte> processStatusList = Arrays.stream(processStatusArr).filter(StringUtils::isNotEmpty).map(Byte::valueOf).collect(Collectors.toList());
        processStatusList.retainAll(paramStatusList);// 取交集
        if (CollectionUtils.isEmpty(processStatusList)) {
            return processStatus;
        }
        if (processStatusList.contains(processStatus)) {
            return processStatus;
        }
        return processStatusList.get(0);
    }

    private void updateOrder(TokenUserDTO userDTO, List<String> orderNos, Integer month, Byte processStatus, Byte sourceStatus, Integer validityDays) throws Exception {
        Date now = new Date();
        List<StoreReturnExecuteOrderUpdateDTO> orderUpdateDTOS = new ArrayList<>();
        for (String orderNo : orderNos) {
            StoreReturnExecuteOrderUpdateDTO orderUpdateDTO = new StoreReturnExecuteOrderUpdateDTO();
            orderUpdateDTO.setMonth(month);
            orderUpdateDTO.setOrderNo(orderNo);
            orderUpdateDTO.setValidityDays(validityDays);
            orderUpdateDTO.setProcessStatus(processStatus);
            orderUpdateDTO.setSourceStatus(sourceStatus);
            orderUpdateDTO.setUpdatedBy(userDTO.getUserId());
            orderUpdateDTO.setUpdatedName(userDTO.getName());
            orderUpdateDTO.setGmtUpdate(now);
            orderUpdateDTOS.add(orderUpdateDTO);
        }
        returnWarehouseExecuteUpdateService.updateOrder(orderUpdateDTOS);
    }
}

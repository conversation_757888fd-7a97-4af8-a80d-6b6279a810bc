package com.cowell.iscm.service.impl;

import com.alibaba.fastjson.JSON;
import com.cowell.iscm.config.Constants;
import com.cowell.iscm.entity.*;
import com.cowell.iscm.enums.*;
import com.cowell.iscm.mapper.*;
import com.cowell.iscm.mapper.extend.IscmStoreReturnExecuteOrderDetailExtendMapper;
import com.cowell.iscm.mapper.extend.IscmStoreReturnExecuteOrderMainExtendMapper;
import com.cowell.iscm.rest.errors.BusinessErrorException;
import com.cowell.iscm.service.ReturnWarehouseExecuteGenService;
import com.cowell.iscm.service.ReturnWarehouseExecuteService;
import com.cowell.iscm.service.ReturnWarehouseExecuteUpdateService;
import com.cowell.iscm.service.dto.CommonProcessDTO;
import com.cowell.iscm.service.dto.ListToExcelMultiSheetDTO;
import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.applyParam.CommonListResponse;
import com.cowell.iscm.service.dto.applyParam.CommonRes;
import com.cowell.iscm.service.dto.applyParam.PageResult;
import com.cowell.iscm.service.dto.returnWarehouse.*;
import com.cowell.iscm.service.dto.storeAccessAuditor.OptionDto;
import com.cowell.iscm.service.feign.*;
import com.cowell.iscm.service.feign.dto.*;
import com.cowell.iscm.utils.BeanUtils;
import com.cowell.iscm.utils.DateUtils;
import com.cowell.iscm.utils.HutoolUtil;
import com.cowell.permission.vo.OrgVO;
import com.google.common.collect.Lists;
import com.mysql.jdbc.exceptions.MySQLIntegrityConstraintViolationException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.dao.DataAccessException;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by schuangxigang on 2022/2/9 19:37.
 */
@Service
public class ReturnWarehouseExecuteGenServiceImpl implements ReturnWarehouseExecuteGenService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private IscmStoreReturnConfirmOrderMapper iscmStoreReturnConfirmOrderMapper;
    @Autowired
    private IscmStoreReturnConfirmOrderDetailMapper iscmStoreReturnConfirmOrderDetailMapper;
    @Autowired
    private IscmStoreReturnExecuteOrderMainMapper iscmStoreReturnExecuteOrderMainMapper;
    @Autowired
    private IscmStoreReturnExecuteOrderMainExtendMapper iscmStoreReturnExecuteOrderExtendMainMapper;
    @Autowired
    private IscmStoreReturnExecuteOrderDetailMapper iscmStoreReturnExecuteOrderDetailMapper;
    @Autowired
    private IscmStoreReturnExecuteOrderDetailExtendMapper iscmStoreReturnExecuteOrderDetailExtendMapper;
    @Autowired
    private ReturnWarehouseExecuteUpdateService returnWarehouseExecuteUpdateService;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private ForestService forestService;
    @Autowired
    private SearchApiService searchApiService;
    @Autowired
    private StockcenterService stockcenterService;
    @Autowired
    private PurchaseService purchaseService;
    @Autowired
    private ReturnWarehouseExecuteService returnWarehouseExecuteService;
    @Autowired
    private ScibFeignClient scibFeignClient;
    @Autowired
    private SupplychainPlusFeignClient supplychainPlusFeignClient;
    @Autowired
    private IAlertService alertService;

    @Autowired
    private RedissonClient redissonClient;

    @Value("${iscm.return.exec.import.max:30000}")
    private Integer importMax;

    @Autowired
    @Qualifier("returnExecImportExecutor")
    private AsyncTaskExecutor executor;

    // 退仓单编号缓存Key(仓库编码+门店编码+日期+001)
    private final String ISCM_RETURN_WAREHOUSE_ORDER_CODE_CACHE = "ISCM-RETURN-WAREHOUSE-ORDER-CODE-CACHE-";
    private final String ISCM_RETURN_WAREHOUSE_IMPORT_CACHE = "ISCM-RETURN-WAREHOUSE-IMPORT-CACHE-";

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String genReturnWarehouseExec(TokenUserDTO userDTO, GenExecParam param) throws Exception {
        try {
            if (CollectionUtils.isEmpty(param.getReturnOrderDetailIds())) {
                throw new BusinessErrorException("请选择退仓明细数据");
            }
            if (StringUtils.isBlank(param.getReturnConfirmOrderNo())) {
                throw new BusinessErrorException("退仓单号不能为空");
            }
            if (Objects.isNull(param.getWarehouseValidityDaysMin()) || param.getWarehouseValidityDaysMin() <= 0) {
                throw new BusinessErrorException("仓库可接收最小效期天数不能为空且需>0");
            }
            IscmStoreReturnConfirmOrderDetailExample detailExample = new IscmStoreReturnConfirmOrderDetailExample();
            detailExample.createCriteria().andReturnConfirmOrderNoEqualTo(param.getReturnConfirmOrderNo()).andIdIn(param.getReturnOrderDetailIds()).andRegisterQuantityGreaterThan(BigDecimal.ZERO);
            List<IscmStoreReturnConfirmOrderDetail> details = iscmStoreReturnConfirmOrderDetailMapper.selectByExample(detailExample);
            if (CollectionUtils.isEmpty(details)) {
                throw new BusinessErrorException("单号:" + param.getReturnConfirmOrderNo() + "下的所选明细数据不存在,请确认是否已经生成退仓执行单");
            }
            // 按照detailId排序
            List<IscmStoreReturnConfirmOrderDetail> sortDetails = new ArrayList<>();
            for (Long id : param.getReturnOrderDetailIds()) {
                for (IscmStoreReturnConfirmOrderDetail detail : details) {
                    if (id.equals(detail.getId())) {
                        sortDetails.add(detail);
                        break;
                    }
                }
            }
            IscmStoreReturnConfirmOrderExample example = new IscmStoreReturnConfirmOrderExample();
            example.createCriteria().andReturnConfirmOrderNoEqualTo(param.getReturnConfirmOrderNo());
            IscmStoreReturnConfirmOrder order = iscmStoreReturnConfirmOrderMapper.selectByExample(example).stream().findAny().orElseThrow(() -> new BusinessErrorException("单号:" + param.getReturnConfirmOrderNo() + "下的所选数据为空,请重新选择"));
            BigDecimal returnedQuantity = BigDecimal.ZERO;
            Iterator<IscmStoreReturnConfirmOrderDetail> iterator = sortDetails.iterator();
            StringBuilder errorMsg = new StringBuilder();
            BigDecimal warehousePlanReceiveReturnQuantity = Objects.isNull(param.getWarehousePlanReceiveReturnQuantity()) ? order.getWarehousePlanReceiveReturnQuantity() : param.getWarehousePlanReceiveReturnQuantity();
            while (iterator.hasNext()) {
                IscmStoreReturnConfirmOrderDetail detail = iterator.next();
                if (param.getWarehouseValidityDaysMin() <= 0 || detail.getValidityDays() < param.getWarehouseValidityDaysMin()) {
                    errorMsg.append("门店:").append(detail.getStoreCode()).append(",批号:").append(detail.getBatchNo()).append("失效天数<仓库可接收最小效期天数:").append(param.getWarehouseValidityDaysMin()).append(",不能退仓");
                    iterator.remove();
                    continue;
                }
                // 禁止返仓 = 是的行，变为灰色，不可选择。
                if ("是".equals(detail.getForbidReturnWarehouse())) {
                    errorMsg.append("门店:").append(detail.getStoreCode()).append(",批号:").append(detail.getBatchNo()).append("是禁止返仓,不能退仓");
                    iterator.remove();
                    continue;
                }
                if (detail.getShouldReturnQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                    errorMsg.append("门店:").append(detail.getStoreCode()).append(",批号:").append(detail.getBatchNo()).append("退仓数量<=0,不能退仓");
                    iterator.remove();
                    continue;
                }
                // 总退仓数量 > 页面的【计划的仓库可接收退仓数量】不可退了
                returnedQuantity = returnedQuantity.add(detail.getReturnQuantity());
                if (returnedQuantity.compareTo(warehousePlanReceiveReturnQuantity) > 0) {
                    errorMsg.append("门店:").append(detail.getStoreCode()).append(",批号:").append(detail.getBatchNo()).append("总退仓数量:").append(returnedQuantity.stripTrailingZeros().toPlainString()).append(" <=计划的仓库可接收退仓数量:").append(warehousePlanReceiveReturnQuantity.stripTrailingZeros().toPlainString()).append("不能退仓");
                    iterator.remove();
                    continue;
                }
            }
            if (CollectionUtils.isEmpty(sortDetails)) {
                throw new BusinessErrorException(errorMsg.toString());
            }

            Date now = new Date();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(now);
            int createdMonth = calendar.get(Calendar.YEAR) * 100 + (calendar.get(Calendar.MONTH) + 1);

            ConcurrentHashMap<String, List<AuctionSpuBaseInfo>> auctionSpuBaseInfoMap = new ConcurrentHashMap<>();
            ConcurrentHashMap<String, String> executeOrderNoMap = new ConcurrentHashMap<>();// 单号集合
            ConcurrentHashMap<String, List<IscmStoreReturnExecuteOrderMain>> storeOrderExistsMap = new ConcurrentHashMap<>();// 门店已存在的订单
            Set<String> companyOrgIds = sortDetails.stream().map(v -> v.getCompanyOrgId() + "-" + v.getGoodsNo()).collect(Collectors.toSet());
            Set<String> storeOrgIds = sortDetails.stream().map(v -> v.getStoreOrgId() + "-" + v.getStoreCode() + "-" + v.getWarehouseCode()).collect(Collectors.toSet());
            companyOrgIds.forEach(k -> {
                try {
                    String[] ks = k.split("-");
                    OrgVO orgVO = permissionService.getOrgInfoById(Long.valueOf(ks[0]));
                    List<AuctionSpuBaseInfo> auctionSpuBaseInfos = forestService.batchFindSpuProperty(Lists.newArrayList(ks[1]), orgVO.getOutId());
                    auctionSpuBaseInfoMap.put(k, auctionSpuBaseInfos);
                } catch (Exception e) {
                    logger.error("获取采购组信息异常", e);
                }
            });
            storeOrgIds.forEach(k -> {
                String[] ks = k.split("-");
                Long storeOrgId = Long.valueOf(ks[0]);
                IscmStoreReturnExecuteOrderMainExample orderMainExample = newOrderMainExample(storeOrgId, ks[2], createdMonth, ReturnTypeEnum.CALCULATE.getType());
                List<IscmStoreReturnExecuteOrderMain> executeOrderExists = iscmStoreReturnExecuteOrderMainMapper.selectByExample(orderMainExample);
                storeOrderExistsMap.put(storeOrgId + "-" + ks[2], executeOrderExists);
                if (CollectionUtils.isEmpty(executeOrderExists)) {
                    String executeOrderNo = genOrderNo(ks[2], ks[1]);
                    executeOrderNoMap.put(k, executeOrderNo);
                } else {
                    executeOrderNoMap.put(k, executeOrderExists.get(0).getReturnOrderNo());
                }
            });

            // 已经存在的明细
            IscmStoreReturnExecuteOrderDetailExample executeOrderDetailExample = new IscmStoreReturnExecuteOrderDetailExample();
            executeOrderDetailExample.createCriteria()
                    .andStoreOrgIdIn(storeOrgIds.stream().map(v -> Long.valueOf(v.split("-")[0])).collect(Collectors.toList()))
                    .andCreatedMonthEqualTo(createdMonth);
//                    .andProcessStatusEqualTo(StoreReturnExecuteProcessStatusEnum.NON_APPROVED.getCode());
            List<IscmStoreReturnExecuteOrderDetail> executeOrderDetailExists = iscmStoreReturnExecuteOrderDetailMapper.selectByExample(executeOrderDetailExample);
            Set<String> batchNoUniqueKeyExists = executeOrderDetailExists.stream().map(this::genBatchNoUniqueKey).collect(Collectors.toSet());

            List<IscmStoreReturnExecuteOrderDetail> executeOrderDetails = sortDetails.stream().map(v -> {
                IscmStoreReturnExecuteOrderDetail executeOrderDetail = new IscmStoreReturnExecuteOrderDetail();
                BeanUtils.copyProperties(v, executeOrderDetail);
                executeOrderDetail.setReturnOrderNo(getOrderNo(executeOrderNoMap, executeOrderDetail));
                executeOrderDetail.setReturnBusinessType(v.getReturnBusinessType());
                executeOrderDetail.setCreatedMonth(createdMonth);
                executeOrderDetail.setCreatedBy(userDTO.getUserId());
                executeOrderDetail.setCreatedName(userDTO.getName());
                executeOrderDetail.setUpdatedBy(userDTO.getUserId());
                executeOrderDetail.setUpdatedName(userDTO.getName());
                executeOrderDetail.setProcessStatus(StoreReturnExecuteProcessStatusEnum.NON_APPROVED.getCode());
                executeOrderDetail.setRealReturnQuantity(BigDecimal.ZERO);
                executeOrderDetail.setIssueReturnQuantity(min(v.getReturnQuantity(), v.getBatchStock()));
                executeOrderDetail.setIssueReturnAmount(genIssueReturnAmount(v.getCostAmount(), v.getRegisterQuantity(), executeOrderDetail.getIssueReturnQuantity()));
                executeOrderDetail.setPosReturnOrderNo("");
                executeOrderDetail.setRowNo("");
                executeOrderDetail.setGmtCreate(now);
                executeOrderDetail.setGmtUpdate(now);
                executeOrderDetail.setReturnReasonType(Constants.ISCM_RETURN_REASON_DEFAULT);
                executeOrderDetail.setReturnReason(Constants.ISCM_RETURN_REASON_NAME_DEFAULT);

                List<AuctionSpuBaseInfo> auctionSpuBaseInfos = auctionSpuBaseInfoMap.get(v.getCompanyOrgId() + "-" + v.getGoodsNo());
                if (CollectionUtils.isNotEmpty(auctionSpuBaseInfos)) {
                    AuctionSpuBaseInfo auctionSpuBaseInfo = auctionSpuBaseInfos.get(0);
                    executeOrderDetail.setGoodsPurChannel(auctionSpuBaseInfo.getPurchchannel());
                    executeOrderDetail.setPurchaseGroup(auctionSpuBaseInfo.getEkgrp());
                    executeOrderDetail.setPurchaseOrgCode(auctionSpuBaseInfo.getOrderorg());
                    executeOrderDetail.setPurchaseOrgName("");
                }
                return executeOrderDetail;
            }).collect(Collectors.toList());
            executeOrderDetails.forEach(k -> {
                if (batchNoUniqueKeyExists.contains(genBatchNoUniqueKey(k))) {
                    errorMsg.append("门店:").append(k.getStoreCode())
                            .append("+仓库:").append(k.getWarehouseCode())
                            .append("+商品:").append(k.getGoodsNo())
                            .append("+批号:").append(k.getBatchNo())
                            .append("已经存在,将被过滤掉<br/>");
                }
            });
            // 移除掉已经添加过的明细(store_org_id,warehouse_code,return_order_no,goods_no,batch_no)
            executeOrderDetails.removeIf(v -> batchNoUniqueKeyExists.contains(genBatchNoUniqueKey(v)));
            Lists.partition(executeOrderDetails, Constants.BATCH_INSERT_ONCE_MAX_VALUE).forEach(v -> iscmStoreReturnExecuteOrderDetailExtendMapper.batchInsert(v));

            // 根据门店+仓库分组
            Map<String, List<IscmStoreReturnExecuteOrderDetail>> storeMap = executeOrderDetails.stream().collect(Collectors.groupingBy(v -> v.getStoreOrgId() + "-" + v.getWarehouseCode()));

            List<IscmStoreReturnExecuteOrderMain> batchInsert = new ArrayList<>();
            List<IscmStoreReturnExecuteOrderMain> batchUpdate = new ArrayList<>();
            logger.info("storeOrderExistsMap={}", JSON.toJSONString(storeOrderExistsMap));
            logger.info("executeOrderNoMap={}", JSON.toJSONString(executeOrderNoMap));
            storeMap.forEach((k, v) -> {
                String[] ks = k.split("-");
                StoreReturnExecuteOrderSum executeOrderSum = new StoreReturnExecuteOrderSum();
                List<StoreReturnExecuteOrderQty> orderQtyList = new ArrayList<>();
                BeanUtils.copyList(v, orderQtyList, StoreReturnExecuteOrderQty.class);
                executeOrderSum.sum(orderQtyList, false);

                List<IscmStoreReturnExecuteOrderMain> executeOrderExists = storeOrderExistsMap.get(k);
                if (CollectionUtils.isEmpty(executeOrderExists)) {
                    IscmStoreReturnExecuteOrderDetail executeOrderDetail = v.get(0);
                    IscmStoreReturnExecuteOrderMain executeOrder = new IscmStoreReturnExecuteOrderMain();
                    BeanUtils.copyProperties(executeOrderDetail, executeOrder);
                    BeanUtils.copyProperties(executeOrderSum, executeOrder);
                    executeOrder.setReturnOrderNo(getOrderNo(executeOrderNoMap, executeOrderDetail));
                    executeOrder.setReturnType(ReturnTypeEnum.CALCULATE.getType());
                    executeOrder.setPosReturnOrderNo("");
                    executeOrder.setCreatedBy(userDTO.getUserId());
                    executeOrder.setCreatedName(userDTO.getName());
                    executeOrder.setUpdatedBy(userDTO.getUserId());
                    executeOrder.setUpdatedName(userDTO.getName());
                    executeOrder.setCreatedMonth(createdMonth);
                    executeOrder.setProcessStatus(StoreReturnExecuteProcessStatusEnum.NON_APPROVED.getCode());
                    executeOrder.setProcessStatusAll("|" + StoreReturnExecuteProcessStatusEnum.NON_APPROVED.getCode() + "|");
                    executeOrder.setGmtCreate(now);
                    executeOrder.setGmtUpdate(now);
                    executeOrder.setIssueValidityDays(0);
                    batchInsert.add(executeOrder);
//                    iscmStoreReturnExecuteOrderMainMapper.insertSelective(executeOrder);
                } else {
                    batchUpdate.add(sum(executeOrderSum, executeOrderExists.get(0)));
                }
            });
            if (CollectionUtils.isNotEmpty(batchInsert)) {
                iscmStoreReturnExecuteOrderExtendMainMapper.batchInsert(batchInsert);
            }
            if (CollectionUtils.isNotEmpty(batchUpdate)) {
                RLock lock = redissonClient.getLock("UPDATE-RETURN-EXECUTE-LOCK-" + batchUpdate.get(0).getPlatformOrgId().toString());
                lock.lock();
                try {
                    logger.info("更新开始...");
                    iscmStoreReturnExecuteOrderExtendMainMapper.batchUpdate(batchUpdate, createdMonth);
                } catch (DataAccessException e) {
                    final Throwable cause = e.getCause();
                    if (cause instanceof MySQLIntegrityConstraintViolationException) {
                        throw new BusinessErrorException(cause.getMessage());
                    }
                } finally {
                    lock.unlock();
                }
            }
            // 删除确认单
//            iscmStoreReturnConfirmOrderDetailMapper.deleteByExample(detailExample);
            IscmStoreReturnConfirmOrderDetailExample detailExample1 = new IscmStoreReturnConfirmOrderDetailExample();
            detailExample1.createCriteria().andReturnConfirmOrderNoEqualTo(order.getReturnConfirmOrderNo());
            iscmStoreReturnConfirmOrderDetailMapper.deleteByExample(detailExample1);
            IscmStoreReturnConfirmOrderDetailExample detailCountExample = new IscmStoreReturnConfirmOrderDetailExample();
            detailCountExample.createCriteria().andReturnConfirmOrderNoEqualTo(order.getReturnConfirmOrderNo());
//            long detailCount = iscmStoreReturnConfirmOrderDetailMapper.countByExample(detailCountExample);
//            if (detailCount <= 0) {
                iscmStoreReturnConfirmOrderMapper.deleteByPrimaryKey(order.getId());
//            }
            // 更新退仓状态
            returnWarehouseExecuteUpdateService.updateReturnWarehouseStatus(new StoreReturnWarehouseGoods(
                    order.getReturnBusinessType(),
                    order.getGoodsNo(),
                    order.getWarehouseCode(),
                    sortDetails.stream().map(IscmStoreReturnConfirmOrderDetail::getStoreOrgId).distinct().collect(Collectors.toList()),
                    sortDetails.stream().map(IscmStoreReturnConfirmOrderDetail::getStoreCode).distinct().collect(Collectors.toList()),
                    sortDetails.stream().map(IscmStoreReturnConfirmOrderDetail::getRegisterOrderNo).distinct().collect(Collectors.toList()),
                    order.getRegisterMonth()
            ), RegisterReturnWarehouseTypeEnum.RETURNED_WAREHOUSE);
            return errorMsg.length() <= 0 ? "生成成功" : errorMsg.toString();
        } catch (BusinessErrorException e) {
            logger.warn("确认加入退仓执行清单失败", e);
            throw e;
        } catch (Exception e) {
            logger.error("确认加入退仓执行清单失败", e);
            throw e;
        }
    }

    @Override
    public String importReturnWarehouseExec(TokenUserDTO userDTO, MultipartFile file) throws Exception {
        try {
            RBucket<CommonProcessDTO<StoreReturnExecuteOrderImport>> bucket = redissonClient.getBucket(ISCM_RETURN_WAREHOUSE_IMPORT_CACHE + userDTO.getUserId());
            if (bucket.isExists()) {
                CommonProcessDTO<StoreReturnExecuteOrderImport> commonProcessDTO = bucket.get();
                if (!commonProcessDTO.getProcessFinished()) {
                    throw new BusinessErrorException("您有退仓执行的导入任务正在进行中,请完成后再继续操作");
                } else {
                    bucket.delete();
                }
            }
            DropBoxParam param = new DropBoxParam();
            param.setType(Constants.ISCM_RETURN_REASON_TYPE);// 写死
            param.setPropertyCode(Constants.ISCM_RETURN_REASON_FLAG);
            CommonListResponse<OptionDto> commonRes = Optional.ofNullable(scibFeignClient.dropBox(param).getBody()).orElseThrow(() -> new BusinessErrorException("查询退仓原因失败,请联系管理员"));
            Map<String, String> reasonMap = Optional.ofNullable(commonRes.getData()).orElseThrow(() -> new BusinessErrorException("查询退仓原因失败,请联系管理员")).stream().collect(Collectors.toMap(OptionDto::getValue, OptionDto::getLabel, (k1,k2) -> k1));
            logger.info("reasonMap:{}", reasonMap);
            List<StoreReturnExecuteOrderImport> importList = HutoolUtil.excelToList(file.getInputStream(), StoreReturnExecuteOrderImport.importExecuteMap(), StoreReturnExecuteOrderImport.class, 2);
            if (CollectionUtils.isEmpty(importList)) {
                throw new BusinessErrorException("导入文件为空");
            }
            if (importList.size() > importMax) {
                throw new BusinessErrorException("导入文件行数超过" + importMax +"行, 请删除多余行数再继续导入");
            }
            List<OrgTreeSimpleDTO> orgTreeSimpleDTOS = permissionService.listUserDataScopeTreesByOrgIdAndTypes(userDTO.getUserId(), 3L, Lists.newArrayList(OrgTypeEnum.ORG_TYPE_STORE.getCode()));
            if (CollectionUtils.isEmpty(orgTreeSimpleDTOS) || CollectionUtils.isEmpty(orgTreeSimpleDTOS.get(0).getChildren())) {
                throw new BusinessErrorException("您没有门店的操作权限");
            }
            Map<String, OrgTreeSimpleDTO> userStoreDataMap = orgTreeSimpleDTOS.get(0).getChildren().stream().filter(v -> StringUtils.isNotBlank(v.getSapcode())).collect(Collectors.toMap(OrgTreeSimpleDTO::getSapcode, Function.identity(), (k1, k2) -> k1));
            Set<String> set = new HashSet<>();
            for (StoreReturnExecuteOrderImport exec : importList) {
                if (StringUtils.isBlank(exec.getWarehouseCode())) {
                    throw new BusinessErrorException("第" + exec.getLineNum() + "行[接受仓库DC]为空");
                }
                if (StringUtils.isBlank(exec.getStoreCode())) {
                    throw new BusinessErrorException("第" + exec.getLineNum() + "行[退仓门店编码]为空");
                }
                if (!userStoreDataMap.containsKey(exec.getStoreCode().trim())) {
                    throw new BusinessErrorException("第" + exec.getLineNum() + "行没有该门店权限");
                }
                if (StringUtils.isBlank(exec.getGoodsNo())) {
                    throw new BusinessErrorException("第" + exec.getLineNum() + "行[商品编码]为空");
                }
                if (StringUtils.isBlank(exec.getBatchNo())) {
                    throw new BusinessErrorException("第" + exec.getLineNum() + "行[生产批号]为空");
                }
                if (StringUtils.isBlank(exec.getReturnQuantity())) {
                    throw new BusinessErrorException("第" + exec.getLineNum() + "行[退仓数量]为空");
                }
                if (StringUtils.isNotBlank(exec.getReturnReason()) && !reasonMap.containsKey(exec.getReturnReason().trim())) {
                    throw new BusinessErrorException("第" + exec.getLineNum() + "行[退仓原因]不存在");
                }
                try {
                    BigDecimal returnQuantity = new BigDecimal(exec.getReturnQuantity());
                    if (returnQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                        throw new BusinessErrorException("第" + exec.getLineNum() + "行[退仓数量]<0");
                    }
                } catch (Exception e) {
                    throw new BusinessErrorException("第" + exec.getLineNum() + "行[退仓数量]不是数字");
                }
                int perSize = set.size();
                set.add(exec.getStoreCode() + exec.getWarehouseCode() + exec.getGoodsNo() + exec.getBatchNo());
                if (perSize == set.size()) {
                    throw new BusinessErrorException("第" + exec.getLineNum() + "行重复");
                }
                if (StringUtils.isBlank(exec.getReturnReason())) {
                    exec.setReturnReason(Constants.ISCM_RETURN_REASON_DEFAULT);
                }
            }
            CommonProcessDTO<StoreReturnExecuteOrderImport> commonProcessDTO = new CommonProcessDTO<>();
            commonProcessDTO.setProcessCount(importList.size());
            bucket.set(commonProcessDTO, 12L, TimeUnit.HOURS);
            executor.execute(() -> {
                Date now = new Date();
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(now);
                int createdMonth = calendar.get(Calendar.YEAR) * 100 + (calendar.get(Calendar.MONTH) + 1);
                Map<String, List<StoreReturnExecuteOrderImport>> importMap = importList.stream().collect(Collectors.groupingBy(StoreReturnExecuteOrderImport::getStoreCode));
                importMap.forEach((storeCode,importDataList) -> {
                    try {
                        List<StoreReturnExecuteOrderImport> errorList = importByStore(userDTO, storeCode, importDataList, createdMonth, now, reasonMap);
                        dealError(userDTO, importDataList.size(), errorList.stream().map(v -> {
                            v.setErrorMsg(v.getErrorMsg() + ", 该门店全部行均未导入成功");
                            return v;
                        }).collect(Collectors.toList()));
                    } catch (Exception e) {
                        logger.error("门店:" + storeCode + "导入失败:", e);
                        logger.error("门店:" + storeCode + "导入失败:{}", e.getMessage());
                        dealError(userDTO, importDataList.size(), importDataList.stream().map(v -> {
                            v.setErrorMsg(e.getMessage() + ", 该门店全部行均未导入成功");
                            return v;
                        }).collect(Collectors.toList()));
                    }
                });
            });
        } catch (Exception e) {
            logger.error("导入门店退仓执行单失败", e);
            throw e;
        }
        return "";
    }

    private List<StoreReturnExecuteOrderImport> importByStore(TokenUserDTO userDTO, String storeCode, List<StoreReturnExecuteOrderImport> importDataList, int createdMonth, Date now, Map<String, String> reasonMap) {
        List<String> goodsNos = importDataList.stream().map(StoreReturnExecuteOrderImport::getGoodsNo).distinct().map(String::trim).collect(Collectors.toList());
        OrgInfoBaseCache store;
        try {
           store = permissionService.getOrgBaseCacheBySapCode(Lists.newArrayList(storeCode), OrgTypeEnum.ORG_TYPE_STORE.getCode()).stream().findAny().orElseThrow(() -> new BusinessErrorException("门店不存在"));
        } catch (Exception e) {
            logger.error("查询门店失败:{}", storeCode);
            throw new BusinessErrorException("查询门店" + storeCode + "失败");
        }
        Map<String, SpuListVo> goodsMap = new HashMap<>();
        Map<String, List<StockGoodsBatchCodeSimpleInfo>> stockMap = new HashMap<>();
        Map<String, AuctionSpuBaseInfo> auctionSpuBaseMap = new HashMap<>();
        try {
            SpuQueryParamVo param = new SpuQueryParamVo();
            param.setGoodsNoList(goodsNos);
            goodsMap.putAll(searchApiService.getSupContentsMap(param));
        } catch (Exception e) {
            logger.error("门店:{}查询商品失败", storeCode);
            throw new BusinessErrorException("门店:" + storeCode + "查询商品失败");
        }
        try{
            StockGoodsPagableQueryParam stockParam = new StockGoodsPagableQueryParam();
            stockParam.setGoodsNos(goodsNos);
            stockParam.setBusinessId(store.getBusinessId());
            stockParam.setStockGreaterThan(BigDecimal.ZERO);
            stockParam.setStoreId(store.getOutId());
            stockMap.putAll(stockcenterService.goodsBatchCodePage(stockParam));
        } catch (Exception e) {
            logger.error("门店:{}查询商品库存失败", storeCode);
            throw new BusinessErrorException("门店:" + storeCode + "查询商品库存失败");
        }
        try{
            auctionSpuBaseMap.putAll(forestService.batchFindSpuProperty(goodsNos, store.getBusinessId()).stream().collect(Collectors.toMap(AuctionSpuBaseInfo::getGoodsNo, Function.identity(), (k1,k2) -> k1)));
        } catch (Exception e) {
            logger.error("门店:{}查询商品属性失败", storeCode);
            throw new BusinessErrorException("门店:" + storeCode + "查询商品属性失败");
        }
        // 已经存在的明细
        logger.info("开始查询已存在的明细");
        IscmStoreReturnExecuteOrderDetailExample executeOrderDetailExample = new IscmStoreReturnExecuteOrderDetailExample();
        executeOrderDetailExample.createCriteria()
                .andStoreOrgIdEqualTo(store.getId())
                .andGoodsNoIn(goodsNos)
                .andCreatedMonthEqualTo(createdMonth)
                .andReturnBusinessTypeEqualTo(ReturnWarehouseBusinessTypeEnum.IMPORT_RETURN.getCode())
                .andProcessStatusEqualTo(StoreReturnExecuteProcessStatusEnum.NON_APPROVED.getCode());
        List<IscmStoreReturnExecuteOrderDetail> executeOrderDetailExists = iscmStoreReturnExecuteOrderDetailMapper.selectByExample(executeOrderDetailExample);
        Map<String, String> batchNoUniqueKeyExists = executeOrderDetailExists.stream().collect(Collectors.toMap(this::genBatchNoUniqueKeyWithoutOrderNo, IscmStoreReturnExecuteOrderDetail::getReturnOrderNo, (k1,k2) -> k1));
        logger.info("batchNoUniqueKeyExists:{}", JSON.toJSONString(batchNoUniqueKeyExists));
        List<StoreReturnExecuteOrderImport> errorList = new ArrayList<>();
        for (StoreReturnExecuteOrderImport data : importDataList) {
            SpuListVo spuListVo = goodsMap.get(data.getGoodsNo());
            if (null == spuListVo) {
                data.setErrorMsg("商品不存在");
                errorList.add(data);
                break;
            }
            List<StockGoodsBatchCodeSimpleInfo> batchStocks = stockMap.get(data.getGoodsNo());
            if (CollectionUtils.isEmpty(batchStocks)) {
                data.setErrorMsg("批号库存不足");
                errorList.add(data);
                break;
            }
            Map<String, List<StockGoodsBatchCodeSimpleInfo>> batchMap = batchStocks.stream().collect(Collectors.groupingBy(StockGoodsBatchCodeSimpleInfo::getBatchNo));
            List<StockGoodsBatchCodeSimpleInfo> stocks = batchMap.get(data.getBatchNo());
            if (CollectionUtils.isEmpty(stocks)) {
                data.setErrorMsg("批号库存不足");
                errorList.add(data);
                break;
            }
            BigDecimal returnQuantity = new BigDecimal(data.getReturnQuantity());
            BigDecimal useStock = stocks.stream().map(stock -> stock.getStock().subtract(stock.getWaitStock().add(stock.getUnqualifiedAreaStock())).add(stock.getTransitStock())).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (returnQuantity.compareTo(useStock) > 0) {
                data.setErrorMsg("批号可用库存:" + useStock.toPlainString() + "<退仓数量:" + data.getReturnQuantity());
                errorList.add(data);
                break;
            }
            String returnOrderNo = batchNoUniqueKeyExists.get(store.getId() + "-" + data.getWarehouseCode() + "-" + data.getGoodsNo() + "-" + data.getBatchNo());
            if (StringUtils.isNotBlank(returnOrderNo)){
                data.setErrorMsg("已在退仓确认单:" + returnOrderNo + "中");
                errorList.add(data);
                break;
            }
        }
        if (CollectionUtils.isNotEmpty(errorList)) {
//            dealError(userDTO, importDataList.size(), errorList);
            logger.info("门店:{}有错误,直接返回", storeCode);
            return errorList;
        }
        List<String> warecodes = importDataList.stream().map(v -> v.getWarehouseCode()).distinct().collect(Collectors.toList());
        List<WarehouseIscmResponse> warehouseParams = warecodes.stream().map(v -> {
            WarehouseIscmResponse warehouse = new WarehouseIscmResponse();
            warehouse.setOrgId(store.getBusinessOrgId());
            warehouse.setWarehouseCode(v);
            return warehouse;
        }).collect(Collectors.toList());
        Map<String, WarehouseIscmResponse> warehouseMap = purchaseService.getBatchWarehouseList(warehouseParams).stream().collect(Collectors.toMap(v -> v.getWarehouseCode(), Function.identity(), (k1, k2) -> k1));
        IscmStoreReturnExecuteOrderMainExample orderMainExample = new IscmStoreReturnExecuteOrderMainExample();
        orderMainExample.createCriteria()
                .andStoreOrgIdEqualTo(store.getId())
                .andWarehouseCodeIn(importDataList.stream().map(StoreReturnExecuteOrderImport::getWarehouseCode).map(String::trim).distinct().collect(Collectors.toList()))
                .andCreatedMonthEqualTo(createdMonth)
                .andProcessStatusEqualTo(StoreReturnExecuteProcessStatusEnum.NON_APPROVED.getCode())
                .andProcessStatusAllEqualTo("|0|")
                .andReturnTypeEqualTo(ReturnTypeEnum.IMPORT.getType());

        Map<String, IscmStoreReturnExecuteOrderMain> storeOrderExistsMap = iscmStoreReturnExecuteOrderMainMapper.selectByExample(orderMainExample).stream().collect(Collectors.toMap(v -> v.getStoreCode() + "-" + v.getWarehouseCode(), Function.identity(), (k1,k2) -> k1));
        Map<String, String> orderNoMap = new HashMap<>();
        List<IscmStoreReturnExecuteOrderDetail> executeOrderDetails = importDataList.stream().map(v -> {
            IscmStoreReturnExecuteOrderDetail executeOrderDetail = new IscmStoreReturnExecuteOrderDetail();
            IscmStoreReturnExecuteOrderMain order = storeOrderExistsMap.get(v.getStoreCode() + "-" + v.getWarehouseCode());
            String orderNo = orderNoMap.get(v.getWarehouseCode() + "-" + v.getStoreCode());
            if (StringUtils.isBlank(orderNo)) {
                orderNo = null != order ? order.getReturnOrderNo() : genOrderNo(v.getWarehouseCode(), v.getStoreCode());
                orderNoMap.put(v.getWarehouseCode() + "-" + v.getStoreCode(), orderNo);
            }
            executeOrderDetail.setReturnOrderNo(orderNo);
            executeOrderDetail.setReturnBusinessType(ReturnWarehouseBusinessTypeEnum.IMPORT_RETURN.getCode());
            executeOrderDetail.setPlatformOrgId(store.getPlatformOrgId());
            executeOrderDetail.setPlatformOrgName(store.getPlatformShortName());
            executeOrderDetail.setCompanyOrgId(store.getBusinessOrgId());
            executeOrderDetail.setCompanyCode(store.getBusinessSapCode());
            executeOrderDetail.setCompanyName(store.getBusinessShortName());
            executeOrderDetail.setStoreOrgId(store.getId());
            executeOrderDetail.setStoreCode(store.getSapCode());
            executeOrderDetail.setStoreName(store.getShortName());
            executeOrderDetail.setWarehouseCode(v.getWarehouseCode());
            WarehouseIscmResponse warehouse = warehouseMap.get(v.getWarehouseCode());
            executeOrderDetail.setWarehouseName(null != warehouse ? warehouse.getWarehouseDesc() : "");
            SpuListVo spuListVo = goodsMap.get(v.getGoodsNo());
            executeOrderDetail.setGoodsNo(v.getGoodsNo());
            executeOrderDetail.setGoodsName(spuListVo.getName());
            executeOrderDetail.setBarCode(spuListVo.getBarCode());
            executeOrderDetail.setGoodsCommonName(spuListVo.getCurName());
            executeOrderDetail.setGoodsUnit(spuListVo.getUnit());
            executeOrderDetail.setDescription(spuListVo.getDescription());
            executeOrderDetail.setSpecifications(spuListVo.getJhiSpecification());
            executeOrderDetail.setDosageForm(spuListVo.getDosageForms());
            executeOrderDetail.setManufacturer(spuListVo.getProducter());
            executeOrderDetail.setApprovalNumber(spuListVo.getApprdocno());
            executeOrderDetail.setGoodsClassId(Long.valueOf(spuListVo.getCategoryId()));
            executeOrderDetail.setGoodsClassName("");
            executeOrderDetail.setForecastSales(BigDecimal.ZERO);
            Map<String, List<StockGoodsBatchCodeSimpleInfo>> batchMap = stockMap.get(v.getGoodsNo()).stream().collect(Collectors.groupingBy(StockGoodsBatchCodeSimpleInfo::getBatchNo));
            List<StockGoodsBatchCodeSimpleInfo> stocks = batchMap.get(v.getBatchNo());
            executeOrderDetail.setBatchNo(stocks.get(0).getBatchNo());
            executeOrderDetail.setValidityDate(stocks.get(0).getExpireDate());
            executeOrderDetail.setProduceDate(stocks.get(0).getProduceDate());
            executeOrderDetail.setStockLowerLimitDays(0);
            executeOrderDetail.setStockUpperLimitDays(0);
            executeOrderDetail.setHdSynthesizeAverageDailySales(BigDecimal.ZERO);
            executeOrderDetail.setStockUpperLimit(BigDecimal.ZERO);
            executeOrderDetail.setStockLowerLimit(BigDecimal.ZERO);
            executeOrderDetail.setBdpSynthesizeAverageDailySales(BigDecimal.ZERO);
            executeOrderDetail.setStorageDays(0);
            executeOrderDetail.setShouldReturnQuantity(new BigDecimal(v.getReturnQuantity()));
            BigDecimal goodsUseStock = stockMap.get(v.getGoodsNo()).stream().map(g -> g.getStock().subtract(g.getWaitStock().add(g.getUnqualifiedAreaStock())).add(g.getTransitStock())).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal useStock = stocks.stream().map(stock -> stock.getStock().subtract(stock.getWaitStock().add(stock.getUnqualifiedAreaStock())).add(stock.getTransitStock())).reduce(BigDecimal.ZERO, BigDecimal::add);
            executeOrderDetail.setGoodsStock(goodsUseStock);
            executeOrderDetail.setBatchStock(useStock);
            executeOrderDetail.setThirtySalesCount(0);
            executeOrderDetail.setValidityDays(null == stocks.get(0).getExpireDate() ? 0 : Math.abs(DateUtils.getDifferDays(stocks.get(0).getExpireDate(), new Date())));
            executeOrderDetail.setThirtySalesQuantity(BigDecimal.ZERO);
            executeOrderDetail.setMinDisplayQuantity(BigDecimal.ZERO);
            executeOrderDetail.setNonSalesDays(0);
            executeOrderDetail.setGoodsline("");
            executeOrderDetail.setPushlevel("");
            executeOrderDetail.setForbidDistribute("");
            executeOrderDetail.setForbidReturnWarehouse("");
            executeOrderDetail.setForbidApply("");
            executeOrderDetail.setForbidAllot("");
            executeOrderDetail.setCreatedMonth(createdMonth);
            executeOrderDetail.setCreatedBy(userDTO.getUserId());
            executeOrderDetail.setCreatedName(userDTO.getName());
            executeOrderDetail.setUpdatedBy(userDTO.getUserId());
            executeOrderDetail.setUpdatedName(userDTO.getName());
            executeOrderDetail.setProcessStatus(StoreReturnExecuteProcessStatusEnum.NON_APPROVED.getCode());
            executeOrderDetail.setReturnQuantity(new BigDecimal(v.getReturnQuantity()));
            executeOrderDetail.setRealReturnQuantity(new BigDecimal(v.getReturnQuantity()));
            executeOrderDetail.setIssueReturnQuantity(new BigDecimal(v.getReturnQuantity()));
            executeOrderDetail.setIssueReturnAmount(BigDecimal.ZERO);
            executeOrderDetail.setPosReturnOrderNo("");
            executeOrderDetail.setRowNo("");
            executeOrderDetail.setGmtCreate(now);
            executeOrderDetail.setGmtUpdate(now);
            executeOrderDetail.setReturnReasonType(v.getReturnReason());
            executeOrderDetail.setReturnReason(reasonMap.get(v.getReturnReason()));

            AuctionSpuBaseInfo auctionSpuBaseInfo = auctionSpuBaseMap.get(v.getGoodsNo());
            if (null != auctionSpuBaseInfo) {
                executeOrderDetail.setGoodsPurChannel(auctionSpuBaseInfo.getPurchchannel());
                executeOrderDetail.setPurchaseGroup(auctionSpuBaseInfo.getEkgrp());
                executeOrderDetail.setPurchaseOrgCode(auctionSpuBaseInfo.getOrderorg());
                executeOrderDetail.setPurchaseOrgName("");
            }
            return executeOrderDetail;
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(executeOrderDetails)) {
            return new ArrayList<>();
        }
        Lists.partition(executeOrderDetails, Constants.BATCH_INSERT_ONCE_MAX_VALUE).forEach(v -> iscmStoreReturnExecuteOrderDetailExtendMapper.batchInsert(v));
        Map<String, List<IscmStoreReturnExecuteOrderDetail>> detailMap = executeOrderDetails.stream().collect(Collectors.groupingBy(v -> v.getStoreCode() + "-" + v.getWarehouseCode()));
        List<IscmStoreReturnExecuteOrderMain> batchInsert = new ArrayList<>();
        List<IscmStoreReturnExecuteOrderMain> batchUpdate = new ArrayList<>();
        detailMap.forEach((k,v) -> {
            IscmStoreReturnExecuteOrderMain order = storeOrderExistsMap.get(k);
            StoreReturnExecuteOrderSum executeOrderSum = new StoreReturnExecuteOrderSum();
            List<StoreReturnExecuteOrderQty> orderQtyList = new ArrayList<>();
            BeanUtils.copyList(v, orderQtyList, StoreReturnExecuteOrderQty.class);
            executeOrderSum.sum(orderQtyList, false);
            if (null == order) {
                IscmStoreReturnExecuteOrderDetail executeOrderDetail = v.get(0);
                IscmStoreReturnExecuteOrderMain executeOrder = new IscmStoreReturnExecuteOrderMain();
                BeanUtils.copyProperties(executeOrderDetail, executeOrder);
                BeanUtils.copyProperties(executeOrderSum, executeOrder);
                executeOrder.setReturnOrderNo(executeOrderDetail.getReturnOrderNo());
                executeOrder.setReturnType(ReturnTypeEnum.IMPORT.getType());
                executeOrder.setWarehouseCode(executeOrderDetail.getWarehouseCode());
                executeOrder.setWarehouseName(executeOrderDetail.getWarehouseName());
                executeOrder.setPosReturnOrderNo("");
                executeOrder.setCreatedBy(userDTO.getUserId());
                executeOrder.setCreatedName(userDTO.getName());
                executeOrder.setUpdatedBy(userDTO.getUserId());
                executeOrder.setUpdatedName(userDTO.getName());
                executeOrder.setCreatedMonth(createdMonth);
                executeOrder.setProcessStatus(StoreReturnExecuteProcessStatusEnum.NON_APPROVED.getCode());
                executeOrder.setProcessStatusAll("|" + StoreReturnExecuteProcessStatusEnum.NON_APPROVED.getCode() + "|");
                executeOrder.setGmtCreate(now);
                executeOrder.setGmtUpdate(now);
                executeOrder.setIssueValidityDays(0);
                batchInsert.add(executeOrder);
            } else {
                batchUpdate.add(sum(executeOrderSum, order));
            }
        });
        if (CollectionUtils.isNotEmpty(batchInsert)) {
            iscmStoreReturnExecuteOrderExtendMainMapper.batchInsert(batchInsert);
        }
        if (CollectionUtils.isNotEmpty(batchUpdate)) {
            RLock lock = redissonClient.getLock("UPDATE-RETURN-EXECUTE-LOCK-" + batchUpdate.get(0).getPlatformOrgId().toString());
            lock.lock();
            try {
                logger.info("更新开始...");
                iscmStoreReturnExecuteOrderExtendMainMapper.batchUpdate(batchUpdate, createdMonth);
            } catch (DataAccessException e) {
                final Throwable cause = e.getCause();
                if (cause instanceof MySQLIntegrityConstraintViolationException) {
                    throw new BusinessErrorException(cause.getMessage());
                }
            } finally {
                lock.unlock();
            }
        }
        return new ArrayList<>();
    }


    @Override
    public void consumDefectiveReturnData(ReturnWarehouseMsg msg) {
        try {
            logger.info("开始处理不良品退仓数据:{}", msg.getBusinessKeys());
            logger.info("开始处理不良品退仓数据ReturnWarehouseMsg:{}", JSON.toJSONString(msg));
            Map<String, String> reasonMap = new HashMap<>();
            try {
                DropBoxParam dropParam = new DropBoxParam();
                dropParam.setType(Constants.ISCM_RETURN_REASON_TYPE);// 写死
                dropParam.setPropertyCode(Constants.ISCM_RETURN_REASON_FLAG);
                CommonListResponse<OptionDto> commonRes = Optional.ofNullable(scibFeignClient.dropBox(dropParam).getBody()).orElseThrow(() -> new BusinessErrorException("查询退仓原因失败,请联系管理员"));
                reasonMap.putAll(Optional.ofNullable(commonRes.getData()).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(OptionDto::getValue, OptionDto::getLabel, (k1, k2) -> k1)));
                logger.info("reasonMap:{}", JSON.toJSONString(reasonMap));
            } catch (Exception e) {
                logger.error("查询退仓原因失败", e);
            }
            if (!reasonMap.containsKey(Constants.ISCM_DEFECTIVE_RETURN_REASON_DEFAULT)) {
                reasonMap.put(Constants.ISCM_DEFECTIVE_RETURN_REASON_DEFAULT, Constants.ISCM_DEFECTIVE_RETURN_REASON_NAME_DEFAULT);
            }
            Date now = new Date();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(now);
            int createdMonth = calendar.get(Calendar.YEAR) * 100 + (calendar.get(Calendar.MONTH) + 1);
            msg.getBusinessKeys().forEach(v -> {
                String[] split = v.split("-");
                List<DefectiveGoodsProcessInfo> processInfos = new ArrayList<>();
                for (int i=0;;i++) {
                    ProcessDataQueryParam param = new ProcessDataQueryParam();
                    param.setProcessKey(msg.getProcessKey());
                    param.setStoreCode(split[0]);
                    param.setWarehouseCode(split[1]);
                    param.setPage(i+1);
                    param.setPerPage(Constants.FEIGN_SERVICE_ONCE_MAX_VALUE);
                    PageResult<DefectiveGoodsProcessInfo> pageResult = supplychainPlusFeignClient.getPerDataProcessList(param);
                    processInfos.addAll(pageResult.getRows());
                    if (param.getPage()*param.getPerPage()>=pageResult.getTotal() || org.apache.commons.collections.CollectionUtils.isEmpty(pageResult.getRows())) {
                        logger.info("门店仓库:{}处理完毕", v);
                        break;
                    }
                }
                if (CollectionUtils.isEmpty(processInfos)) {
                    return;
                }
                String orderNo = genOrderNo(split[1], split[0]);
                List<IscmStoreReturnExecuteOrderDetail> executeOrderDetails = new ArrayList<>();
                processInfos.stream().collect(Collectors.groupingBy(p ->p.getWarehouseCode() + "-" + p.getStoreCode() + "-" + p.getGoodsNo() + "-" + p.getBatchNo())).forEach((key, list) -> {
                    DefectiveGoodsProcessInfo process = list.get(0);
                    IscmStoreReturnExecuteOrderDetail executeOrderDetail = new IscmStoreReturnExecuteOrderDetail();
                    BeanUtils.copyProperties(process, executeOrderDetail);
                    executeOrderDetail.setReturnOrderNo(orderNo);
                    executeOrderDetail.setReturnBusinessType(ReturnWarehouseBusinessTypeEnum.DEFECTIVE_RETURN.getCode());
                    executeOrderDetail.setProcessStatus(StoreReturnExecuteProcessStatusEnum.NON_APPROVED.getCode());
                    executeOrderDetail.setThirtySalesQuantity(BigDecimal.ZERO);
                    executeOrderDetail.setThirtySalesCount(0);
                    executeOrderDetail.setThirtySalesQuantity(BigDecimal.ZERO);
                    executeOrderDetail.setNonSalesDays(0);
                    executeOrderDetail.setShouldReturnQuantity(list.stream().map(DefectiveGoodsProcessInfo::getReturnQuantity).reduce(BigDecimal.ZERO, BigDecimal::add));
                    executeOrderDetail.setReturnQuantity(executeOrderDetail.getShouldReturnQuantity());
                    executeOrderDetail.setRealReturnQuantity(executeOrderDetail.getShouldReturnQuantity());
                    executeOrderDetail.setIssueReturnQuantity(executeOrderDetail.getShouldReturnQuantity());
                    executeOrderDetail.setIssueReturnAmount(list.stream().map(DefectiveGoodsProcessInfo::getCostAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                    executeOrderDetail.setRegisterQuantity(executeOrderDetail.getShouldReturnQuantity());
                    executeOrderDetail.setPosReturnOrderNo("");
                    executeOrderDetail.setRowNo("");
                    executeOrderDetail.setReturnReasonType(Constants.ISCM_DEFECTIVE_RETURN_REASON_DEFAULT);
                    executeOrderDetail.setReturnReason(reasonMap.get(Constants.ISCM_DEFECTIVE_RETURN_REASON_DEFAULT));
                    executeOrderDetail.setCreatedMonth(createdMonth);
                    executeOrderDetail.setForecastSales(BigDecimal.ZERO);
                    executeOrderDetail.setStockUpperLimitDays(0);
                    executeOrderDetail.setStockLowerLimitDays(0);
                    executeOrderDetail.setHdSynthesizeAverageDailySales(BigDecimal.ZERO);
                    executeOrderDetail.setStorageDays(0);
                    executeOrderDetail.setNonSalesDays(0);
                    executeOrderDetail.setThirtySalesQuantity(BigDecimal.ZERO);
                    executeOrderDetail.setThirtySalesCount(0);
                    executeOrderDetail.setIssueValidityDays(0);
                    executeOrderDetail.setValidityDate(null == process.getValidityDate() ? null : Date.from(process.getValidityDate().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
                    executeOrderDetail.setProduceDate(null == process.getProduceDate() ? null : Date.from(process.getProduceDate().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
                    executeOrderDetails.add(executeOrderDetail);
                });
//                List<IscmStoreReturnExecuteOrderDetail> executeOrderDetails = processInfos.stream().map(process -> {
//                    IscmStoreReturnExecuteOrderDetail executeOrderDetail = new IscmStoreReturnExecuteOrderDetail();
//                    BeanUtils.copyProperties(process, executeOrderDetail);
//                    executeOrderDetail.setReturnOrderNo(orderNo);
//                    executeOrderDetail.setReturnBusinessType(ReturnWarehouseBusinessTypeEnum.DEFECTIVE_RETURN.getCode());
//                    executeOrderDetail.setProcessStatus(StoreReturnExecuteProcessStatusEnum.NON_APPROVED.getCode());
//                    executeOrderDetail.setThirtySalesQuantity(BigDecimal.ZERO);
//                    executeOrderDetail.setThirtySalesCount(0);
//                    executeOrderDetail.setThirtySalesQuantity(BigDecimal.ZERO);
//                    executeOrderDetail.setNonSalesDays(0);
//                    executeOrderDetail.setShouldReturnQuantity(process.getReturnQuantity());
//                    executeOrderDetail.setReturnQuantity(process.getReturnQuantity());
//                    executeOrderDetail.setRealReturnQuantity(process.getReturnQuantity());
//                    executeOrderDetail.setIssueReturnQuantity(process.getReturnQuantity());
//                    executeOrderDetail.setIssueReturnAmount(process.getCostAmount());
//                    executeOrderDetail.setRegisterQuantity(process.getReturnQuantity());
//                    executeOrderDetail.setPosReturnOrderNo("");
//                    executeOrderDetail.setRowNo("");
//                    executeOrderDetail.setReturnReasonType("12");
//                    executeOrderDetail.setReturnReason("近效期退仓");
//                    executeOrderDetail.setCreatedMonth(createdMonth);
//                    executeOrderDetail.setForecastSales(BigDecimal.ZERO);
//                    executeOrderDetail.setStockUpperLimitDays(0);
//                    executeOrderDetail.setStockLowerLimitDays(0);
//                    executeOrderDetail.setHdSynthesizeAverageDailySales(BigDecimal.ZERO);
//                    executeOrderDetail.setStorageDays(0);
//                    executeOrderDetail.setNonSalesDays(0);
//                    executeOrderDetail.setThirtySalesQuantity(BigDecimal.ZERO);
//                    executeOrderDetail.setThirtySalesCount(0);
//                    executeOrderDetail.setIssueValidityDays(0);
//                    executeOrderDetail.setValidityDate(null == process.getValidityDate() ? null : Date.from(process.getValidityDate().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
//                    executeOrderDetail.setProduceDate(null == process.getProduceDate() ? null : Date.from(process.getProduceDate().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
//                    return executeOrderDetail;
//                }).collect(Collectors.toList());
                Lists.partition(executeOrderDetails, Constants.BATCH_INSERT_ONCE_MAX_VALUE).forEach(details -> iscmStoreReturnExecuteOrderDetailExtendMapper.batchInsert(details));
                IscmStoreReturnExecuteOrderDetail executeOrderDetail = executeOrderDetails.get(0);
                StoreReturnExecuteOrderSum executeOrderSum = new StoreReturnExecuteOrderSum();
                List<StoreReturnExecuteOrderQty> orderQtyList = new ArrayList<>();
                BeanUtils.copyList(executeOrderDetails, orderQtyList, StoreReturnExecuteOrderQty.class);
//                logger.info("orderQtyList:{}", JSON.toJSONString(orderQtyList));
                executeOrderSum.sumNoRegister(orderQtyList, false);
//                logger.info("executeOrderSum:{}", JSON.toJSONString(executeOrderSum));
                IscmStoreReturnExecuteOrderMain executeOrder = new IscmStoreReturnExecuteOrderMain();
                BeanUtils.copyProperties(executeOrderDetail, executeOrder);
                BeanUtils.copyProperties(executeOrderSum, executeOrder);
                executeOrder.setReturnCostAmountTotal(executeOrderSum.getReturnCostAmountTotal());
                executeOrder.setIssueReturnAmountTotal(executeOrderSum.getIssueReturnAmountTotal());
                executeOrder.setReturnType(ReturnTypeEnum.DEFECTIVE.getType());
                executeOrder.setReturnOrderNo(executeOrderDetail.getReturnOrderNo());
                executeOrder.setWarehouseCode(executeOrderDetail.getWarehouseCode());
                executeOrder.setWarehouseName(executeOrderDetail.getWarehouseName());
                executeOrder.setPosReturnOrderNo("");
                executeOrder.setCreatedMonth(createdMonth);
                executeOrder.setProcessStatus(StoreReturnExecuteProcessStatusEnum.NON_APPROVED.getCode());
                executeOrder.setProcessStatusAll("|" + StoreReturnExecuteProcessStatusEnum.NON_APPROVED.getCode() + "|");
                executeOrder.setGmtCreate(now);
                executeOrder.setGmtUpdate(now);
                executeOrder.setIssueValidityDays(0);
                iscmStoreReturnExecuteOrderMainMapper.insertSelective(executeOrder);
            });
        } catch (Exception e) {
            logger.error("消费失败", e);
            throw e;
        } finally {
            supplychainPlusFeignClient.deletePerDataProcess(msg.getProcessKey());
        }
    }

    @Override
    public void genNoticeDzMsg() {
        try {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());
            int thisMonth = calendar.get(Calendar.YEAR) * 100 + (calendar.get(Calendar.MONTH) + 1);
            calendar.add(Calendar.MONTH,-1);
            int lastMonth = calendar.get(Calendar.YEAR) * 100 + (calendar.get(Calendar.MONTH) + 1);
            List<Long> noticeOrgIds = iscmStoreReturnExecuteOrderDetailExtendMapper.selectNoticeStoreOrgIds(thisMonth, DateUtils.dealDateTimeStart(new Date()), DateUtils.dealDateTimeEnd(new Date()));
            noticeOrgIds.addAll(iscmStoreReturnExecuteOrderDetailExtendMapper.selectNoticeStoreOrgIds(lastMonth, DateUtils.dealDateTimeStart(new Date()), DateUtils.dealDateTimeEnd(new Date())));
            noticeOrgIds.forEach(v -> {
                try {
                    List<EmployeeDetailWithWxDTO> employees = permissionService.getSalesclerkMastersByStoreId(v);
                    String wxUsers = employees.stream().map(EmployeeDetailWithWxDTO::getWxId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining("|"));
                    StringBuilder msg = new StringBuilder("系统已生成退仓申请单，请及时处理，并在下一个配货日将实物带回仓库。");
                    List<IscmStoreReturnExecuteOrderDetail> details = iscmStoreReturnExecuteOrderDetailExtendMapper.selectNoticeInfos(thisMonth, DateUtils.dealDateTimeStart(new Date()), DateUtils.dealDateTimeEnd(new Date()), v);
                    details.addAll(iscmStoreReturnExecuteOrderDetailExtendMapper.selectNoticeInfos(lastMonth, DateUtils.dealDateTimeStart(new Date()), DateUtils.dealDateTimeEnd(new Date()), v));
                    if(CollectionUtils.isEmpty(details)) {
                        logger.info("企业:{}没有需要提醒的单据", v);
                        return;
                    }
                    Map<String, IscmStoreReturnExecuteOrderDetail> noticeDetails = details.stream().filter(detail -> null == detail.getHandleQuantity()).collect(Collectors.toMap(d -> d.getGoodsNo()+ "-" + d.getBatchNo(), Function.identity(), (k1,k2) -> k1));
                    if (MapUtils.isNotEmpty(noticeDetails)) {
                        msg.append("其中以下商品批号可全部退仓：\n");
                        noticeDetails.values().forEach(detail -> {
                            msg.append("\t\t商品编码：").append(detail.getGoodsNo()).append("，批号：").append(detail.getBatchNo()).append("\n");
                        });
                        msg.append("如系统下发的退仓申请单中的数量比实物库存数量少，可将这些商品批号手工创建退仓申请单一并退回仓库。");
                    }
//                    logger.info("noticeMsg:{}", msg.toString());
                    if (StringUtils.isBlank(wxUsers)) {
                        logger.error("门店:{}没有需要通知的店长",v);
                        return;
                    }
                    AlertContent alert = new AlertContent();
                    alert.setType("QYWX");
                    alert.setFlag(true);
                    alert.setToUsers(wxUsers);
                    alert.setSubject("不良品退仓提醒");
                    alert.setMessage(msg.toString());
                    alertService.alert(alert);
                } catch (Exception e) {
                    logger.error("门店:{}通知店长失败",v, e);
                }
            });
        } catch (Exception e) {
            logger.error("生成通知店长消息失败");
        }
    }

    private void dealError(TokenUserDTO userDTO, int partProcessCount, List<StoreReturnExecuteOrderImport> errorList) {
        RBucket<CommonProcessDTO<StoreReturnExecuteOrderImport>> bucket = redissonClient.getBucket(ISCM_RETURN_WAREHOUSE_IMPORT_CACHE + userDTO.getUserId());
        CommonProcessDTO<StoreReturnExecuteOrderImport> commonProcessDTO = bucket.get();
        commonProcessDTO.setErrorCount(commonProcessDTO.getErrorCount() + errorList.size());
        commonProcessDTO.setPassCount(commonProcessDTO.getPassCount() + partProcessCount - errorList.size());
        List<StoreReturnExecuteOrderImport> error = Optional.ofNullable(commonProcessDTO.getErrorList()).orElse(new ArrayList<>());
        error.addAll(errorList);
        commonProcessDTO.setErrorList(error);
        commonProcessDTO.setProcessFinished(commonProcessDTO.getProcessCount().equals(commonProcessDTO.getPassCount() + commonProcessDTO.getErrorCount()));
        bucket.set(commonProcessDTO, 2L, TimeUnit.HOURS);
    }
    @Override
    public CommonProcessDTO<StoreReturnExecuteOrderImport> getImportExecProcess(TokenUserDTO userDTO, Integer bizType, HttpServletResponse response) throws Exception {
        RBucket<CommonProcessDTO<StoreReturnExecuteOrderImport>> bucket = redissonClient.getBucket(ISCM_RETURN_WAREHOUSE_IMPORT_CACHE + userDTO.getUserId());
        if (bucket.isExists()) {
            CommonProcessDTO<StoreReturnExecuteOrderImport> commonProcessDTO = bucket.get();
            if (commonProcessDTO.getProcessFinished()) {
                if (bizType.equals(2) && CollectionUtils.isNotEmpty(commonProcessDTO.getErrorList())) {
                    String fileName = "退仓执行导入错误文件" + DateUtils.conventDateStrByDate(new Date(), DateUtils.DATETIME_PATTERN_COMPACT);
                    fileName = URLEncoder.encode(fileName, "UTF-8");
                    List<ListToExcelMultiSheetDTO> listDto = new ArrayList<>();
                    ListToExcelMultiSheetDTO<StoreReturnExecuteOrderImport> dto = new ListToExcelMultiSheetDTO();
                    dto.setSheetName("sheet0");
                    dto.setFieldMap(StoreReturnExecuteOrderImport.exportExecuteMap());
                    dto.setListGroup(commonProcessDTO.getErrorList());
                    listDto.add(dto);
                    HutoolUtil.listToExcelMultiSheet(fileName, listDto, response);
                }
            }
            return commonProcessDTO;
        }
        CommonProcessDTO<StoreReturnExecuteOrderImport> commonProcessDTO = new CommonProcessDTO();
        commonProcessDTO.setProcessFinished(true);
        return commonProcessDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(TokenUserDTO userDTO, DelExecParam param) throws Exception {
        try {
            if (CollectionUtils.isEmpty(param.getIds())) {
                throw new BusinessErrorException("请选择需要删除的单据");
            }
            if (null == param.getMonth()) {
                throw new BusinessErrorException("请选择日期");
            }
            IscmStoreReturnExecuteOrderMainExample example = new IscmStoreReturnExecuteOrderMainExample();
            example.createCriteria().andCreatedMonthEqualTo(param.getMonth()).andIdIn(param.getIds());
            List<IscmStoreReturnExecuteOrderMain> orderList = iscmStoreReturnExecuteOrderMainMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(orderList)) {
                throw new BusinessErrorException("请选择需要删除的单据");
            }
            IscmStoreReturnExecuteOrderDetailExample detailExample = new IscmStoreReturnExecuteOrderDetailExample();
            detailExample.createCriteria().andCreatedMonthEqualTo(param.getMonth()).andReturnOrderNoIn(orderList.stream().map(IscmStoreReturnExecuteOrderMain::getReturnOrderNo).collect(Collectors.toList())).andProcessStatusIn(Arrays.stream(StoreReturnExecuteProcessStatusEnum.ENABLE_DELETE_STATUS).collect(Collectors.toList()));
            List<IscmStoreReturnExecuteOrderDetail> details = iscmStoreReturnExecuteOrderDetailMapper.selectByExample(detailExample);
            if (CollectionUtils.isEmpty(details)) {
                throw new BusinessErrorException("删除失败,没找到需要删除的记录");
            }
            Map<String, List<IscmStoreReturnExecuteOrderDetail>> detailMap = details.stream().collect(Collectors.groupingBy(IscmStoreReturnExecuteOrderDetail::getReturnOrderNo));
            for (Map.Entry<String, List<IscmStoreReturnExecuteOrderDetail>> entry : detailMap.entrySet()){
                ReturnWarehouseExecuteIdBatchParam deleteParam = new ReturnWarehouseExecuteIdBatchParam();
                deleteParam.setMonth(param.getMonth());
                deleteParam.setIds(entry.getValue().stream().map(IscmStoreReturnExecuteOrderDetail::getId).collect(Collectors.toList()));
//            deleteParam.setStoreOrgIds(details.stream().map(IscmStoreReturnExecuteOrderDetail::getStoreOrgId).distinct().collect(Collectors.toList()));
                returnWarehouseExecuteService.deleteExecuteDetail(userDTO, deleteParam);
            }
        } catch (Exception e) {
            logger.error("删除退仓执行单失败", e);
            throw e;
        }
    }

    /**
     * 构建主单查询条件(门店orgId && 未下发)
     *
     * @param storeOrgId
     * @param warehouseCode
     * @param createdMonth
     * @return
     */
    private IscmStoreReturnExecuteOrderMainExample newOrderMainExample(Long storeOrgId, String warehouseCode, int createdMonth, int returnType) {
        IscmStoreReturnExecuteOrderMainExample orderMainExample = new IscmStoreReturnExecuteOrderMainExample();
        orderMainExample.createCriteria()
                .andStoreOrgIdEqualTo(storeOrgId)
                .andWarehouseCodeEqualTo(warehouseCode)
                .andCreatedMonthEqualTo(createdMonth)
                .andProcessStatusEqualTo(StoreReturnExecuteProcessStatusEnum.NON_APPROVED.getCode())
                .andReturnTypeEqualTo(returnType);
        return orderMainExample;
    }

    /**
     * 明细批号唯一key(store_org_id,warehouse_code,return_order_no,goods_no,batch_no)
     *
     * @param detail
     * @return
     */
    private String genBatchNoUniqueKey(IscmStoreReturnExecuteOrderDetail detail) {
        return detail.getStoreOrgId() + "-" + detail.getWarehouseCode() + "-" + detail.getReturnOrderNo() + "-" + detail.getGoodsNo() + "-" + detail.getBatchNo();
    }

    private String genBatchNoUniqueKeyWithoutOrderNo(IscmStoreReturnExecuteOrderDetail detail) {
        return detail.getStoreOrgId() + "-" + detail.getWarehouseCode() + "-" + detail.getGoodsNo() + "-" + detail.getBatchNo();
    }

    /**
     * 下发退仓金额 = 登记时成本金额/登记数量 * 下发退仓数量
     *
     * @param costAmount
     * @param registerQuantity
     * @param issueReturnQuantity
     * @return
     */
    private BigDecimal genIssueReturnAmount(BigDecimal costAmount, BigDecimal registerQuantity, BigDecimal issueReturnQuantity) {
        if (null == costAmount || null == registerQuantity || null == issueReturnQuantity) {
            return BigDecimal.ZERO;
        }
        return costAmount.divide(registerQuantity, 4, RoundingMode.HALF_UP).multiply(issueReturnQuantity);
    }

    /**
     * 合计(和已有的合计)
     *
     * @param executeOrderSum
     * @param executeOrderExist
     * @return
     */
//    private IscmStoreReturnExecuteOrderMain sum(StoreReturnExecuteOrderSum executeOrderSum, IscmStoreReturnExecuteOrderMain executeOrderExist) {
//        IscmStoreReturnExecuteOrderMain sum = new IscmStoreReturnExecuteOrderMain();
//        sum.setReturnGoodsQuantityTotal(executeOrderSum.getReturnGoodsQuantityTotal() + executeOrderExist.getReturnGoodsQuantityTotal());
//        sum.setReturnQuantityTotal(executeOrderSum.getReturnQuantityTotal().add(executeOrderExist.getReturnQuantityTotal()));
//        sum.setReturnCostAmountTotal(executeOrderSum.getReturnCostAmountTotal().add(executeOrderExist.getReturnCostAmountTotal()));
//        sum.setIssueReturnAmountTotal(executeOrderSum.getIssueReturnAmountTotal().add(executeOrderExist.getIssueReturnAmountTotal()));
//        sum.setIssueReturnQuantityTotal(executeOrderSum.getIssueReturnQuantityTotal().add(executeOrderExist.getIssueReturnQuantityTotal()));
//        sum.setRealReturnQuantityTotal(executeOrderSum.getRealReturnQuantityTotal().add(executeOrderExist.getRealReturnQuantityTotal()));
//        return sum;
//    }

    private IscmStoreReturnExecuteOrderMain sum(StoreReturnExecuteOrderSum executeOrderSum, IscmStoreReturnExecuteOrderMain executeOrderExist) {
        executeOrderExist.setReturnGoodsQuantityTotal(executeOrderSum.getReturnGoodsQuantityTotal() + executeOrderExist.getReturnGoodsQuantityTotal());
        executeOrderExist.setReturnQuantityTotal(executeOrderSum.getReturnQuantityTotal().add(executeOrderExist.getReturnQuantityTotal()));
        executeOrderExist.setReturnCostAmountTotal(executeOrderSum.getReturnCostAmountTotal().add(executeOrderExist.getReturnCostAmountTotal()));
        executeOrderExist.setIssueReturnAmountTotal(executeOrderSum.getIssueReturnAmountTotal().add(executeOrderExist.getIssueReturnAmountTotal()));
        executeOrderExist.setIssueReturnQuantityTotal(executeOrderSum.getIssueReturnQuantityTotal().add(executeOrderExist.getIssueReturnQuantityTotal()));
        executeOrderExist.setRealReturnQuantityTotal(executeOrderSum.getRealReturnQuantityTotal().add(executeOrderExist.getRealReturnQuantityTotal()));
        return executeOrderExist;
    }

    private BigDecimal min(BigDecimal b1, BigDecimal b2) {
        b1 = Objects.isNull(b1) ? BigDecimal.ZERO : b1;
        b2 = Objects.isNull(b2) ? BigDecimal.ZERO : b2;
        if (b1.compareTo(b2) >= 0) {
            return b2;
        }
        return b1;
    }

    /**
     * 生成退仓单号(单号规则: 退仓接收仓库编码+门店编码+yyMMdd+001(3位自增顺序号))
     *
     * @param warehouseCode
     * @param storeCode
     * @return
     */
    private String genOrderNo(String warehouseCode, String storeCode) {
        String nowStr = DateUtils.conventDateStrByPattern(new Date(), "yyMMdd");
        RBucket<String> bucket = redissonClient.getBucket(ISCM_RETURN_WAREHOUSE_ORDER_CODE_CACHE + warehouseCode + storeCode + nowStr);
        if (null == bucket.get()) {
            bucket.set("001", 1L, TimeUnit.DAYS);
        } else {
            bucket.set(String.format("%03d", Integer.parseInt(bucket.get()) + 1), 1L, TimeUnit.DAYS);
        }
        return warehouseCode + storeCode + nowStr + "-" + bucket.get();
    }

    /**
     * 从单号集合中获取单号
     *
     * @param orderNoMap
     * @param orderDetail
     * @return
     */
    private String getOrderNo(Map<String, String> orderNoMap, IscmStoreReturnExecuteOrderDetail orderDetail) {
        return orderNoMap.get(orderDetail.getStoreOrgId() + "-" + orderDetail.getStoreCode() + "-" + orderDetail.getWarehouseCode());
    }
}

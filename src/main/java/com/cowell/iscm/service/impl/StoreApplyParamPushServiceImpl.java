package com.cowell.iscm.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.iscm.config.Constants;
import com.cowell.iscm.entity.*;
import com.cowell.iscm.enums.*;
import com.cowell.iscm.mapper.IscmStoreApplyParamGoodsLevelOrgMapper;
import com.cowell.iscm.mapper.IscmStoreApplyParamGoodsUpperLimitItemOrgMapper;
import com.cowell.iscm.mapper.IscmStoreApplyParamGoodsUpperLimitOrgMapper;
import com.cowell.iscm.mapper.IscmStoreApplyParamOrgMapper;
import com.cowell.iscm.rest.errors.BusinessErrorException;
import com.cowell.iscm.service.StoreApplyParamPushService;
import com.cowell.iscm.service.StoreApplyParamService;
import com.cowell.iscm.service.dto.RequestBodyDTO;
import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.applyParam.*;
import com.cowell.iscm.service.feign.*;
import com.cowell.iscm.service.feign.dto.OrgInfoBaseCache;
import com.cowell.iscm.utils.BeanUtils;
import com.cowell.iscm.utils.DateUtils;
import com.cowell.iscm.utils.JacksonUtil;
import com.cowell.iscm.utils.MBUtils;
import com.cowell.permission.dto.OrgDTO;
import com.cowell.permission.dto.mdm.MdmStoreBaseDTO;
import com.cowell.iscm.service.feign.dto.EmployeeInfoVO;
import com.cowell.permission.vo.OrgTreeVO;
import com.cowell.permission.vo.OrgVO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by schuangxigang on 2022/7/1 10:28.
 */
@Service
public class StoreApplyParamPushServiceImpl implements StoreApplyParamPushService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Lazy
    @Autowired
    private StoreApplyParamService storeApplyParamService;
    @Autowired
    private IscmStoreApplyParamOrgMapper iscmStoreApplyParamOrgMapper;
    @Autowired
    private IscmStoreApplyParamGoodsUpperLimitOrgMapper iscmStoreApplyParamGoodsUpperLimitOrgMapper;
    @Autowired
    private IscmStoreApplyParamGoodsUpperLimitItemOrgMapper iscmStoreApplyParamGoodsUpperLimitItemOrgMapper;
    @Autowired
    private IscmStoreApplyParamGoodsLevelOrgMapper iscmStoreApplyParamGoodsLevelOrgMapper;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private ErpSaasService erpSaasService;
    @Autowired
    private TagService tagService;
    @Autowired
    private StoreService storeService;

    @Value("${iscm.apply.param.hd.pushOrgCodes:}")
    private String pushHdOrgCodes;

    @Value("${iscm.apply.param.hd.pushCodes:}")
    private String pushHdCodes;

    @Value("${iscm.apply.param.hd.code.mapping:}")
    private String hdCodeMapping;
    @Autowired
    private MBUtils mbUtils;
    @Value("${iscm.push.store.goodsUpperLimit.properties:}")
    private String sapPushProp;

    @Value("${iscm.mb.storeApplyParam.properties:}")
    private String storeApplyParamPrpo;

    @Value("${iscm.mb.storeApplyParam.properties_bak:}")
    private String storeApplyParamPrpoBak;

    @Value("${iscm.mb.pushApplyParamGoodsUpperLimitItemToHd.properties:}")
    private String pushApplyParamGoodsUpperLimitItemToHd;

    @Value("${iscm.mb.pushApplyParamGoodsUpperLimitItemToHd.properties_bak:}")
    private String pushApplyParamGoodsUpperLimitItemToHdBak;

    @Value("${iscm.mb.pushApplyParamToHd.properties:}")
    private String applyParamToHd;

    @Value("${iscm.mb.pushApplyParamToHd.properties_bak:}")
    private String applyParamToHdBak;

    @Value("${iscm.mb.hd.hot.return.properties:}")
    private String mbHotReturnProperties;

    @Value("${iscm.storeGroup.tagBizType:17}")
    private Integer tagBizType;

    private Integer orgType;// 当前操作的机构类型

    @Async("taskExecutor")
    @Override
    public void pushParams(TokenUserDTO userDTO, Long orgId, Integer orgType, Integer paramType, Integer paramScope) {
        logger.debug("请货上限进入 pushParams 方法");
        if (null == orgId || null == orgType || null == paramType) {
            logger.info("orgId|orgType|paramType must be not null.");
            return;
        }
        OrgTypeEnum orgTypeEnum = OrgTypeEnum.getEnumByCode(orgType);
        if (null == orgTypeEnum) {
            logger.info("orgType={} is illegal.", orgType);
            return;
        }
        if (!(Objects.equals(orgTypeEnum, OrgTypeEnum.ORG_TYPE_COMPANY)
                || Objects.equals(orgTypeEnum, OrgTypeEnum.ORG_TYPE_REGIONAL_PLATFORM)
                || Objects.equals(orgTypeEnum, OrgTypeEnum.ORG_TYPE_BUSINESS))) {
            logger.info("仅支持总部/平台/连锁级别的参数");
            return;
        }
        this.orgType = orgType;
        ApplyParamTypeEnum paramTypeEnum = ApplyParamTypeEnum.getEnumByCode(paramType);
        if (null == paramTypeEnum) {
            logger.info("paramType={} is illegal.", paramType);
            return;
        }
        if (StringUtils.isBlank(pushHdCodes)) {
            logger.info("未配置推送海典的请货参数编码");
            return;
        }
        List<String> pushHdCodeList = Lists.newArrayList(pushHdCodes.split(","));
        pushHdCodeList.retainAll(Lists.newArrayList(ApplyParamTypeEnum.paramTypeAndCodeMapping().get(paramTypeEnum)));
        if (CollectionUtils.isEmpty(pushHdCodeList)) {
            logger.info("未配置推送海典的请货参数编码,paramType={}", paramType);
            return;
        }

        List<OrgTreeVO> pushOrgTreeVOS;
//        Map<Long, OrgTreeVO> pushOrgTreeVOMap;
        try {
            List<OrgTreeVO> orgTreeVOS = permissionService.getUserDataScopeOrgTrees(userDTO.getUserId(), Lists.newArrayList(OrgTypeEnum.ORG_TYPE_BUSINESS.getCode()), orgType);
            OrgTreeVO orgTreeVO = orgTreeVOS.stream().filter(v -> Objects.equals(orgId, v.getId())).findAny().orElseThrow(() -> new BusinessErrorException("选择的机构不在用户数据权限范围内"));
            if (Objects.equals(orgType, OrgTypeEnum.ORG_TYPE_BUSINESS.getCode())) {
                pushOrgTreeVOS = Lists.newArrayList(orgTreeVO);
            } else {
                pushOrgTreeVOS = orgTreeVO.getChildren();
            }
            if (CollectionUtils.isEmpty(pushOrgTreeVOS)) {
                throw new BusinessErrorException("没有查询到待推送的企业信息");
            }
            logger.info("pushOrgTreeVOS:{}", JSON.toJSONString(pushOrgTreeVOS));
            Map<Long, List<OrgVO>> legalMap = permissionService.listLegalOrgInfoByIds(pushOrgTreeVOS.stream().map(OrgTreeVO::getId).distinct().collect(Collectors.toList()));
            logger.info("legalMap:{}", JSON.toJSONString(legalMap));
            List<OrgTreeVO> pushVOS = new ArrayList<>();
            for (OrgTreeVO treeVO : pushOrgTreeVOS) {
                List<OrgVO> orgVOS = legalMap.get(treeVO.getId());
                if (CollectionUtils.isNotEmpty(orgVOS)) {
                    pushVOS.addAll(orgVOS.stream().map(v -> {
                        OrgTreeVO tree = new OrgTreeVO();
                        BeanUtils.copyProperties(treeVO, tree);
                        tree.setSapcode(v.getSapcode());
                        tree.setShortName(v.getShortName());
                        tree.setName(v.getName());
                        return tree;
                    }).collect(Collectors.toList()));
                } else {
                    pushVOS.add(treeVO);
                }
            }
//            pushOrgTreeVOMap = pushVOS.stream().collect(Collectors.toMap(OrgTreeVO::getId, Function.identity(), (k1, k2) -> k1));

            List<EmployeeInfoVO> employeeInfoVOS = permissionService.getEmployeeInfoByUserIdList(Lists.newArrayList(userDTO.getUserId()));
            EmployeeInfoVO employeeInfoVO = CollectionUtils.isNotEmpty(employeeInfoVOS) ? employeeInfoVOS.get(0) : new EmployeeInfoVO();
            logger.info("pushVOS:{}", JSON.toJSONString(pushVOS));
            pushVOS.forEach(v -> {
                // 过滤配置的推送企业
                if (StringUtils.isNotBlank(pushHdOrgCodes) && !Arrays.stream(pushHdOrgCodes.split(",")).collect(Collectors.toList()).contains(v.getSapcode())) {
                    return;
                }
                try {
                    StoreApplyParamDTO storeApplyParamDTO = storeApplyParamService.getAutoApplyList(v.getId(),null, paramType, paramScope,userDTO);
                    List<AutoApplyParamDTO> paramDTOS = Lists.newArrayList(
                            storeApplyParamDTO.getOnwayStockDTO(),
                            storeApplyParamDTO.getUnAbleStockDTO(),
                            storeApplyParamDTO.getAutoApplyTimeDTO(),
//                            storeApplyParamDTO.getMiddleDealSwitchDTO(),
                            storeApplyParamDTO.getNewStoreMonth(),
                            storeApplyParamDTO.getNewGoodsDay(),
                            storeApplyParamDTO.getStoreProperty(),
                            storeApplyParamDTO.getGoodsClass(),
                            storeApplyParamDTO.getGoodsManagementAttr(),
                            storeApplyParamDTO.getGoodsSign()
                    );
                    paramDTOS.addAll(Optional.ofNullable(storeApplyParamDTO.getSpecialGoodsCtrlDTOS()).orElseGet(ArrayList::new));


                    logger.info("paramDTOS:{}", JSON.toJSONString(paramDTOS));
                    logger.info("pushHdCodeList:{}", JSON.toJSONString(pushHdCodeList));

                    paramDTOS = paramDTOS.stream().filter(paramDTO -> Objects.nonNull(paramDTO) && pushHdCodeList.contains(paramDTO.getParamCode())).collect(Collectors.toList());

                    logger.info("paramDTOS:{}", JSON.toJSONString(paramDTOS));

                    if (CollectionUtils.isNotEmpty(paramDTOS)) {
                        pushApplyParamToHd(v, employeeInfoVO, paramDTOS,orgId,orgType,null,null, paramScope);
                    }

                    switch (paramTypeEnum) {
                        case AUTO_APPLY:
                            StoreApplyParamDTO upperLimitApplyParamDTO = storeApplyParamService.getAutoApplyList(v.getId(),null, ApplyParamTypeEnum.APPLY_GOODS_UPPER_LIMIT.getCode(), paramScope, userDTO);
                            pushApplyParamGoodsUpperLimitToHd(v, employeeInfoVO, upperLimitApplyParamDTO.getUpperLimits(),
                                    storeApplyParamDTO.getGoodsLevelParamDTOS(),null,null);

                            break;
                        case MIDDLE_PACKAGE_GOODS_APPLY:
//                            "ISCM-APPLY-0009": "GJ0269"
//                            "ISCM-APPLY-0010": "GJ0269,GJ9084"
                            AutoApplyParamDTO middlePackageWayParamDTO = new AutoApplyParamDTO();// 中包装处理方式
                            AutoApplyParamDTO middlePackageRatioParamDTO = new AutoApplyParamDTO();// 中包装系数
                            AutoApplyParamDTO middleDealSwitchDTO = storeApplyParamDTO.getMiddleDealSwitchDTO();
                            AutoApplyRemainderDealDTO remainderDealDTO = storeApplyParamDTO.getRemainderDealDTO();
                            if ("false".equalsIgnoreCase(middleDealSwitchDTO.getParamValue())) {
                                middlePackageWayParamDTO.setParamCode(middleDealSwitchDTO.getParamCode());// 海典编码 GJ0269
                                middlePackageWayParamDTO.setParamValue(ComMiddleSwitchEnum.NO_APPROVE.getCode());
                            }
                            if ("true".equalsIgnoreCase(middleDealSwitchDTO.getParamValue())) {
                                ApplyParamRemainderDealEnum remainderDealEnum = ApplyParamRemainderDealEnum.getEnumByCode(remainderDealDTO.getParamValue());
                                middlePackageWayParamDTO.setParamCode(remainderDealDTO.getParamCode() + "_0");// 海典编码 GJ0269
                                middlePackageWayParamDTO.setParamValue(remainderDealEnum.getMiddlePackageSwitch());
                                if (Objects.equals(remainderDealEnum.getCode(), ApplyParamRemainderDealEnum.SELF.getCode())) {
                                    middlePackageRatioParamDTO.setParamCode(remainderDealDTO.getParamCode() + "_1");// 海典编码 GJ9084
                                    middlePackageRatioParamDTO.setParamValue(String.valueOf(remainderDealDTO.getSelf()));
                                }
                            }
                            pushApplyParamToHd(v, employeeInfoVO, Lists.newArrayList(middlePackageWayParamDTO, middlePackageRatioParamDTO),orgId,orgType,null,null, paramScope);

                            break;
                        case APPLY_GOODS_UPPER_LIMIT:
                            StoreApplyParamDTO autoApplyParamDTO = storeApplyParamService.getAutoApplyList(v.getId(),null, ApplyParamTypeEnum.AUTO_APPLY.getCode(), paramScope, userDTO);

                            List<StoreApplyParamGoodsUpperLimit> upperLimits = storeApplyParamDTO.getUpperLimits();
                            List<StoreApplyParamGoodsUpperLimitItem> exemptionLimits = storeApplyParamDTO.getExemptionLimits();
                            List<StoreApplyParamGoodsUpperLimitItem> manualLimits = storeApplyParamDTO.getManualLimits();
                            List<StoreApplyParamGoodsUpperLimitItem> urgentLimits = storeApplyParamDTO.getUrgentLimits();

                            List<StoreApplyParamGoodsUpperLimitItem> upperLimitItems = new ArrayList<>();
                            upperLimitItems.addAll(exemptionLimits);
                            upperLimitItems.addAll(manualLimits);
                            upperLimitItems.addAll(urgentLimits);
                            StoreApplyParamDTO autoApplyList = storeApplyParamService.getAutoApplyList(v.getId(), null,ApplyParamTypeEnum.APPLY_GOODS_UPPER_LIMIT.getCode(), paramScope, userDTO);
                            logger.debug("请货上限进入 下推海典 方法");
                            pushApplyParamToHd(v, employeeInfoVO, Lists.newArrayList(autoApplyList.getApplyModifyItemRatio(), autoApplyList.getApplyQtyUpperRatio(), autoApplyList.getApplyQtyLowerRatio()),orgId,orgType,null,null, paramScope);
                            if (ApplyUpperLimitScopeEnum.NORMAL.getCode() == paramScope) {
                                pushApplyParamGoodsUpperLimitToHd(v, employeeInfoVO, upperLimits, autoApplyParamDTO.getGoodsLevelParamDTOS(), null, null);
                                pushApplyParamGoodsUpperLimitToSAP(v, employeeInfoVO, upperLimits, autoApplyParamDTO.getGoodsLevelParamDTOS());
                            }
                            pushApplyParamGoodsUpperLimitItemToHd(v, employeeInfoVO, upperLimitItems,null,null,null,null, paramScope);

                            break;
                        default:
                    }
                } catch (Exception e) {
                    logger.error("获取请货参数异常,orgId={}", v.getId(), e);
                }
            });
        } catch (Exception e) {
            logger.error("pushParams|error!", e);
        }
    }

    @Override
    public void pushParams(TokenUserDTO userDTO, Long orgId, Integer orgType, Integer paramType,Byte operationFlag, Integer paramScope) {
        logger.debug("请货上限进入 pushParams 方法");
        if (null == orgId || null == orgType || null == paramType || null == operationFlag) {
            logger.info("orgId|orgType|paramType|storeApplyParamDTO must be not null.");
            return;
        }
        OrgTypeEnum orgTypeEnum = OrgTypeEnum.getEnumByCode(orgType);
        if (null == orgTypeEnum) {
            logger.info("orgType={} is illegal.", orgType);
            return;
        }
        if (!Objects.equals(orgTypeEnum, OrgTypeEnum.ORG_TYPE_STORE_GROUP)) {
            logger.info("仅支持门店组级别的参数");
            return;
        }
        ApplyParamTypeEnum paramTypeEnum = ApplyParamTypeEnum.getEnumByCode(paramType);
        if (null == paramTypeEnum) {
            logger.info("paramType={} is illegal.", paramType);
            return;
        }
        if (StringUtils.isBlank(pushHdCodes)) {
            logger.info("未配置推送海典的请货参数编码");
            return;
        }
        List<String> pushHdCodeList = Lists.newArrayList(pushHdCodes.split(","));
        pushHdCodeList.retainAll(Lists.newArrayList(ApplyParamTypeEnum.paramTypeAndCodeMapping().get(paramTypeEnum)));
        if (CollectionUtils.isEmpty(pushHdCodeList)) {
            logger.info("未配置推送海典的请货参数编码,paramType={}", paramType);
            return;
        }
        try{
            List<EmployeeInfoVO> employeeInfoVOS = permissionService.getEmployeeInfoByUserIdList(Lists.newArrayList(userDTO.getUserId()));
            EmployeeInfoVO employeeInfoVO = CollectionUtils.isNotEmpty(employeeInfoVOS) ? employeeInfoVOS.get(0) : new EmployeeInfoVO();
            Map<String, List<MdmStoreBaseDTO>> comIdMdmStoreBaseMap = getMdmStoreBaseMap(orgId);
            if (comIdMdmStoreBaseMap == null) return;
            for (String comId : comIdMdmStoreBaseMap.keySet()) {
                List<MdmStoreBaseDTO> mdmStoreBaseDTOS = comIdMdmStoreBaseMap.get(comId);
                if (CollectionUtils.isEmpty(mdmStoreBaseDTOS)) {
                    logger.info("法人下没有要推送海典的数据：{}", comId);
                    continue;
                }
                StoreApplyParamDTO storeApplyParamDTO = storeApplyParamService.getAutoApplyList(orgId, OrgTypeEnum.ORG_TYPE_STORE_GROUP.getCode(), paramType, paramScope, userDTO);
                List<AutoApplyParamDTO> paramDTOS = Lists.newArrayList(
                        storeApplyParamDTO.getOnwayStockDTO(),
                        storeApplyParamDTO.getUnAbleStockDTO(),
                        storeApplyParamDTO.getAutoApplyTimeDTO(),
                        storeApplyParamDTO.getNewStoreMonth(),
                        storeApplyParamDTO.getNewGoodsDay(),
                        storeApplyParamDTO.getStoreProperty(),
                        storeApplyParamDTO.getGoodsClass(),
                        storeApplyParamDTO.getGoodsManagementAttr(),
                        storeApplyParamDTO.getGoodsSign()
                );
                paramDTOS.addAll(Optional.ofNullable(storeApplyParamDTO.getSpecialGoodsCtrlDTOS()).orElseGet(ArrayList::new));

                paramDTOS = paramDTOS.stream().filter(paramDTO -> Objects.nonNull(paramDTO) && pushHdCodeList.contains(paramDTO.getParamCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(paramDTOS)) {
                    pushApplyParamToHd(null, employeeInfoVO, paramDTOS, orgId, orgType, mdmStoreBaseDTOS, operationFlag, paramScope);
                }
                switch (paramTypeEnum) {
                    case AUTO_APPLY:
                        StoreApplyParamDTO upperLimitApplyParamDTO = storeApplyParamService.getAutoApplyList(orgId, orgType, ApplyParamTypeEnum.APPLY_GOODS_UPPER_LIMIT.getCode(), paramScope, userDTO);
                        pushApplyParamGoodsUpperLimitToHd(null, employeeInfoVO, upperLimitApplyParamDTO.getUpperLimits(), storeApplyParamDTO.getGoodsLevelParamDTOS(), orgId, operationFlag);
                        break;
                    case APPLY_GOODS_UPPER_LIMIT:
                        StoreApplyParamDTO autoApplyParamDTO = storeApplyParamService.getAutoApplyList(orgId, orgType, ApplyParamTypeEnum.AUTO_APPLY.getCode(), paramScope, userDTO);

                        List<StoreApplyParamGoodsUpperLimit> upperLimits = storeApplyParamDTO.getUpperLimits();
                        List<StoreApplyParamGoodsUpperLimitItem> exemptionLimits = storeApplyParamDTO.getExemptionLimits();
                        List<StoreApplyParamGoodsUpperLimitItem> manualLimits = storeApplyParamDTO.getManualLimits();
                        List<StoreApplyParamGoodsUpperLimitItem> urgentLimits = storeApplyParamDTO.getUrgentLimits();

                        List<StoreApplyParamGoodsUpperLimitItem> upperLimitItems = new ArrayList<>();
                        upperLimitItems.addAll(exemptionLimits);
                        upperLimitItems.addAll(manualLimits);
                        upperLimitItems.addAll(urgentLimits);
                        StoreApplyParamDTO autoApplyList = storeApplyParamService.getAutoApplyList(orgId, orgType, ApplyParamTypeEnum.APPLY_GOODS_UPPER_LIMIT.getCode(), paramScope, userDTO);
                        logger.debug("请货上限进入 下推海典 方法");
                        pushApplyParamToHd(null, employeeInfoVO, Lists.newArrayList(autoApplyList.getApplyModifyItemRatio(), autoApplyList.getApplyQtyUpperRatio(), autoApplyList.getApplyQtyLowerRatio()), orgId, orgType, mdmStoreBaseDTOS, operationFlag, paramScope);
                        if (ApplyUpperLimitScopeEnum.NORMAL.getCode() == paramScope) {
                            pushApplyParamGoodsUpperLimitToHd(null, employeeInfoVO, upperLimits, autoApplyParamDTO.getGoodsLevelParamDTOS(), orgId, operationFlag);
                        }
                        pushApplyParamGoodsUpperLimitItemToHd(null, employeeInfoVO, upperLimitItems,comId,orgType,operationFlag,mdmStoreBaseDTOS,paramScope);
                        break;
                    default:
                        break;
                }
            }
        }catch (Exception e) {
            logger.error("获取请货参数异常,orgId={}", orgId, e);
        }
    }

    /**
     * 根据门店组ID获取法人机构信息
     * @param orgId
     * @return
     * @throws Exception
     */
    @Override
    public Map<String, List<MdmStoreBaseDTO>> getMdmStoreBaseMap(Long orgId){
        try{
            List<String> storeOrgIdList = tagEntityCodeListQuery(orgId);
            if (CollectionUtils.isEmpty(storeOrgIdList)){
                return null;
            }
            logger.debug("根据门店组ID调用标签获取门店 返回={}", storeOrgIdList);
            List<OrgInfoBaseCache> orgDTOList = permissionService.getOrgBaseCacheByOrgId(storeOrgIdList.stream().map(Long::valueOf).collect(Collectors.toList()), OrgTypeEnum.ORG_TYPE_STORE.getCode());
//            List<OrgDTO> orgDTOList = permissionService.listOrgInfoByIds(storeOrgIdList.stream().map(Long::valueOf).collect(Collectors.toList()), false);
            if (CollectionUtils.isEmpty(orgDTOList)){
                return null;
            }
            List<Long> storeIds = orgDTOList.stream().map(OrgInfoBaseCache::getOutId).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(storeIds)) {
                logger.info("storeIds is empty");
                return null;
            }
            List<MdmStoreBaseDTO> storeByStoreIds = storeService.findStoreByStoreIds(storeIds);
            Map<String, List<MdmStoreBaseDTO>> comIdMdmStoreBaseMap = storeByStoreIds.stream().collect(Collectors.groupingBy(MdmStoreBaseDTO::getComId));
            logger.debug("根据门店组ID获取法人机构信息 返回={}", comIdMdmStoreBaseMap);
            return comIdMdmStoreBaseMap;
        }catch (Exception e){
            logger.error("获取门店组、门店组获取法人树信息异常",e);
        }
        return new HashMap<>();

    }

    public List<String> tagEntityCodeListQuery(Long tagId) {
        TagEntityQueryServerParam queryServerParam = new TagEntityQueryServerParam();
        queryServerParam.setManagerFlag(YNEnum.YES.getType());
        queryServerParam.setResultType(ResultTypeEnums.ENTITYCODE.getType());
        queryServerParam.setTagBizType(tagBizType);
        queryServerParam.setTagId(tagId);
        queryServerParam.setEntityType(EntityTypeEnum.STORE.getType());
        List<String> storeOrgList= tagService.tagEntityCodeListQuery(queryServerParam);
        return storeOrgList;
    }

    private void pushApplyParamGoodsUpperLimitToSAP(OrgTreeVO orgTreeVO, EmployeeInfoVO employeeInfoVO, List<StoreApplyParamGoodsUpperLimit> upperLimits, List<GoodsLevelParamDTO> goodsLevelParamDTOS) {
        try {
//            if (!this.pushAble(orgTreeVO.getId(), ApplyParamCodeEnum.GOODS_UPPER_LIMIT)) {
//                logger.info("pushApplyParamGoodsUpperLimitToHd|pushAble=false");
//                return;
//            }
            List<Map<String, Object>> details = new ArrayList<>();
            upperLimits.forEach(data -> {
                Map<String, Object> detailList = new HashMap<>();
                detailList.put("LASTTIME", data.getGmtUpdate());
                detailList.put("LASTMODIFY", data.getUpdatedBy());
                detailList.put("MDM_COMPANY_CODE", orgTreeVO.getSapcode());
                detailList.put("PROMTYPE", data.getGoodsType());
                detailList.put("WARE_CLASSNO", data.getGoodsLevel());
                detailList.put("COTYPE", Objects.nonNull(data.getStockUpperLimitMultiple()) ? 1 : 2);
                detailList.put("CONO", Objects.nonNull(data.getStockUpperLimitMultiple()) ? data.getStockUpperLimitMultiple() : data.getMarketableDays());
                detailList.put("LOEKZ", "");
                details.add(detailList);
            });
            Map<String, Object> headerjson = new HashMap<>();
            if (CollectionUtils.isEmpty(details)) {
                Map<String, Object> delete = new HashMap<>();
                delete.put("MDM_COMPANY_CODE", orgTreeVO.getSapcode());
                delete.put("LOEKZ", "X");
                details.add(delete);
                headerjson.put("DETAIL", details);
            } else {
                headerjson.put("DETAIL", details);
            }
            Map<String, Object> mbConfig = MBUtils.getMbConfig(sapPushProp);
            String pushData = MBUtils.assembleParam(mbConfig, JSON.toJSONString(headerjson));
            mbUtils.pushToMB(MBUtils.getMbUrl(sapPushProp), pushData);
        } catch (Exception e) {
            logger.error("推送参数至sap出错", e);
        }
    }

    private boolean pushAble(Long orgId, ApplyParamCodeEnum paramCodeEnum,Integer orgType) {
        logger.info("orgId:{},paramCodeEnum:{}",orgId,JSONObject.toJSONString(paramCodeEnum));
        if (null == paramCodeEnum) {
            return false;
        }
        if (Objects.equals(this.orgType, OrgTypeEnum.ORG_TYPE_BUSINESS.getCode())) {
            return true;
        }
        if (Objects.equals(orgType, OrgTypeEnum.ORG_TYPE_STORE_GROUP.getCode())) {
            return true;
        }
        long count;
        switch (paramCodeEnum) {
            case GOODS_UPPER_LIMIT:
                IscmStoreApplyParamGoodsUpperLimitOrgExample upperLimitOrgExample = new IscmStoreApplyParamGoodsUpperLimitOrgExample();
                IscmStoreApplyParamGoodsUpperLimitOrgExample.Criteria criteria = upperLimitOrgExample.createCriteria();
                if (orgType != null && orgType.equals(OrgTypeEnum.ORG_TYPE_STORE_GROUP.getCode())) {
                    criteria.andOrgIdEqualTo(orgId);
                } else {
                    criteria.andOrgIdEqualTo(orgId).andParamLevelEqualTo(OrgTypeEnum.ORG_TYPE_BUSINESS.getCode());
                }
                count = iscmStoreApplyParamGoodsUpperLimitOrgMapper.countByExample(upperLimitOrgExample);
                break;
            case GOODS_UPPER_LIMIT_ITEM:
                IscmStoreApplyParamGoodsUpperLimitItemOrgExample upperLimitItemOrgExample = new IscmStoreApplyParamGoodsUpperLimitItemOrgExample();
                IscmStoreApplyParamGoodsUpperLimitItemOrgExample.Criteria criteria1 = upperLimitItemOrgExample.createCriteria();
                if (orgType != null && orgType.equals(OrgTypeEnum.ORG_TYPE_STORE_GROUP.getCode())) {
                    criteria1.andOrgIdEqualTo(orgId);
                } else {
                    criteria1.andOrgIdEqualTo(orgId).andParamLevelEqualTo(OrgTypeEnum.ORG_TYPE_BUSINESS.getCode());
                }
                count = iscmStoreApplyParamGoodsUpperLimitItemOrgMapper.countByExample(upperLimitItemOrgExample);
                break;
            case GOODS_LEVEL:
                IscmStoreApplyParamGoodsLevelOrgExample goodsLevelOrgExample = new IscmStoreApplyParamGoodsLevelOrgExample();
                IscmStoreApplyParamGoodsLevelOrgExample.Criteria criteria2 = goodsLevelOrgExample.createCriteria();
                if (orgType != null && orgType.equals(OrgTypeEnum.ORG_TYPE_STORE_GROUP.getCode())) {
                    criteria2.andOrgIdEqualTo(orgId);
                } else {
                    criteria2.andOrgIdEqualTo(orgId).andParamLevelEqualTo(OrgTypeEnum.ORG_TYPE_BUSINESS.getCode());
                }
                count = iscmStoreApplyParamGoodsLevelOrgMapper.countByExample(goodsLevelOrgExample);
                break;
            default:
                IscmStoreApplyParamOrgExample paramOrgExample = new IscmStoreApplyParamOrgExample();
                IscmStoreApplyParamOrgExample.Criteria criteria3 = paramOrgExample.createCriteria();
                if (orgType != null && orgType.equals(OrgTypeEnum.ORG_TYPE_STORE_GROUP.getCode())) {
                    criteria3.andOrgIdEqualTo(orgId).andParamCodeEqualTo(paramCodeEnum.getCode());
                } else {
                    criteria3.andOrgIdEqualTo(orgId).andParamCodeEqualTo(paramCodeEnum.getCode()).andParamLevelEqualTo(OrgTypeEnum.ORG_TYPE_BUSINESS.getCode());
                }
                count = iscmStoreApplyParamOrgMapper.countByExample(paramOrgExample);
        }
        return count <= 0L;
    }

    private String getParamValue(String paramValue) {
        if ("TRUE".equalsIgnoreCase(paramValue) || "FALSE".equalsIgnoreCase(paramValue)) {
            paramValue = BooleanUtils.toIntegerObject(BooleanUtils.toBooleanObject(paramValue)).toString();
        }
        return paramValue;
    }

    private void pushApplyParamToHd(OrgTreeVO orgTreeVO, EmployeeInfoVO employeeInfoVO, List<AutoApplyParamDTO> paramDTOS,Long orgId,Integer orgType,List<MdmStoreBaseDTO> mdmStoreBaseDTOS,Byte operationFlag, Integer paramScope) {
        logger.debug("请货上限进入pushApplyParamToHd方法");
        RequestBodyDTO requestBodyDTO = null;
        try {
            JSONObject hdCodeMappingJSONObject = Optional.ofNullable(JSONObject.parseObject(hdCodeMapping)).orElseGet(JSONObject::new);
            logger.info("hdCodeMappingJSONObject:{}", hdCodeMappingJSONObject.toJSONString());
            List<ApplyParamPush> paramPushList = new ArrayList<>();
            paramDTOS.forEach(v -> {
                boolean pushAble;
                ApplyParamPush paramPush = new ApplyParamPush();
                if (v.getParamCode().contains("_")) {
                    String paramCode = v.getParamCode().substring(0, v.getParamCode().lastIndexOf("_"));
                    String idx = v.getParamCode().substring(v.getParamCode().lastIndexOf("_") + 1);
                    String hdCode = hdCodeMappingJSONObject.getString(paramCode);
                    String[] hdCodes;
                    if (StringUtils.isBlank(hdCode)) {
                        hdCodes = ApplyParamCodeEnum.getEnumByCode(paramCode).getHdCode().split(",");
                    } else {
                        hdCodes = hdCode.split(",");
                    }
                    paramPush.setParamCode(hdCodes[Integer.parseInt(idx)]);
                    pushAble = this.pushAble(Objects.nonNull(orgTreeVO)?orgTreeVO.getId():orgId, ApplyParamCodeEnum.getEnumByCode(paramCode),orgType);

                } else {
                    paramPush.setParamCode(Optional.ofNullable(hdCodeMappingJSONObject.getString(v.getParamCode())).orElse(ApplyParamCodeEnum.getEnumByCode(v.getParamCode()).getHdCode()));
                    pushAble = this.pushAble(Objects.nonNull(orgTreeVO)?orgTreeVO.getId():orgId, ApplyParamCodeEnum.getEnumByCode(v.getParamCode()),orgType);
                }
                paramPush.setParamValue(getParamValue(v.getParamValue()));
                if (null != operationFlag && operationFlag.equals(Constants.HD_PUSH_DEL_DATA_STATUS)) {
                    paramPush.setParamValue("");
                }
                paramPush.setMdmCode(Objects.nonNull(orgTreeVO)?orgTreeVO.getSapcode():mdmStoreBaseDTOS.get(0).getComId());
                List<StoreInfo> storeInfos = new ArrayList<>();
                if (orgType.equals(OrgTypeEnum.ORG_TYPE_STORE_GROUP.getCode())){
                    List<String> storeNoList = mdmStoreBaseDTOS.stream().map(MdmStoreBaseDTO::getStoreNo).collect(Collectors.toList());
                    for (String s : storeNoList) {
                        StoreInfo storeInfo = new StoreInfo();
                        storeInfo.setStoreCode(s);
                        storeInfo.setFlag(operationFlag);
                        storeInfos.add(storeInfo);
                    }
                    paramPush.setStoreInfo(storeInfos);
                }
                paramPush.setUpdateUser(employeeInfoVO.getEmpCode());
                paramPush.setUpdateTime(v.getGmtUpdate());
                // 增加参数域
                paramPush.setParamScope(null == paramScope ? ApplyUpperLimitScopeEnum.NORMAL.getCode() : paramScope);
                if (pushAble) {
                    paramPushList.add(paramPush);
                }
            });
            if (null != orgType && orgType.equals(OrgTypeEnum.ORG_TYPE_STORE_GROUP.getCode())) {
                // 取配置
                boolean flag = false;
                if (org.apache.commons.lang.StringUtils.isNotBlank(mbHotReturnProperties)) {
                    JSONObject returnJson = JSONObject.parseObject(mbHotReturnProperties);
                    flag = returnJson.getBoolean("return");
                }
                // 如果回滚的话, 走原下发海典方法
                if (flag) {
                    Map<String, Object> mbConfig = MBUtils.getMbConfig(applyParamToHdBak);
                    mbConfig.put("updatetime",DateUtils.conventDateStrByDate(new Date(),DateUtils.DATE_MDM_PATTERN_MILLISECOND));
                    Map<String, List<ApplyParamPush>> applyParamPushMap = new HashMap<>();
                    applyParamPushMap.put("detail",paramPushList);
                    String pushData = MBUtils.assembleParam(mbConfig, JSONObject.toJSONString(applyParamPushMap));
                    mbUtils.pushToMB(MBUtils.getMbUrl(applyParamToHdBak), pushData);
                } else {
                    Map<String, Object> mbConfig = MBUtils.getMbConfig(applyParamToHd);
                    mbConfig.put("updatetime",DateUtils.conventDateStrByDate(new Date(),DateUtils.DATE_MDM_PATTERN_MILLISECOND));
                    Map<String, List<ApplyParamPush>> applyParamPushMap = new HashMap<>();
                    applyParamPushMap.put("detail",paramPushList);
                    // 增加 sopCompanyCode
                    String pushData = MBUtils.assembleParam(mbConfig, JSONObject.toJSONString(applyParamPushMap), Objects.nonNull(orgTreeVO)?orgTreeVO.getSapcode():mdmStoreBaseDTOS.get(0).getComId());
                    mbUtils.pushToMB(MBUtils.getMbUrl(applyParamToHd), pushData);
                }
                return;
            }
            requestBodyDTO = new RequestBodyDTO();
            requestBodyDTO.setAction(ErpSaasActionEnum.PUSH_APPLY_PARAM_TO_HD.getAction());
            requestBodyDTO.setAppName(Constants.APP_NAME.toLowerCase());
            requestBodyDTO.setBusinessId("99999");
            requestBodyDTO.setBdata(JacksonUtil.packingJsonData("detail", paramPushList));
            // 取配置
            boolean flag = false;
            if (org.apache.commons.lang.StringUtils.isNotBlank(mbHotReturnProperties)) {
                JSONObject returnJson = JSONObject.parseObject(mbHotReturnProperties);
                flag = returnJson.getBoolean("return");
            }
            // 如果回滚的话, 走原下发海典方法, 参数里不加compId和route就走老url, 否则走新url
            if (!flag) {
                requestBodyDTO.setCompId(Objects.nonNull(orgTreeVO)?orgTreeVO.getSapcode():mdmStoreBaseDTOS.get(0).getComId());
                requestBodyDTO.setRoute("mb");
            }

        } catch (JsonProcessingException e) {
            logger.error("pushApplyParamToHd|解析发送数据异常", e);
        } catch (Exception e) {
            logger.error("pushApplyParamToHd|error!", e);
        }
        try {
            //获取发送给SAP的报文
            logger.info("pushApplyParamToHd|发送SAP报文给海典,requestBody:{}", requestBodyDTO);
            //发送sap报文
            String result = erpSaasService.accept(requestBodyDTO);
            logger.info("pushApplyParamToHd|发送SAP报文给海典,result:{}", result);
        } catch (Exception e) {
            logger.error("pushApplyParamToHd|error!", e);
        }
    }

    /**
     * ApplyParamGoodsUpperLimit 推送海典数据 门店组和企业做区分
     * @param orgTreeVO
     * @param employeeInfoVO
     * @param upperLimits
     * @param goodsLevelParamDTOS
     * @param orgId
     */
    @Override
    public void pushApplyParamGoodsUpperLimitToHd(OrgTreeVO orgTreeVO, EmployeeInfoVO employeeInfoVO, List<StoreApplyParamGoodsUpperLimit> upperLimits,
                                                  List<GoodsLevelParamDTO> goodsLevelParamDTOS, Long orgId,Byte operationFlag) {
        logger.debug("请货上限进入 pushApplyParamGoodsUpperLimitToHd 方法");
        if (null != orgId) {
            Map<String, List<MdmStoreBaseDTO>> mdmStoreBaseMap = getMdmStoreBaseMap(orgId);
            if (MapUtils.isEmpty(mdmStoreBaseMap)) {
                return;
            }
            for (String comId : mdmStoreBaseMap.keySet()) {
                List<MdmStoreBaseDTO> mdmStoreBaseDTOS = mdmStoreBaseMap.get(comId);
                if (CollectionUtils.isEmpty(mdmStoreBaseDTOS)){
                    continue;
                }
                pushApplyComIdParamGoodsUpperLimitToHd( orgTreeVO,  employeeInfoVO, upperLimits,  goodsLevelParamDTOS,mdmStoreBaseDTOS,operationFlag);
            }
        }else {
            pushApplyComIdParamGoodsUpperLimitToHd( orgTreeVO,  employeeInfoVO, upperLimits,  goodsLevelParamDTOS,null,operationFlag);
        }
    }

    private void pushApplyComIdParamGoodsUpperLimitToHd(OrgTreeVO orgTreeVO, EmployeeInfoVO employeeInfoVO,
                                                        List<StoreApplyParamGoodsUpperLimit> upperLimits, List<GoodsLevelParamDTO> goodsLevelParamDTOS,
                                                        List<MdmStoreBaseDTO> mdmStoreBaseDTOS,Byte operationFlag) {
        logger.debug("请货上限进入 pushApplyComIdParamGoodsUpperLimitToHd 方法");
        RequestBodyDTO requestBodyDTO = null;
        try {
//            if (!this.pushAble(orgTreeVO.getId(), ApplyParamCodeEnum.GOODS_UPPER_LIMIT)) {
//                logger.info("pushApplyParamGoodsUpperLimitToHd|pushAble=false");
//                return;
//            }
            Map<Byte, GoodsLevelParamDTO> goodsLevelMap = goodsLevelParamDTOS.stream().filter(v -> Objects.nonNull(v.getStartValue()) && Objects.nonNull(v.getEndValue())).collect(Collectors.toMap(GoodsLevelParamDTO::getGoodsLevel, Function.identity(), (k1, k2) -> k1));
            Map<String, ApplyParamPushUpperLimit> pushUpperLimits = new HashMap<>();
            for (ApplyParamGoodsLevelEnum level : ApplyParamGoodsLevelEnum.values()) {
                ApplyParamPushUpperLimit limit = new ApplyParamPushUpperLimit();
                if (Objects.nonNull(orgTreeVO)){
                    limit.setMdmCode(orgTreeVO.getSapcode());
                }
                List<StoreInfo> storeInfos = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(mdmStoreBaseDTOS)){
                    limit.setMdmCode(mdmStoreBaseDTOS.get(0).getComId());
                    for (MdmStoreBaseDTO mdmStoreBaseDTO : mdmStoreBaseDTOS) {
                        StoreInfo storeInfo = new StoreInfo();
                        storeInfo.setStoreCode(mdmStoreBaseDTO.getStoreNo());
                        storeInfo.setFlag(operationFlag);
                        storeInfos.add(storeInfo);
                    }
                    limit.setStoreInfo(storeInfos);
                }
                limit.setGoodsLevel(level.getCode()+"");
                limit.setGoodsLevelName(level.getName());
                limit.setGoodsLevelStartValue(0);
                limit.setGoodsLevelEndValue(0);
                GoodsLevelParamDTO paramDTO = goodsLevelMap.get(level.getCode());
                if (Objects.nonNull(paramDTO)) {
                    limit.setGoodsLevelStartValue(paramDTO.getStartValue());
                    limit.setGoodsLevelEndValue(paramDTO.getEndValue());
                }
                if (Constants.HD_PUSH_DEL_DATA_STATUS.equals(operationFlag)){
                    limit.setGoodsLevelStartValue(0);
                    limit.setGoodsLevelEndValue(0);
                }
                if (level.getCode() == ApplyParamGoodsLevelEnum.NEW_GOODS.getCode()){
                    limit.setGoodsLevelStartValue(0);
                    limit.setGoodsLevelEndValue(9999);
                }
                limit.setRoutineStockUpperLimitMultiple(null);
                limit.setRoutineMarketableDays(null);
                limit.setPromotionStockUpperLimitMultiple(null);
                limit.setPromotionMarketableDays(null);
                limit.setAaStockUpperLimitMultiple(null);
                limit.setAaMarketableDays(null);
                limit.setExemptionStockUpperLimitMultiple(null);
                limit.setExemptionMarketableDays(null);
                limit.setUpdateTime(DateUtils.conventDateStrByDate(new Date(), DateUtils.DATETIME_PATTERN));
                limit.setUpdateUser(employeeInfoVO.getEmpCode());
                limit.setGoldenStockUpperLimitMultiple(null);
                limit.setGoldenMarketableDays(null);
                pushUpperLimits.put(String.valueOf(level.getCode()),limit);
            }

            // 根据商品等级分组
            Map<Byte, List<StoreApplyParamGoodsUpperLimit>> map = upperLimits.stream().collect(Collectors.groupingBy(StoreApplyParamGoodsUpperLimit::getGoodsLevel));
            map.forEach((k, v) -> {
                ApplyParamPushUpperLimit pushUpperLimit = new ApplyParamPushUpperLimit();
                String goodsLevelName = ApplyParamGoodsLevelEnum.getNameByCode(k);
                pushUpperLimit.setGoodsLevel(k+"");
                pushUpperLimit.setGoodsLevelName(goodsLevelName);
                GoodsLevelParamDTO paramDTO = goodsLevelMap.get(k);
                if (Objects.nonNull(paramDTO)) {
                    pushUpperLimit.setGoodsLevelStartValue(paramDTO.getStartValue());
                    pushUpperLimit.setGoodsLevelEndValue(paramDTO.getEndValue());
                } else {
                    pushUpperLimit.setGoodsLevelStartValue(0);
                    pushUpperLimit.setGoodsLevelEndValue(0);
                }
                if (Constants.DELETE_STATUS.equals(operationFlag)){
                    pushUpperLimit.setGoodsLevelStartValue(0);
                    pushUpperLimit.setGoodsLevelEndValue(0);
                }
                pushUpperLimit.setRoutineStockUpperLimitMultiple(v.stream().filter(v1 -> Objects.equals(v1.getGoodsType(), ApplyParamGoodsTypeEnum.ROUTINE.getCode())).findAny().map(StoreApplyParamGoodsUpperLimit::getStockUpperLimitMultiple).orElse(null));
                pushUpperLimit.setRoutineMarketableDays(v.stream().filter(v1 -> Objects.equals(v1.getGoodsType(), ApplyParamGoodsTypeEnum.ROUTINE.getCode())).findAny().map(StoreApplyParamGoodsUpperLimit::getMarketableDays).orElse(null));
                pushUpperLimit.setPromotionStockUpperLimitMultiple(v.stream().filter(v1 -> Objects.equals(v1.getGoodsType(), ApplyParamGoodsTypeEnum.PROMOTION.getCode())).findAny().map(StoreApplyParamGoodsUpperLimit::getStockUpperLimitMultiple).orElse(null));
                pushUpperLimit.setPromotionMarketableDays(v.stream().filter(v1 -> Objects.equals(v1.getGoodsType(), ApplyParamGoodsTypeEnum.PROMOTION.getCode())).findAny().map(StoreApplyParamGoodsUpperLimit::getMarketableDays).orElse(null));
                pushUpperLimit.setAaStockUpperLimitMultiple(v.stream().filter(v1 -> Objects.equals(v1.getGoodsType(), ApplyParamGoodsTypeEnum.AA.getCode())).findAny().map(StoreApplyParamGoodsUpperLimit::getStockUpperLimitMultiple).orElse(null));
                pushUpperLimit.setAaMarketableDays(v.stream().filter(v1 -> Objects.equals(v1.getGoodsType(), ApplyParamGoodsTypeEnum.AA.getCode())).findAny().map(StoreApplyParamGoodsUpperLimit::getMarketableDays).orElse(null));
                pushUpperLimit.setAaPlusStockUpperLimitMultiple(v.stream().filter(v1 -> Objects.equals(v1.getGoodsType(), ApplyParamGoodsTypeEnum.AAPLUS.getCode())).findAny().map(StoreApplyParamGoodsUpperLimit::getStockUpperLimitMultiple).orElse(null));
                pushUpperLimit.setAaPlusMarketableDays(v.stream().filter(v1 -> Objects.equals(v1.getGoodsType(), ApplyParamGoodsTypeEnum.AAPLUS.getCode())).findAny().map(StoreApplyParamGoodsUpperLimit::getMarketableDays).orElse(null));
                pushUpperLimit.setFlagshipStockUpperLimitMultiple(v.stream().filter(v1 -> Objects.equals(v1.getGoodsType(), ApplyParamGoodsTypeEnum.FLAGSHIP.getCode())).findAny().map(StoreApplyParamGoodsUpperLimit::getStockUpperLimitMultiple).orElse(null));
                pushUpperLimit.setFlagshipMarketableDays(v.stream().filter(v1 -> Objects.equals(v1.getGoodsType(), ApplyParamGoodsTypeEnum.FLAGSHIP.getCode())).findAny().map(StoreApplyParamGoodsUpperLimit::getMarketableDays).orElse(null));
                pushUpperLimit.setExemptionStockUpperLimitMultiple(v.stream().filter(v1 -> Objects.equals(v1.getGoodsType(), ApplyParamGoodsTypeEnum.EXEMPTION.getCode())).findAny().map(StoreApplyParamGoodsUpperLimit::getStockUpperLimitMultiple).orElse(null));
                pushUpperLimit.setExemptionMarketableDays(v.stream().filter(v1 -> Objects.equals(v1.getGoodsType(), ApplyParamGoodsTypeEnum.EXEMPTION.getCode())).findAny().map(StoreApplyParamGoodsUpperLimit::getMarketableDays).orElse(null));
                pushUpperLimit.setCommissionStockUpperLimitMultiple(v.stream().filter(v1 -> Objects.equals(v1.getGoodsType(), ApplyParamGoodsTypeEnum.COMMISSION.getCode())).findAny().map(StoreApplyParamGoodsUpperLimit::getStockUpperLimitMultiple).orElse(null));
                pushUpperLimit.setCommissionMarketableDays(v.stream().filter(v1 -> Objects.equals(v1.getGoodsType(), ApplyParamGoodsTypeEnum.COMMISSION.getCode())).findAny().map(StoreApplyParamGoodsUpperLimit::getMarketableDays).orElse(null));

                if (Objects.nonNull(orgTreeVO)){
                    pushUpperLimit.setMdmCode(orgTreeVO.getSapcode());
                }
                if (CollectionUtils.isNotEmpty(mdmStoreBaseDTOS)){
                    List<StoreInfo> storeInfos = new ArrayList<>();
                    pushUpperLimit.setMdmCode(mdmStoreBaseDTOS.get(0).getComId());
                        for (MdmStoreBaseDTO mdmStoreBaseDTO : mdmStoreBaseDTOS) {
                            StoreInfo storeInfo = new StoreInfo();
                            storeInfo.setStoreCode(mdmStoreBaseDTO.getStoreNo());
                            storeInfo.setFlag(operationFlag);
                            storeInfos.add(storeInfo);
                        }
                    pushUpperLimit.setStoreInfo(storeInfos);
                }
                pushUpperLimit.setUpdateUser(employeeInfoVO.getEmpCode());
                pushUpperLimit.setUpdateTime(v.get(0).getGmtUpdate());
                if (goodsLevelName.equals(ApplyParamGoodsLevelEnum.NEW_GOODS.getName())){
                    pushUpperLimit.setGoodsLevelStartValue(0);
                    pushUpperLimit.setGoodsLevelEndValue(9999);
                }
                pushUpperLimits.replace(pushUpperLimit.getGoodsLevel(), pushUpperLimit);
            });

            if (CollectionUtils.isNotEmpty(mdmStoreBaseDTOS)) {
                // 取配置
                boolean flag = false;
                if (org.apache.commons.lang.StringUtils.isNotBlank(mbHotReturnProperties)) {
                    JSONObject returnJson = JSONObject.parseObject(mbHotReturnProperties);
                    flag = returnJson.getBoolean("return");
                }
                // 如果回滚的话, 走原下发海典方法
                if (flag) {
                    Map<String, Object> mbConfig = MBUtils.getMbConfig(storeApplyParamPrpoBak);
                    mbConfig.put("updatetime", DateUtils.conventDateStrByDate(new Date(), DateUtils.DATE_MDM_PATTERN_MILLISECOND));
                    Map<String, List<ApplyParamPushUpperLimit>> pushUpperLimitMap = new HashMap<>();
                    pushUpperLimitMap.put("detail",pushUpperLimits.values().stream().sorted(Comparator.comparing(ApplyParamPushUpperLimit::getGoodsLevel)).collect(Collectors.toList()));
                    String pushData = MBUtils.assembleParam(mbConfig, JSONObject.toJSONString(pushUpperLimitMap));
                    mbUtils.pushToMB(MBUtils.getMbUrl(storeApplyParamPrpoBak), pushData);
                } else {
                    Map<String, Object> mbConfig = MBUtils.getMbConfig(storeApplyParamPrpo);
                    mbConfig.put("updatetime", DateUtils.conventDateStrByDate(new Date(), DateUtils.DATE_MDM_PATTERN_MILLISECOND));
                    Map<String, List<ApplyParamPushUpperLimit>> pushUpperLimitMap = new HashMap<>();
                    pushUpperLimitMap.put("detail",pushUpperLimits.values().stream().sorted(Comparator.comparing(ApplyParamPushUpperLimit::getGoodsLevel)).collect(Collectors.toList()));
                    // 增加 compId
                    String pushData = MBUtils.assembleParam(mbConfig, JSONObject.toJSONString(pushUpperLimitMap), Objects.nonNull(orgTreeVO) ? orgTreeVO.getSapcode() : mdmStoreBaseDTOS.get(0).getComId());
                    mbUtils.pushToMB(MBUtils.getMbUrl(storeApplyParamPrpo), pushData);
                }
                return;
            }
            requestBodyDTO = new RequestBodyDTO();
            requestBodyDTO.setAction(ErpSaasActionEnum.PUSH_APPLY_PARAM_GOODS_UPPER_LIMIT_TO_HD.getAction());
            requestBodyDTO.setAppName(Constants.APP_NAME.toLowerCase());
            requestBodyDTO.setBusinessId("99999");
            requestBodyDTO.setBdata(JacksonUtil.packingJsonData("detail", pushUpperLimits.values().stream().sorted(Comparator.comparing(ApplyParamPushUpperLimit::getGoodsLevel)).collect(Collectors.toList())));
            // 取配置
            boolean flag = false;
            if (org.apache.commons.lang.StringUtils.isNotBlank(mbHotReturnProperties)) {
                JSONObject returnJson = JSONObject.parseObject(mbHotReturnProperties);
                flag = returnJson.getBoolean("return");
            }
            // 如果回滚的话, 走原下发海典方法, 参数里不加compId和route就走老url, 否则走新url
            if (!flag) {
                requestBodyDTO.setCompId(Objects.nonNull(orgTreeVO) ? orgTreeVO.getSapcode() : Objects.requireNonNull(mdmStoreBaseDTOS).get(0).getComId());
                requestBodyDTO.setRoute("mb");
            }
        } catch (JsonProcessingException e) {
            logger.error("pushApplyParamGoodsUpperLimitToHd|解析发送数据异常", e);
        } catch (Exception e) {
            logger.error("pushApplyParamGoodsUpperLimitToHd|error!", e);
        }
        try {
            //获取发送给SAP的报文
            logger.info("pushApplyParamGoodsUpperLimitToHd|发送SAP报文给海典,requestBody:{}", requestBodyDTO);
            //发送sap报文
            String result = erpSaasService.accept(requestBodyDTO);
            logger.info("pushApplyParamGoodsUpperLimitToHd|发送SAP报文给海典,result:{}", result);
        } catch (Exception e) {
            logger.error("pushApplyParamGoodsUpperLimitToHd|error!", e);
        }
    }

    private void pushApplyParamGoodsUpperLimitItemToHd(OrgTreeVO orgTreeVO, EmployeeInfoVO employeeInfoVO, List<StoreApplyParamGoodsUpperLimitItem> upperLimitItems,String tempComId,Integer orgType,Byte operationFlag, List<MdmStoreBaseDTO> mdmStoreBaseDTOS, Integer paramScope) {
        logger.debug("请货上限进入 pushApplyParamGoodsUpperLimitItemToHd 方法");
        RequestBodyDTO requestBodyDTO = null;
        try {
//            if (!this.pushAble(orgTreeVO.getId(), ApplyParamCodeEnum.GOODS_UPPER_LIMIT_ITEM)) {
//                logger.info("pushApplyParamGoodsUpperLimitItemToHd|pushAble=false");
//                return;
//            }

            Map<String, ApplyParamPushUpperLimitItem> pushUpperLimitItems = new HashMap<>();
            for (ApplyParamStoreDistrCircleEnum distrCircleEnum : ApplyParamStoreDistrCircleEnum.values()) {
                ApplyParamPushUpperLimitItem pushUpperLimitItem = new ApplyParamPushUpperLimitItem();
                pushUpperLimitItem.setMdmCode(Objects.nonNull(orgTreeVO) ? orgTreeVO.getSapcode() : tempComId);
                pushUpperLimitItem.setStoreDistributionCycle(distrCircleEnum.getCode());
                pushUpperLimitItem.setManualWithinDays(null);
                pushUpperLimitItem.setManualAllowApplyRows(null);
                pushUpperLimitItem.setUrgentWithinDays(null);
                pushUpperLimitItem.setUrgentAllowApplyRows(null);
                pushUpperLimitItem.setExemptionWithinDays(null);
                pushUpperLimitItem.setExemptionAllowApplyRows(null);
                pushUpperLimitItem.setUpdateTime(DateUtils.conventDateStrByDate(new Date(), DateUtils.DATETIME_PATTERN));
                // 增加参数域
                pushUpperLimitItem.setParamScope(null == paramScope ? ApplyUpperLimitScopeEnum.NORMAL.getCode() : paramScope);
                pushUpperLimitItem.setUpdateUser(employeeInfoVO.getEmpCode());
                if (null != orgType && orgType.equals(OrgTypeEnum.ORG_TYPE_STORE_GROUP.getCode())) {
                    List<StoreInfo> storeInfos = new ArrayList<>();
                    for (MdmStoreBaseDTO mdmStoreBaseDTO : mdmStoreBaseDTOS) {
                        StoreInfo storeInfo = new StoreInfo();
                        storeInfo.setStoreCode(mdmStoreBaseDTO.getStoreNo());
                        storeInfo.setFlag(operationFlag);
                        storeInfos.add(storeInfo);
                    }
                    pushUpperLimitItem.setStoreInfo(storeInfos);
                }
                pushUpperLimitItems.put(distrCircleEnum.getCode(), pushUpperLimitItem);
            }
            // 根据配送周期分组
            Map<String, List<StoreApplyParamGoodsUpperLimitItem>> map = upperLimitItems.stream().collect(Collectors.groupingBy(StoreApplyParamGoodsUpperLimitItem::getStoreDistributionCycle));
            map.forEach((k, v) -> {
                ApplyParamPushUpperLimitItem pushUpperLimitItem = new ApplyParamPushUpperLimitItem();
                pushUpperLimitItem.setStoreDistributionCycle(k+"");
                if (null != orgType && orgType.equals(OrgTypeEnum.ORG_TYPE_STORE_GROUP.getCode()) && Constants.HD_PUSH_DEL_DATA_STATUS.equals(operationFlag)) {
                    pushUpperLimitItem.setManualWithinDays(null);
                    pushUpperLimitItem.setManualAllowApplyRows(null);
                    pushUpperLimitItem.setUrgentWithinDays(null);
                    pushUpperLimitItem.setUrgentAllowApplyRows(null);
                    pushUpperLimitItem.setExemptionWithinDays(null);
                    pushUpperLimitItem.setExemptionAllowApplyRows(null);
                }else {
                    pushUpperLimitItem.setManualWithinDays(v.stream().filter(v1 -> Objects.equals(v1.getItemType(), ApplyParamLimitItemTypeEnum.MANUAL.getCode())).findAny().map(StoreApplyParamGoodsUpperLimitItem::getWithinDays).orElse(null));
                    pushUpperLimitItem.setManualAllowApplyRows(v.stream().filter(v1 -> Objects.equals(v1.getItemType(), ApplyParamLimitItemTypeEnum.MANUAL.getCode())).findAny().map(StoreApplyParamGoodsUpperLimitItem::getAllowApplyRows).orElse(null));
                    pushUpperLimitItem.setUrgentWithinDays(v.stream().filter(v1 -> Objects.equals(v1.getItemType(), ApplyParamLimitItemTypeEnum.URGENT.getCode())).findAny().map(StoreApplyParamGoodsUpperLimitItem::getWithinDays).orElse(null));
                    pushUpperLimitItem.setUrgentAllowApplyRows(v.stream().filter(v1 -> Objects.equals(v1.getItemType(), ApplyParamLimitItemTypeEnum.URGENT.getCode())).findAny().map(StoreApplyParamGoodsUpperLimitItem::getAllowApplyRows).orElse(null));
                    pushUpperLimitItem.setExemptionWithinDays(v.stream().filter(v1 -> Objects.equals(v1.getItemType(), ApplyParamLimitItemTypeEnum.EXEMPTION.getCode())).findAny().map(StoreApplyParamGoodsUpperLimitItem::getWithinDays).orElse(null));
                    pushUpperLimitItem.setExemptionAllowApplyRows(v.stream().filter(v1 -> Objects.equals(v1.getItemType(), ApplyParamLimitItemTypeEnum.EXEMPTION.getCode())).findAny().map(StoreApplyParamGoodsUpperLimitItem::getAllowApplyRows).orElse(null));
                }

                pushUpperLimitItem.setMdmCode(Objects.nonNull(orgTreeVO) ? orgTreeVO.getSapcode() : tempComId);
                pushUpperLimitItem.setUpdateUser(employeeInfoVO.getEmpCode());
                pushUpperLimitItem.setUpdateTime(v.get(0).getGmtUpdate());
                // 增加参数域
                pushUpperLimitItem.setParamScope(null == paramScope ? ApplyUpperLimitScopeEnum.NORMAL.getCode() : paramScope);
                pushUpperLimitItems.replace(k, pushUpperLimitItem);
            });
            if (null != orgType && orgType.equals(OrgTypeEnum.ORG_TYPE_STORE_GROUP.getCode())) {
                // 取配置
                boolean flag = false;
                if (org.apache.commons.lang.StringUtils.isNotBlank(mbHotReturnProperties)) {
                    JSONObject returnJson = JSONObject.parseObject(mbHotReturnProperties);
                    flag = returnJson.getBoolean("return");
                }
                // 如果回滚的话, 走原下发海典方法
                if (flag) {
                    Map<String, Object> mbConfig = MBUtils.getMbConfig(pushApplyParamGoodsUpperLimitItemToHdBak);
                    mbConfig.put("updatetime",DateUtils.conventDateStrByDate(new Date(),DateUtils.DATE_MDM_PATTERN_MILLISECOND));
                    Map<String, List<ApplyParamPushUpperLimitItem>> pushUpperLimitItemMap = new HashMap<>();
                    pushUpperLimitItemMap.put("detail",pushUpperLimitItems.values().stream().collect(Collectors.toList()));
                    // 增加 compID by lxc
                    String pushData = MBUtils.assembleParam(mbConfig,JSONObject.toJSONString(pushUpperLimitItemMap));
                    mbUtils.pushToMB(MBUtils.getMbUrl(pushApplyParamGoodsUpperLimitItemToHdBak), pushData);
                } else {
                    Map<String, Object> mbConfig = MBUtils.getMbConfig(pushApplyParamGoodsUpperLimitItemToHd);
                    mbConfig.put("updatetime",DateUtils.conventDateStrByDate(new Date(),DateUtils.DATE_MDM_PATTERN_MILLISECOND));
                    Map<String, List<ApplyParamPushUpperLimitItem>> pushUpperLimitItemMap = new HashMap<>();
                    pushUpperLimitItemMap.put("detail",pushUpperLimitItems.values().stream().collect(Collectors.toList()));
                    // 增加 compID by lxc
                    String pushData = MBUtils.assembleParam(mbConfig,JSONObject.toJSONString(pushUpperLimitItemMap), Objects.nonNull(orgTreeVO) ? orgTreeVO.getSapcode() : tempComId);
                    mbUtils.pushToMB(MBUtils.getMbUrl(pushApplyParamGoodsUpperLimitItemToHd), pushData);
                }
                return;
            }
            requestBodyDTO = new RequestBodyDTO();
            requestBodyDTO.setAction(ErpSaasActionEnum.PUSH_APPLY_PARAM_GOODS_UPPER_LIMIT_ITEM_TO_HD.getAction());
            requestBodyDTO.setAppName(Constants.APP_NAME.toLowerCase());
            requestBodyDTO.setBusinessId("99999");
            requestBodyDTO.setBdata(JacksonUtil.packingJsonData("detail", Lists.newArrayList(pushUpperLimitItems.values())));

            // 取配置
            boolean flag = false;
            if (org.apache.commons.lang.StringUtils.isNotBlank(mbHotReturnProperties)) {
                JSONObject returnJson = JSONObject.parseObject(mbHotReturnProperties);
                flag = returnJson.getBoolean("return");
            }
            // 如果回滚的话, 走原下发海典方法, 参数里不加compId和route就走老url, 否则走新url
            if (!flag) {
                requestBodyDTO.setCompId(Objects.nonNull(orgTreeVO) ? orgTreeVO.getSapcode() : tempComId);
                requestBodyDTO.setRoute("mb");
            }

        } catch (JsonProcessingException e) {
            logger.error("pushApplyParamGoodsUpperLimitItemToHd|解析发送数据异常", e);
        } catch (Exception e) {
            logger.error("pushApplyParamGoodsUpperLimitItemToHd|error!", e);
        }
        try {
            //获取发送给SAP的报文
            logger.info("pushApplyParamGoodsUpperLimitItemToHd|发送SAP报文给海典,requestBody:{}", requestBodyDTO);
            //发送sap报文
            String result = erpSaasService.accept(requestBodyDTO);
            logger.info("pushApplyParamGoodsUpperLimitItemToHd|发送SAP报文给海典,result:{}", result);
        } catch (Exception e) {
            logger.error("pushApplyParamGoodsUpperLimitItemToHd|error!", e);
        }
    }
}

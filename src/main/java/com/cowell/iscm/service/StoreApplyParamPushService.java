package com.cowell.iscm.service;

import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.applyParam.GoodsLevelParamDTO;
import com.cowell.iscm.service.dto.applyParam.StoreApplyParamGoodsUpperLimit;
import com.cowell.permission.dto.mdm.MdmStoreBaseDTO;
import com.cowell.iscm.service.feign.dto.EmployeeInfoVO;
import com.cowell.permission.vo.OrgTreeVO;

import java.util.List;
import java.util.Map;

/**
 * Created by schuangxigang on 2022/6/29 14:32.
 */
public interface StoreApplyParamPushService {
    //指定机构下发
    void pushParams(TokenUserDTO userDTO, Long orgId, Integer orgType, Integer paramType, Integer paramScope);

    //门店组下发
    void pushParams(TokenUserDTO userDTO, Long orgId, Integer orgType, Integer paramType,Byte operationFlag, Integer paramScope);

    void pushApplyParamGoodsUpperLimitToHd(OrgTreeVO orgTreeVO, EmployeeInfoVO employeeInfoVO, List<StoreApplyParamGoodsUpperLimit> upperLimits, List<GoodsLevelParamDTO> goodsLevelParamDTOS,Long orgId,Byte operationFlag);

    /**
     * 根据门店组ID获取法人机构信息
     * @param orgId
     * @return
     */
    public Map<String, List<MdmStoreBaseDTO>> getMdmStoreBaseMap(Long orgId);
}

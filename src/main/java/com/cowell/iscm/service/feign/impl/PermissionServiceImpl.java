package com.cowell.iscm.service.feign.impl;

import com.alibaba.fastjson.JSON;
import com.cowell.cstore.util.LoggerUtil;
import com.cowell.iscm.cache.CacheInstance;
import com.cowell.iscm.cache.OHCDataCacheInstance;
import com.cowell.iscm.config.Constants;
import com.cowell.iscm.config.OHCDataConstants;
import com.cowell.iscm.entity.IscmParamBusinessComidMapping;
import com.cowell.iscm.entity.IscmParamBusinessComidMappingExample;
import com.cowell.iscm.enums.OrgTypeEnum;
import com.cowell.iscm.mapper.IscmParamBusinessComidMappingMapper;
import com.cowell.iscm.rest.errors.BusinessErrorException;
import com.cowell.iscm.rest.errors.ErrorCodeEnum;
import com.cowell.iscm.service.feign.PermissionFeignClient;
import com.cowell.iscm.service.feign.PermissionService;
import com.cowell.iscm.service.feign.ScibFeignClient;
import com.cowell.iscm.service.feign.dto.*;
import com.cowell.iscm.service.feign.dto.EmployeeInfoVO;
import com.cowell.iscm.utils.CommonUtil;
import com.cowell.permission.dto.ChildOrgsDTO;
import com.cowell.permission.dto.EmployeeDetailDTO;
import com.cowell.permission.dto.OrgDTO;
import com.cowell.permission.dto.OrgTreeDTO;
import com.cowell.permission.vo.*;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.cowell.iscm.config.Constants.*;

@Service
public class PermissionServiceImpl implements PermissionService {

    private final Logger logger = LoggerFactory.getLogger(PermissionServiceImpl.class);

    @Autowired
    private PermissionFeignClient permissionFeignClient;

    @Autowired
    private ScibFeignClient scibFeignClient;

    @Autowired
    private CacheInstance cacheInstance;

    @Autowired
    private RedissonClient redissonClient;

    @Value("${iscm.resourceId:42}")
    private Long iscmResourceId;

    @Value("${iscm.comId.mapping.from:iscm}")
    private String mappingFrom;

    // 1-开启缓存 0-关闭
    @Value("${iscm.empcode.cache.able:1}")
    private int empcodeCacheAble;

    // 缓存时间 默认2分钟
    @Value("${iscm.org.info.ohc.time:1800000}")
    private Integer orgInfoOHCTime;

    // 缓存时间 默认2分钟
    @Value("${iscm.parent.org.info.ohc.time:1800000}")
    private Integer parentOrgInfoOHCTime;

    // 缓存时间 默认2分钟
    @Value("${iscm.store_base.info.ohc.time:1800000}")
    private Integer storeBaseInfoOHCTime;

    private static final String ISCM_PERMISSION_CACHE_KEY = "ISCM-PERMISSION-CACHE-KEY-";

    @Autowired
    private IscmParamBusinessComidMappingMapper businessComidMappingMapper;

    @Override
    public OrgVO getOrgInfoById(Long orgId) throws Exception {
        try {
            logger.info("调用权限根据orgId获取组织信息 param -> {}", orgId);
            ResponseEntity<OrgVO> responseEntity = permissionFeignClient.getOrgInfoById(orgId);
            if (responseEntity == null
                    || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                    || null == responseEntity.getBody()) {
                throw new BusinessErrorException("没有获取到组织机构信息");
            }
            return responseEntity.getBody();
        } catch (BusinessErrorException e) {
            logger.warn("调用权限根据orgId获取组织信息失败:", e);
            throw e;
        } catch (Exception e) {
            logger.error("调用权限根据orgId获取组织信息失败:", e);
            throw e;
        }
    }

    @Override
    public List<OrgDTO> listOrgInfoByIds(List<Long> orgIds, boolean isPathName) throws Exception {
        try {
            logger.info("调用权限根据orgIds获取组织信息 param -> {}", JSON.toJSONString(orgIds));
            ResponseEntity<List<OrgDTO>> responseEntity = permissionFeignClient.listOrgInfoById(orgIds, false);
            if (responseEntity == null
                    || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                    || CollectionUtils.isEmpty(responseEntity.getBody())) {
                throw new BusinessErrorException("没有获取到组织机构信息");
            }
            //logger.info("调用权限根据orgIds获取组织信息 response -> {}", JSON.toJSONString(responseEntity.getBody()));
            return responseEntity.getBody();
        } catch (BusinessErrorException e) {
            logger.warn("调用权限根据orgIds获取组织信息失败:", e);
            throw e;
        } catch (Exception e) {
            logger.error("调用权限根据orgId获取组织信息失败", e);
            throw e;
        }
    }

    @Override
    public List<Long> getUserDataOrgInTypesList(Long userId, Long resourceId, int[] types) throws Exception {
        try {
            if (null == resourceId) {
                resourceId = iscmResourceId;
            }
            if (null == types || types.length <= 0) {
                throw new BusinessErrorException("组织类型不能为空");
            }
            logger.info("调用权限获取用户数据权限组织id集合 userId -> {}, resourceId -> {}, types -> {}", userId, resourceId, JSON.toJSONString(types));
            ResponseEntity<List<Long>> responseEntity = permissionFeignClient.getUserDataOrgInTypesList(userId, resourceId, types);
            if (responseEntity == null
                    || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                    || CollectionUtils.isEmpty(responseEntity.getBody())) {
                throw new BusinessErrorException("没有获取到用户数据权限组织id集合");
            }
            //logger.info("调用权限获取用户数据权限组织id集合 response -> {}", JSON.toJSONString(responseEntity.getBody()));
            return responseEntity.getBody();
        } catch (BusinessErrorException e) {
            logger.warn("调用权限获取用户数据权限组织id集合失败:", e);
            throw e;
        } catch (Exception e) {
            logger.error("调用权限获取用户数据权限组织id集合失败:", e);
            throw e;
        }
    }

    @Override
    public List<OrgTreeDTO> listOrgTreesByRootOrgIdAndTypes(Long orgId , int[] types) throws Exception {
        try {
            if (null == types || types.length <= 0) {
                throw new BusinessErrorException("组织类型不能为空");
            }
            logger.info("根据系统权限ID获取系统的菜单树 orgId-> {}, types -> {}", orgId, JSON.toJSONString(types));
            ResponseEntity<List<OrgTreeDTO>> responseEntity = permissionFeignClient.listOrgTreesByRootOrgIdAndTypes(orgId, types);
            if (responseEntity == null
                    || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                    || CollectionUtils.isEmpty(responseEntity.getBody())) {
                throw new BusinessErrorException("没有指定系统权限ID获取系统的菜单树");
            }
            return responseEntity.getBody();
        } catch (BusinessErrorException e) {
            logger.warn("根据系统权限ID获取系统的菜单树失败:", e);
            throw e;
        } catch (Exception e) {
            logger.error("根据系统权限ID获取系统的菜单树失败:", e);
            throw e;
        }
    }

    @Override
    public Map<Long, List<OrgDTO>> listDirectParentOrgByOrgIdBatch(List<Long> orgIds) throws Exception {
        try {
            logger.info("调用权限根据组织机构ID和类型获取组织机构所有直属上级组织，包括该组织机构 param -> {}", JSON.toJSONString(orgIds));

            ResponseEntity<Map<Long, List<OrgDTO>>> responseEntity = permissionFeignClient.listDirectParentOrgByOrgIdBatch(orgIds);
            if (responseEntity == null
                    || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                    || MapUtils.isEmpty(responseEntity.getBody())) {
                throw new BusinessErrorException("没有获取到组织机构所有直属上级组织，包括该组织机构");
            }
            return responseEntity.getBody();
        } catch (BusinessErrorException e) {
            logger.info("调用权限根据组织机构ID和类型获取组织机构所有直属上级组织，包括该组织机构失败:", e);
            throw e;
        } catch (Exception e) {
            logger.error("调用权限根据组织机构ID和类型获取组织机构所有直属上级组织，包括该组织机构失败:", e);
            throw e;
        }
    }

    @Override
    public Map<Long, List<OrgDTOSimple>> listDirectParentOrgByOrgIdBatchCache(List<Long> orgIds) throws Exception {
        LoggerUtil.info("根据组织机构ID和类型获取组织机构所有直属上级组织，包括该组织机构-缓存查询开始");
        if (CollectionUtils.isEmpty(orgIds)) {
            return new HashMap<>();
        }
        Map<Long, List<OrgDTOSimple>> resultMap = Maps.newHashMap();
        List<Long> unCacheOrgIds = Lists.newArrayList();
        for (Long orgId : orgIds) {
            String key = OHCDataConstants.PARENT_ORG_INFO_CACHE_KEY_PREFIX + ":" + orgId;
            String orgDtoStr = OHCDataCacheInstance.parentOrgInfoOHCDataCache.getCacheStrValue(key);
            if (StringUtils.isBlank(orgDtoStr)) {
                unCacheOrgIds.add(orgId);
                continue;
            }
            resultMap.put(orgId, com.cowell.cstore.util.CommonUtil.parseArray(orgDtoStr, OrgDTOSimple.class));
        }
        if (CollectionUtils.isEmpty(unCacheOrgIds)) {
            return resultMap;
        }
        LoggerUtil.info("根据组织机构ID和类型获取组织机构所有直属上级组织，包括该组织机构-缓存查询-查询接口：{}", unCacheOrgIds);
        Map<Long, List<OrgDTO>> orgDTOMap = listDirectParentOrgByOrgIdBatch(unCacheOrgIds);
        if(Objects.nonNull(orgDTOMap)){
            orgDTOMap.forEach((k,v)->{
                List<OrgDTOSimple> orgDTOSimples = new ArrayList<>();
                v.stream().filter(x->Objects.equals(OrgTypeEnum.ORG_TYPE_BUSINESS.getCode(),x.getType())).forEach(x->orgDTOSimples.add(new OrgDTOSimple(x.getId(),x.getType())));
                resultMap.put(k,orgDTOSimples);

                String key = OHCDataConstants.PARENT_ORG_INFO_CACHE_KEY_PREFIX + ":" + k;
                OHCDataCacheInstance.parentOrgInfoOHCDataCache.setCache(key, orgDTOSimples, parentOrgInfoOHCTime);
            });
        }
        return resultMap;
    }

    @Override
    public List<OrgTreeVO> listUserDataScopeTreesByTypes(Long userId, Long resourceId, List<Integer> types, Integer rootOrgType) throws Exception {
        try {
            if (null == resourceId) {
                resourceId = iscmResourceId;
            }
            logger.info("调用权限根据类型查询用户数据范围的组织机构树,指定类型组成的阉割版的树 userId -> {}, resourceId -> {}, types -> {}, rootOrgType -> {}", userId, resourceId, JSON.toJSONString(types), rootOrgType);
            String listUserDataScopeTreesByTypesKey = null;
            try {
                String paramsMd5Key = CommonUtil.md5(userId, resourceId, types, rootOrgType);
                if (StringUtils.isNotBlank(paramsMd5Key)) {
                    listUserDataScopeTreesByTypesKey = ISCM_PERMISSION_CACHE_KEY + paramsMd5Key;
                    List<OrgTreeVO> orgTreeCache = cacheInstance.getValue(listUserDataScopeTreesByTypesKey);
                    if (null != orgTreeCache) {
                        logger.info("调用权限根据类型查询用户数据范围的组织机构树,指定类型组成的阉割版的树 userId -> {}, resourceId -> {}, types -> {}, rootOrgType -> {} 取缓存成功 orgTreeCache.size={}", userId, resourceId, JSON.toJSONString(types), rootOrgType, orgTreeCache.size());
                        return orgTreeCache;
                    }
                }
            } catch (Exception e) {
                logger.warn("调用权限根据类型查询用户数据范围的组织机构树 get permissionCache Exception-> {}, resourceId -> {}, types -> {}, rootOrgType -> {} e={}", userId, resourceId, JSON.toJSONString(types), rootOrgType, e);
            }

            ResponseEntity<List<OrgTreeVO>> responseEntity = permissionFeignClient.listUserDataScopeTreesByTypes(userId, resourceId, types, rootOrgType);
            if (responseEntity == null
                    || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                    || CollectionUtils.isEmpty(responseEntity.getBody())) {
                throw new BusinessErrorException("没有获取到用户数据范围的组织机构树,指定类型组成的阉割版的树");
            }
            if (StringUtils.isNotBlank(listUserDataScopeTreesByTypesKey)) {
                try {
                    cacheInstance.putValue(listUserDataScopeTreesByTypesKey, responseEntity.getBody());
                } catch (Exception e) {
                    logger.warn("调用权限根据类型查询用户数据范围的组织机构树 put permissionCache Exception-> {}, resourceId -> {}, types -> {}, rootOrgType -> {} e={}", userId, resourceId, JSON.toJSONString(types), rootOrgType, e);
                }
            }
            return responseEntity.getBody();
        } catch (BusinessErrorException e) {
            logger.warn("调用权限根据类型查询用户数据范围的组织机构树,指定类型组成的阉割版的树失败:", e);
            throw e;
        } catch (Exception e) {
            logger.error("调用权限根据类型查询用户数据范围的组织机构树,指定类型组成的阉割版的树失败:", e);
            throw e;
        }
    }

    @Override
    public List<OrgVO> queryOrgInfoBySapCodes(List<String> sapCodes) {
        try {
            logger.info("调用权限根据SapCode批量查询组织信息 sapCodes -> {}", JSON.toJSONString(sapCodes));
            ResponseEntity<List<OrgVO>> responseEntity = permissionFeignClient.queryOrgInfoBySapCodes(sapCodes.toArray(new String[]{}));
            if (responseEntity == null
                    || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))) {
                throw new BusinessErrorException("没有获取到组织信息");
            }
            return responseEntity.getBody();
        } catch (BusinessErrorException e) {
            logger.warn("调用权限根据SapCode批量查询组织信息失败:", e);
            throw e;
        } catch (Exception e) {
            logger.error("调用权限根据SapCode批量查询组织信息失败:", e);
            throw e;
        }
    }

    @Override
    public List<OrgVO> queryOrgInfoBySapCodes(List<String> sapCodes, int type) {
        try {
            List<OrgVO> orgVOS = queryOrgInfoBySapCodes(sapCodes);
            if (CollectionUtils.isNotEmpty(orgVOS)) {
                return orgVOS.stream().filter(a -> null != a.getType() && a.getType().equals(type)).collect(Collectors.toList());
            }
            return orgVOS;
        } catch (BusinessErrorException e) {
            logger.warn("调用权限根据SapCode批量查询组织信息失败:", e);
            throw e;
        } catch (Exception e) {
            logger.error("调用权限根据SapCode批量查询组织信息失败:", e);
            throw e;
        }
    }

    /**
     * 功    能：根据SapCode批量查询组织信息 全部级别-本地缓存
     * 作    者：苏涛
     * 时    间：2024-08-07
     */
    @Override
    public List<OrgVO> queryOrgInfoBySapCodesCache(List<String> sapCodes) {
        if (CollectionUtils.isEmpty(sapCodes)) {
            return new ArrayList<>();
        }
        List<OrgVO> resultList = Lists.newArrayList();
        List<String> unCacheSapCodes = Lists.newArrayList();
        for (String sapCode : sapCodes) {
            String key = OHCDataConstants.ORG_INFO_CACHE_KEY_PREFIX + ":" + sapCode;
            OrgVO orgVO = OHCDataCacheInstance.orgInfoOHCDataCache.getCache(key, OrgVO.class);
            if (Objects.isNull(orgVO)) {
                unCacheSapCodes.add(sapCode);
                continue;
            }
            resultList.add(orgVO);
        }
        if (CollectionUtils.isEmpty(unCacheSapCodes)) {
            return resultList;
        }
        List<OrgVO> orgInfoList = queryOrgInfoBySapCodes(unCacheSapCodes);
        resultList.addAll(orgInfoList);
        orgInfoList.forEach(orgInfo -> {
            String key = OHCDataConstants.ORG_INFO_CACHE_KEY_PREFIX + ":" + orgInfo.getSapcode();
            OHCDataCacheInstance.orgInfoOHCDataCache.setCache(key, orgInfo, orgInfoOHCTime);
        });
        return resultList;
    }


    public List<OrgVO> queryOrgInfoBySapCodesCache(List<String> sapCodes, int type) {
        try {
            LoggerUtil.info("调用权限根据SapCode批量查询组织信息缓存开始 sapCodes -> {}", sapCodes);
            List<OrgVO> orgVOS = queryOrgInfoBySapCodesCache(sapCodes);
            if (CollectionUtils.isNotEmpty(orgVOS)) {
                return orgVOS.stream().filter(a -> null != a.getType() && a.getType().equals(type)).collect(Collectors.toList());
            }
            return orgVOS;
        } catch (BusinessErrorException e) {
            logger.warn("调用权限根据SapCode批量查询组织信息失败:", e);
            throw e;
        } catch (Exception e) {
            logger.error("调用权限根据SapCode批量查询组织信息失败:", e);
            throw e;
        }
    }

    @Override
    public List<OrgDTO> getUserDataScopeStoreBatch(Long userId, Long resourceId, Long[] businessIdList, Integer idType) {
        try {
            if (null == resourceId) {
                resourceId = iscmResourceId;
            }
            String redisKey = userId.toString() + "-" + Arrays.stream(businessIdList).map(String::valueOf).collect(Collectors.joining("|"));
            RBucket<List<OrgDTO>> bucket = redissonClient.getBucket(redisKey);
            if (Objects.nonNull(bucket) && CollectionUtils.isNotEmpty(bucket.get())) {
                return bucket.get();
            } else {
                logger.info("调用权限获取用户在某组织下有权限的门店 userId -> {}, resourceId -> {}, businessIdList -> {}, idType -> {}", userId.toString(), resourceId.toString(), JSON.toJSONString(businessIdList), idType.toString());
                ResponseEntity<List<OrgDTO>> responseEntity = permissionFeignClient.getUserDataScopeStoreBatch(userId, resourceId, businessIdList, idType);
                if (responseEntity == null
                        || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))) {
                    throw new BusinessErrorException("没有获取到门店信息");
                }
                bucket.set( responseEntity.getBody(), 10L, TimeUnit.MINUTES);
            }
            return bucket.get();
        } catch (BusinessErrorException e) {
            logger.warn("调用权限获取用户在某组织下有权限的门店失败:", e);
            throw e;
        } catch (Exception e) {
            logger.error("调用权限获取用户在某组织下有权限的门店失败:", e);
            throw e;
        }
    }

    @Override
    public List<OrgDTO> listDirectParentOrgByOrgId(Long orgId) throws Exception {
        try {
            ResponseEntity<List<OrgDTO>> responseEntity = permissionFeignClient.listDirectParentOrgByOrgId(orgId);
            if (responseEntity == null
                    || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))) {
                throw new BusinessErrorException("没有获取到组织机构所有直属上级组织，包括该组织机构");
            }
            //logger.info("调用权限获取用户在某组织下有权限的门店 response -> {}", JSON.toJSONString(responseEntity.getBody()));
            return responseEntity.getBody() == null ? new ArrayList<>() : responseEntity.getBody();
        } catch (BusinessErrorException e) {
            logger.warn("调用权限根据组织机构ID和类型获取组织机构所有直属上级组织，包括该组织机构失败:", e);
            throw e;
        } catch (Exception e) {
            logger.error("调用权限根据组织机构ID和类型获取组织机构所有直属上级组织，包括该组织机构失败:", e);
            throw e;
        }
    }

    @Override
    public List<OrgTreeVO> getAbsScopeOrgTrees(Long userId, Long resourceId, List<Integer> types) throws Exception {
        try {
            ResponseEntity<List<OrgTreeVO>> responseEntity = permissionFeignClient.getAbsScopeOrgTrees(userId, resourceId == null ? iscmResourceId : resourceId, types);
            if (responseEntity == null
                    || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                    || CollectionUtils.isEmpty(responseEntity.getBody())) {
                throw new BusinessErrorException("没有获取到用户权限");
            }
            //logger.info("调用权限获取用户权限的阉割版树 response -> {}", JSON.toJSONString(responseEntity.getBody()));
            return responseEntity.getBody();
        } catch (BusinessErrorException e) {
            logger.warn("调用权限获取用户权限的阉割版树失败:", e);
            throw e;
        } catch (Exception e) {
            logger.error("调用权限获取用户权限的阉割版树失败:", e);
            throw e;
        }
    }

    @Override
    public List<OrgTreeDTO> listUserDataScopeTreesByRootOrgType(Long userId, Long resourceId, Integer type) {
        try {
            ResponseEntity<List<OrgTreeDTO>> responseEntity = permissionFeignClient.listUserDataScopeTreesByRootOrgType(userId, resourceId == null ? iscmResourceId : resourceId, type);
            if (responseEntity == null
                    || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                    || CollectionUtils.isEmpty(responseEntity.getBody())) {
                throw new BusinessErrorException("没有根据类型获取到户权限树");
            }
            //logger.info("调用权限根据类型查询用户数据范围的组织机构树 response -> {}", JSON.toJSONString(responseEntity.getBody()));
            return responseEntity.getBody();
        } catch (BusinessErrorException e) {
            logger.warn("调用权限根据类型查询用户数据范围的组织机构树失败:", e);
            throw e;
        } catch (Exception e) {
            logger.error("调用权限根据类型查询用户数据范围的组织机构树失败:", e);
            throw e;
        }
    }

    @Override
    public List<ResourceTreeVO> getMenuTreeByParam(Long resourceId) throws Exception {
        try {
            ResponseEntity<List<ResourceTreeVO>> responseEntity = permissionFeignClient.getMenuTreeByParam(resourceId == null ? iscmResourceId : resourceId);
            if (responseEntity == null
                    || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))) {
                throw new BusinessErrorException("没有根据系统权限ID获取到系统的菜单树");
            }
            //logger.info("调用权限根据系统权限ID获取系统的菜单树 response -> {}", JSON.toJSONString(responseEntity.getBody()));
            return responseEntity.getBody();
        } catch (BusinessErrorException e) {
            logger.warn("调用权限根据系统权限ID获取系统的菜单树失败:", e);
            throw e;
        } catch (Exception e) {
            logger.error("调用权限根据系统权限ID获取系统的菜单树失败:", e);
            throw e;
        }
    }

    @Override
    public List<Long> queryUserIdsByRoles(List<String> roleCodes) throws Exception {
        try {
            if (CollectionUtils.isEmpty(roleCodes)) {
                throw new BusinessErrorException("角色编码不能为空");
            }
            List<Long> userIds = new ArrayList<>();
            QueryEmployeeDTO queryEmployeeDTO = new QueryEmployeeDTO();
            queryEmployeeDTO.setRoleCodes(roleCodes.toArray(new String[0]));
            for (int i = 0; ; i++) {
                if (i == 0) {
                    queryEmployeeDTO.setPage(i);
                    queryEmployeeDTO.setSize(100);
                }
                List<EmployeeDetailDTO> list = getEmployeeDetails(queryEmployeeDTO);
                if (CollectionUtils.isEmpty(list)) {
                    break;
                }
                userIds.addAll(list.stream().map(EmployeeDetailDTO::getUserId).distinct().collect(Collectors.toList()));
                if (queryEmployeeDTO.getSize() > list.size()) {
                    break;
                }
                queryEmployeeDTO.setPage(i + 1);
            }
            return userIds.stream().distinct().collect(Collectors.toList());
        } catch (BusinessErrorException e) {
            logger.warn("调用权限根据角色组织名字手机号员工编码 批量查询员工失败:", e);
            throw e;
        } catch (Exception e) {
            logger.error("调用权限根据角色组织名字手机号员工编码 批量查询员工失败:", e);
            throw e;
        }
    }

    @Override
    public List<EmployeeDetailDTO> queryUserIdsByRolesAndOrgIds(List<String> roleCodes, List<Long> orgIds) throws Exception {
        try {
            if (CollectionUtils.isEmpty(roleCodes)) {
                throw new BusinessErrorException("角色编码不能为空");
            }
            List<EmployeeDetailDTO> employeeDetailDTOS = new ArrayList<>(roleCodes.size());
            QueryEmployeeDTO queryEmployeeDTO = new QueryEmployeeDTO();
            queryEmployeeDTO.setRoleCodes(roleCodes.toArray(new String[0]));
            queryEmployeeDTO.setOrgIds(orgIds);
            for (int i = 0; ; i++) {
                if (i == 0) {
                    queryEmployeeDTO.setPage(i);
                    queryEmployeeDTO.setSize(100);
                }
                List<EmployeeDetailDTO> list = getEmployeeDetails(queryEmployeeDTO);
                if (CollectionUtils.isEmpty(list)) {
                    break;
                }
                employeeDetailDTOS.addAll(list.stream().filter(v -> CollectionUtils.isNotEmpty(v.getEmployeeOrgAndTitleVOS())).collect(Collectors.toList()));
                if (queryEmployeeDTO.getSize() > list.size()) {
                    break;
                }
                queryEmployeeDTO.setPage(i + 1);
            }
            if (CollectionUtils.isEmpty(employeeDetailDTOS)) {
                throw new BusinessErrorException("批量查询员工失败 人员没有数据权限");
            }
            logger.info("调用权限根据角色组织名字手机号员工编码 批量查询员工完毕 response -> {}", JSON.toJSONString(employeeDetailDTOS));
            return employeeDetailDTOS;
        } catch (BusinessErrorException e) {
            logger.warn("调用权限根据角色组织名字手机号员工编码 批量查询员工失败:", e);
            throw e;
        } catch (Exception e) {
            logger.error("调用权限根据角色组织名字手机号员工编码 批量查询员工失败:", e);
            throw e;
        }
    }

    private List<EmployeeDetailDTO> getEmployeeDetails(QueryEmployeeDTO queryEmployeeDTO) {
        logger.info("开始调用权限根据角色组织名字手机号员工编码 批量查询员工 queryEmployeeDTO -> {}", JSON.toJSONString(queryEmployeeDTO));
        ResponseEntity<PageInfo<EmployeeDetailDTO>> responseEntity = permissionFeignClient.queryEmployeeBatch(queryEmployeeDTO);
        if (responseEntity == null
                || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                || null == responseEntity.getBody()) {
            throw new BusinessErrorException("批量查询员工失败");
        }
        return responseEntity.getBody().getList();
    }

    @Override
    public Map<Long, List<OrgDTO>> getBatchUserScopeOrgByType(List<Long> userIds, Long resourceId, Integer type) {
        try {
            Map<Long, List<OrgDTO>> result = new HashMap<>();
            // 志勇说量太大,只支持50一次查询
            Lists.partition(userIds, 50).forEach(v -> {
                logger.info("开始调用权限批量获取用户权限范围内的指定类型组织 userIds -> {}, resourceId -> {}, type -> {}", userIds, resourceId == null ? iscmResourceId : resourceId, type);
                ResponseEntity<Map<Long, List<OrgDTO>>> responseEntity = permissionFeignClient.getBatchUserScopeOrgByType(v, resourceId == null ? iscmResourceId : resourceId, type);
                logger.info("开始调用权限批量获取用户权限范围内的指定类型组织 response -> {}", JSON.toJSONString(responseEntity));
                if (responseEntity == null
                        || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                        || MapUtils.isEmpty(responseEntity.getBody())) {
                    throw new BusinessErrorException("调用权限批量获取用户权限范围内的指定类型组织失败");
                }
                Map<Long, List<OrgDTO>> body = responseEntity.getBody();
                if (MapUtils.isNotEmpty(body)) {
                    result.putAll(body);
                }
            });
            return result;
        } catch (BusinessErrorException e) {
            logger.warn("调用权限批量获取用户权限范围内的指定类型组织失败:", e);
            throw e;
        } catch (Exception e) {
            logger.error("调用权限批量获取用户权限范围内的指定类型组织失败:", e);
            throw e;
        }
    }

    @Override
    public List<EmployeeInfoVO> getEmployeeInfoByUserIdList(List<Long> userIds) {
        try {
            EmployeeIdListVO employeeIdListVO = new EmployeeIdListVO();
            employeeIdListVO.setUserIds(userIds);
            logger.info("开始调用权限批量根据用户id集合查询用户信息 employeeIdListVO -> {}", JSON.toJSONString(employeeIdListVO));
            ResponseEntity<List<EmployeeInfoVO>> responseEntity = permissionFeignClient.getEmployeeInfoByUserIdList(employeeIdListVO);
            logger.info("开始调用权限批量根据用户id集合查询用户信息 response -> {}", JSON.toJSONString(responseEntity));
            if (responseEntity == null
                    || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                    || Objects.isNull(responseEntity.getBody())) {
                throw new BusinessErrorException("根据用户id集合查询用户信息失败");
            }
            return responseEntity.getBody();
        } catch (BusinessErrorException e) {
            logger.warn("根据用户id集合查询用户信息失败:", e);
            throw e;
        } catch (Exception e) {
            logger.error("根据用户id集合查询用户信息失败:", e);
            throw e;
        }
    }

    @Override
    public List<OrgTreeVO> getUserDataScopeOrgTrees(Long userId, OrgTypeEnum orgTypeEnum) {
        if (null == userId || null == orgTypeEnum) {
            return Collections.emptyList();
        }
        List<OrgTreeVO> orgTreeVOS;
        try {
            orgTreeVOS = listUserDataScopeTreesByTypes(userId, null, Lists.newArrayList(orgTypeEnum.getCode()), orgTypeEnum.getCode());
        } catch (Exception e) {
            throw new BusinessErrorException(ErrorCodeEnum.GET_PERM_ERROR);
        }
        if (CollectionUtils.isEmpty(orgTreeVOS)) {
            throw new BusinessErrorException("对不起,您没有"+orgTypeEnum.getName()+"数据权限");
        }
        return orgTreeVOS;
    }

    @Override
    public List<OrgTreeVO> getUserDataScopeOrgTrees(Long userId, List<Integer> orgTypes, Integer rootOrgType) {
        if (null == userId || null == rootOrgType || CollectionUtils.isEmpty(orgTypes)) {
            return Collections.emptyList();
        }
        List<OrgTreeVO> orgTreeVOS;
        try {
            orgTreeVOS = listUserDataScopeTreesByTypes(userId, null, orgTypes, rootOrgType);
        } catch (Exception e) {
            throw new BusinessErrorException(ErrorCodeEnum.GET_PERM_ERROR);
        }
        if (CollectionUtils.isEmpty(orgTreeVOS)) {
            throw new BusinessErrorException("对不起,您没有用户数据权限");
        }
        return orgTreeVOS;
    }

    @Override
    public List<ChildOrgsDTO> listChildOrgAssignedType(List<Long> ids, Integer type) {
        try {
            logger.info("根据上级组织机构ID和类型获取组织机构下所有该类型的组织机构列表 -> ids={},type={}", ids, type);
            ResponseEntity<List<ChildOrgsDTO>> responseEntity = permissionFeignClient.listChildOrgAssignedType(ids, type);
            if (responseEntity == null
                    || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                    || CollectionUtils.isEmpty(responseEntity.getBody())) {
                throw new BusinessErrorException("根据上级组织机构ID和类型获取组织机构下所有该类型的组织机构列表失败");
            }
            return responseEntity.getBody();
        } catch (BusinessErrorException e) {
            logger.warn("根据上级组织机构ID和类型获取组织机构下所有该类型的组织机构列表失败:", e);
            throw e;
        } catch (Exception e) {
            logger.error("根据上级组织机构ID和类型获取组织机构下所有该类型的组织机构列表失败:", e);
            throw new BusinessErrorException(ErrorCodeEnum.GET_PERM_ERROR);
        }
    }

    @Override
    public Map<String, List<OrgDTO>> getAncestorsOrgList(List<String> sapCodes, Integer orgType) {
        try {
            logger.info("getAncestorsOrgList|sapCodes -> {}, orgType -> {}", sapCodes, orgType);
            List<OrgVO> orgVOS = queryOrgInfoBySapCodes(sapCodes, orgType);
            List<Long> orgIds = orgVOS.stream().map(OrgVO::getId).collect(Collectors.toList());
            Map<Long, String> orgIdCodeMap = orgVOS.stream().collect(Collectors.toMap(OrgVO::getId, OrgVO::getSapcode, (k1, k2) -> k1));
            Map<Long, List<OrgDTO>> orgIdMap = new HashMap<>();
            Map<String, List<OrgDTO>> orgCodeMap = new HashMap<>();
            Lists.partition(orgIds, MAX_PAGE_SIZE).forEach(subList -> {
                try {
                    orgIdMap.putAll(this.listDirectParentOrgByOrgIdBatch(subList));
                } catch (Exception e) {
                    logger.warn("getAncestorsOrgList|error!", e);
                }
            });
            if (MapUtils.isNotEmpty(orgIdMap)) {
                orgIdMap.forEach((k, v) -> orgCodeMap.put(orgIdCodeMap.get(k), v));
                return orgCodeMap;
            }
            return new HashMap<>();
        } catch (Exception e) {
            logger.warn("getAncestorsOrgList|error!", e);
            throw e;
        }
    }

    @Override
    public Map<Long, List<OrgDTO>> getAncestorsOrgList(List<Long> orgIds) {
        try {
            logger.info("getAncestorsOrgList|orgIds -> {}", orgIds);
            Map<Long, List<OrgDTO>> orgMap = new HashMap<>();
            Lists.partition(orgIds, MAX_PAGE_SIZE).forEach(subList -> {
                try {
                    orgMap.putAll(this.listDirectParentOrgByOrgIdBatch(subList));
                } catch (Exception e) {
                    logger.warn("getAncestorsOrgList|error!", e);
                }
            });
            return orgMap;
        } catch (Exception e) {
            logger.warn("getAncestorsOrgList|error!", e);
            throw e;
        }
    }

    @Override
    public List<OrgDTO> getAncestorsOrgList(Long orgId) {
        try {
            logger.info("getAncestorsOrgList|orgId -> {}", orgId);
            return this.listDirectParentOrgByOrgId(orgId);
        } catch (Exception e) {
            logger.warn("getAncestorsOrgList|error!", e);
            throw new BusinessErrorException(ErrorCodeEnum.GET_PERM_ERROR);
        }
    }

    @Override
    public List<EmployeeInfoVO> getListByEmpCodesAndStatus(List<String> empCodes) {
        try {
            EmpCodeListAndStatusVO param = new EmpCodeListAndStatusVO();
            param.setStatusList(Lists.newArrayList(0));
            param.setEmpCodes(empCodes);
            logger.info("开始调用权限根据用户empCodes和状态查询用户信息 empCodes -> {}", JSON.toJSONString(param));
            ResponseEntity<List<EmployeeInfoVO>> responseEntity = permissionFeignClient.getListByEmpCodesAndStatus(param);
            if (responseEntity == null
                    || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                    || responseEntity.getBody() == null) {
                throw new BusinessErrorException("调用权限根据用户empCodes和状态查询用户信息失败");
            }
            return responseEntity.getBody();
        } catch (BusinessErrorException e) {
            logger.warn("调用权根据用户empCodes和状态查询用户信息失败:", e);
            throw e;
        } catch (Exception e) {
            logger.error("调用权限根据用户empCodes和状态查询用户信息失败:", e);
            throw e;
        }
    }

    @Override
    public List<OrgSimpleDTO> getUserDataScopeChildOrgByOrgId(Long userId, List<Long> orgIds, boolean isRecursion) {
        try {
            int isRecurs = isRecursion ? 1 : 0;
            logger.info("开始调用权限获取用户是否有权限访问指定orgId下的所有子级组织 userId:{},resourceId:{},orgIds:{},isRecursion:{}",userId, iscmResourceId, JSON.toJSONString(orgIds), isRecurs);
            ResponseEntity<List<OrgSimpleDTO>> responseEntity = permissionFeignClient.getUserDataScopeChildOrgByOrgId(userId, iscmResourceId, orgIds, isRecurs);
            if (responseEntity == null
                    || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                    || responseEntity.getBody() == null) {
                throw new BusinessErrorException("调用权限获取用户是否有权限访问指定orgId下的所有子级组织失败");
            }
            return responseEntity.getBody();
        } catch (BusinessErrorException e) {
            logger.warn("调用权限获取用户是否有权限访问指定orgId下的所有子级组织失败:", e);
            throw e;
        } catch (Exception e) {
            logger.error("调用权获取用户是否有权限访问指定orgId下的所有子级组织失败:", e);
            throw e;
        }
    }

    @Override
    public List<OrgTreeSimpleDTO> listUserDataScopeTreesByOrgIdAndTypes(Long userId, Long orgId, List<Integer> types) {
        try {
            logger.info("开始调用权限获取指定组织下用户有数据权限的树并根据组织类型过滤 userId:{},resourceId:{},orgIds:{},isRecursion:{}",userId, iscmResourceId, orgId, JSON.toJSONString(types));
            ResponseEntity<List<OrgTreeSimpleDTO>> responseEntity = permissionFeignClient.listUserDataScopeTreesByOrgIdAndTypes(userId, iscmResourceId, orgId, types);
            if (responseEntity == null
                    || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                    || responseEntity.getBody() == null) {
                throw new BusinessErrorException("调用权限获取指定组织下用户有数据权限的树并根据组织类型过滤失败");
            }
            return responseEntity.getBody();
        } catch (BusinessErrorException e) {
            logger.warn("调用权限获取指定组织下用户有数据权限的树并根据组织类型过滤失败:", e);
            throw e;
        } catch (Exception e) {
            logger.error("调用权限获取指定组织下用户有数据权限的树并根据组织类型过滤失败:", e);
            throw e;
        }
    }

    @Override
    public Map<Long, List<OrgVO>> listLegalOrgInfoByIds(List<Long> orgIds) {
        if (mappingFrom.equals(Constants.APP_NAME)){
            Map<Long, List<OrgVO>> result = new HashMap<>();
            try {
                logger.info("取Mapping管理树组织ID获取法人树组织信息 orgIds:{}", JSON.toJSONString(orgIds));
                IscmParamBusinessComidMappingExample example = new IscmParamBusinessComidMappingExample();
                example.createCriteria().andBusinessOrgIdIn(orgIds).andStatusEqualTo(NORMAL_STATUS);
                List<IscmParamBusinessComidMapping> iscmParamBusinessComidMappings = businessComidMappingMapper.selectByExample(example);
                for (IscmParamBusinessComidMapping mapping : iscmParamBusinessComidMappings) {
                    ArrayList<OrgVO> LegalOrg = Lists.newArrayList();
                    if(StringUtils.isNotBlank(mapping.getComids())){
                        Arrays.stream(mapping.getComids().split(",")).forEach(v->{
                            OrgVO vo =  new OrgVO();
                            vo.setSapcode(v);
                            vo.setName(mapping.getBusinessName());
                            vo.setShortName(mapping.getBusinessName());
                            LegalOrg.add(vo);
                        });
                        result.put(mapping.getBusinessOrgId(), LegalOrg);
                        logger.info("取Mapping管理树组织ID获取法人树组织信息 mapping.getBusinessOrgId():{} LegalOrg={}",mapping.getBusinessOrgId(), JSON.toJSONString(LegalOrg));
                    }
                }
                return result;
            } catch (BusinessErrorException e) {
                logger.warn("取Mapping管理树组织ID获取法人树组织信息失败:", e);
                throw e;
            } catch (Exception e) {
                logger.error("取Mapping管理树组织ID获取法人树组织信息失败:", e);
                throw e;
            }
        }else {
            try {
                logger.info("调用权限通过管理树组织ID获取法人树组织信息 orgIds:{}", JSON.toJSONString(orgIds));
                ResponseEntity<Map<Long, List<OrgVO>>> responseEntity = permissionFeignClient.listLegalOrgInfoByIds(orgIds);
                if (responseEntity == null
                        || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                        || responseEntity.getBody() == null) {
                    throw new BusinessErrorException("调用权限通过管理树组织ID获取法人树组织信息失败");
                }
                return responseEntity.getBody();
            } catch (BusinessErrorException e) {
                logger.warn("调用权限通过管理树组织ID获取法人树组织信息失败:", e);
                throw e;
            } catch (Exception e) {
                logger.error("调用权限通过管理树组织ID获取法人树组织信息失败:", e);
                throw e;
            }
        }
    }

    //2025-08-14 权限中台暂时不准 不给调用这个了  统一走 listLegalOrgInfoByIds 这个
    @Override
    public Map<String, List<OrgVO>> listLegalOrgInfoBySapCodes(List<String> sapCodes) {
//        try {
//            logger.info("调用权限通过管理树组织ID获取法人树组织信息 sapCodes:{}", JSON.toJSONString(sapCodes));
//            ResponseEntity<Map<String, List<OrgVO>>> responseEntity = permissionFeignClient.listLegalOrgInfoBySapCodes(sapCodes);
//            if (responseEntity == null
//                    || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
//                    || responseEntity.getBody() == null) {
//                throw new BusinessErrorException("调用权限通过管理树连锁编码获取法人树组织信息失败");
//            }
//            return responseEntity.getBody();
//        } catch (BusinessErrorException e) {
//            logger.warn("调用权限通过管理树连锁编码获取法人树组织信息失败:", e);
//            throw e;
//        } catch (Exception e) {
//            logger.error("调用权限通过管理树连锁编码获取法人树组织信息失败:", e);
//            throw e;
//        }
        return new HashMap<>();
    }

    @Override
    public List<OrgDTO> listOrgByOutId(Integer type, List<Long> outIds) throws Exception {
        try {
            logger.info("调用权限根据组织机构outId获取组织机构集合 type:{},outIds:{}", type, JSON.toJSONString(outIds));
            ResponseEntity<List<OrgDTO>> responseEntity = permissionFeignClient.listOrgByOutId(type, outIds);
            if (responseEntity == null
                    || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                    || responseEntity.getBody() == null) {
                throw new BusinessErrorException("调用权限根据组织机构outId获取组织机构集合失败");
            }
            return CollectionUtils.isNotEmpty(responseEntity.getBody()) ? responseEntity.getBody() : new ArrayList<>();
        } catch (BusinessErrorException e) {
            logger.warn("调用权限根据组织机构outId获取组织机构集合失败:", e);
            throw e;
        } catch (Exception e) {
            logger.error("调用权限根据组织机构outId获取组织机构集合失败:", e);
            throw e;
        }

    }
    /**
     * 根据 storeOrgIdList 查询缓存 返回结构信息含上级连锁和平台
     * @param storeOrgIds
     * @return
     */
    @Override
    public List<OrgInfoBaseCache> getOrgInfoBaseCacheListByStoreOrgIds(List<Long> storeOrgIds) {
        try {
            logger.info("调用Scib根据组织机构orgId获取组织机构集合 storeOrgIds:{}", storeOrgIds);
            storeOrgIds = storeOrgIds.stream().distinct().collect(Collectors.toList());
            List<OrgInfoBaseCache> result=new ArrayList<>();
            Lists.partition(storeOrgIds,MAX_PAGE_SIZE).stream().forEach(v->{
                CommonOrgCacheDTO commonOrgCacheDTO=new CommonOrgCacheDTO();
                commonOrgCacheDTO.setOrgIdList(v);
                commonOrgCacheDTO.setType(OrgTypeEnum.ORG_TYPE_STORE.getCode());
                ResponseEntity<List<OrgInfoBaseCache>> responseEntity = scibFeignClient.getOrgBaseCacheListByOutIdList(commonOrgCacheDTO);
                if (responseEntity == null
                        || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                        || responseEntity.getBody() == null) {
                    throw new BusinessErrorException("调用Scib根据组织机构orgId获取组织机构集合失败");
                }
                result.addAll(CollectionUtils.isNotEmpty(responseEntity.getBody()) ? responseEntity.getBody() : new ArrayList<>());
            });
            return result;
        } catch (BusinessErrorException e) {
            logger.warn("调用Scib根据组织机构orgId获取组织机构集合失败:", e);
            throw e;
        } catch (Exception e) {
            logger.error("调用Scib根据组织机构orgId获取组织机构集合失败:", e);
            throw e;
        }
    }

    @Override
    public List<OrgInfoBaseCache> getOrgInfoBaseCacheListByStoreIdsCache(List<Long> storeIds) {
        logger.info("调用Scib根据组织机构orgId获取组织机构集合-缓存 storeIds:{}", storeIds);
        if (CollectionUtils.isEmpty(storeIds)) {
            return new ArrayList<>();
        }
        List<OrgInfoBaseCache> resultList = Lists.newArrayList();
        List<Long> unCacheStoreIds = Lists.newArrayList();
        for (Long storeId : storeIds) {
            String key = OHCDataConstants.STORE_BASE_INFO_CACHE_KEY_PREFIX + ":" + storeId;
            OrgInfoBaseCache orgInfoBase = OHCDataCacheInstance.storeBaseInfoOHCDataCache.getCache(key, OrgInfoBaseCache.class);
            if (Objects.isNull(orgInfoBase)) {
                unCacheStoreIds.add(storeId);
                continue;
            }
            resultList.add(orgInfoBase);
        }
        if (CollectionUtils.isEmpty(unCacheStoreIds)) {
            return resultList;
        }
        List<OrgInfoBaseCache> orgInfoBaseList = getOrgInfoBaseCacheListByStoreIds(unCacheStoreIds);
        resultList.addAll(orgInfoBaseList);
        orgInfoBaseList.forEach(orgInfoBase -> {
            String key = OHCDataConstants.STORE_BASE_INFO_CACHE_KEY_PREFIX + ":" + orgInfoBase.getOutId();
            OHCDataCacheInstance.storeBaseInfoOHCDataCache.setCache(key, orgInfoBase, storeBaseInfoOHCTime);
        });
        return resultList;
    }

    @Override
    public List<OrgInfoBaseCache> getOrgInfoBaseCacheListByStoreIds(List<Long> storeIds) {
        try {
            logger.info("调用Scib根据组织机构orgId获取组织机构集合 storeIds:{}", storeIds);
            storeIds = storeIds.stream().distinct().collect(Collectors.toList());
            List<OrgInfoBaseCache> result=new ArrayList<>();
            Lists.partition(storeIds,MAX_PAGE_SIZE).stream().forEach(v->{
                CommonOrgCacheDTO commonOrgCacheDTO=new CommonOrgCacheDTO();
                commonOrgCacheDTO.setStoreIdList(v);
                ResponseEntity<List<OrgInfoBaseCache>> responseEntity = scibFeignClient.getOrgBaseCacheListByOutIdList(commonOrgCacheDTO);
                if (responseEntity == null
                        || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                        || responseEntity.getBody() == null) {
                    throw new BusinessErrorException("调用Scib根据组织机构storeIds获取组织机构集合失败");
                }
                result.addAll(CollectionUtils.isNotEmpty(responseEntity.getBody()) ? responseEntity.getBody() : new ArrayList<>());
            });
            return result;
        } catch (BusinessErrorException e) {
            logger.warn("调用Scib根据组织机构storeIds获取组织机构集合失败:", e);
            throw e;
        } catch (Exception e) {
            logger.error("调用Scib根据组织机构storeIds获取组织机构集合失败:", e);
            throw e;
        }
    }

    /**
     * 根据组织ID 查询该组织指定类型的上级
     *
     * @param orgId    组织ID
     * @param type     可空
     * @param idType   要查询的上级组织类型 例: 100, 300, 500
     * @param treeType 可空
     * @return
     */
    @Override
    public List<OrgDTO> listParentOrgByIdAndType(Long orgId, Integer type, Integer idType, String treeType) {
        try {
            logger.info("根据组织ID查询该组织指定类型的上级 param -> {}", orgId, type, idType, treeType);
            ResponseEntity<List<OrgDTO>> responseEntity = permissionFeignClient.getParentOrgByIdAndType(orgId, type, idType, treeType);
            if (responseEntity == null
                    || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                    || null == responseEntity.getBody()) {
                throw new BusinessErrorException("没有获取到组织机构信息");
            }
            logger.info("根据组织ID查询该组织指定类型的上级 response -> {}", JSON.toJSONString(responseEntity.getBody()));
            return responseEntity.getBody();
        } catch (BusinessErrorException e) {
            logger.warn("根据组织ID查询该组织指定类型的上级失败:", e);
            throw e;
        } catch (Exception e) {
            logger.error("根据组织ID查询该组织指定类型的上级失败:", e);
            throw e;
        }
    }

    @Override
    public List<OrgInfoBaseCache> getOrgBaseCacheBySapCode(List<String> sapCodeList, Integer orgType) {
        try{
            logger.info("调用Scib根据组织机构sapCode获取组织机构集合 sapCodeList:{}", sapCodeList);
            sapCodeList = sapCodeList.stream().distinct().collect(Collectors.toList());
            List<OrgInfoBaseCache> result=new ArrayList<>();
            Lists.partition(sapCodeList, MAX_PAGE_SIZE).stream().forEach(v->{
                ResponseEntity<List<OrgInfoBaseCache>> responseEntity = scibFeignClient.getOrgBaseCacheBySapCode(v, orgType);
                if (responseEntity == null
                        || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                        || responseEntity.getBody() == null) {
                    throw new BusinessErrorException("调用Scib根据组织机构sapCode获取组织机构集合失败");
                }
                result.addAll(CollectionUtils.isNotEmpty(responseEntity.getBody()) ? responseEntity.getBody() : new ArrayList<>());
            });
            return result;
        } catch (BusinessErrorException e) {
            logger.warn("调用Scib根据组织机构sapCode获取组织机构集合失败:", e);
            throw e;
        } catch (Exception e) {
            logger.error("调用Scib根据组织机构sapCode获取组织机构集合失败:", e);
            throw e;
        }
    }

    @Override
    public List<OrgInfoBaseCache> getOrgBaseCacheByOrgId(List<Long> orgIds, Integer orgType) {
        try{
            logger.info("调用Scib根据组织机构sapCode获取组织机构集合 orgIds:{}", orgIds);
            orgIds = orgIds.stream().distinct().collect(Collectors.toList());
            List<OrgInfoBaseCache> result=new ArrayList<>();
            Lists.partition(orgIds, MAX_PAGE_SIZE).stream().forEach(v->{
                ResponseEntity<List<OrgInfoBaseCache>> responseEntity = scibFeignClient.getOrgBaseCacheByOrgId(v, orgType);
                if (responseEntity == null
                        || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                        || responseEntity.getBody() == null) {
                    throw new BusinessErrorException("调用Scib根据组织机构sapCode获取组织机构集合失败");
                }
                result.addAll(CollectionUtils.isNotEmpty(responseEntity.getBody()) ? responseEntity.getBody() : new ArrayList<>());
            });
            return result;
        } catch (BusinessErrorException e) {
            logger.warn("调用Scib根据组织机构sapCode获取组织机构集合失败:", e);
            throw e;
        } catch (Exception e) {
            logger.error("调用Scib根据组织机构sapCode获取组织机构集合失败:", e);
            throw e;
        }
    }

    @Override
    public EmployeeVO matchAbleEmpCodeAndOrg(Long orgId, String keyword) {
        logger.info("matchAbleEmpCodeAndOrg|orgId:{}..keyword:{}..empcodeCacheAble:{}.", orgId, keyword, empcodeCacheAble);
        if(StringUtils.isBlank(keyword) || Objects.isNull(orgId)){
            return null;
        }
        try {
            String redisKey = orgId + "-" + keyword;
            RBucket<EmployeeVO> bucket = redissonClient.getBucket(redisKey);
            if (Objects.nonNull(bucket) && Objects.nonNull(bucket.get()) && empcodeCacheAble==1) {
                return bucket.get();
            } else {
                ResponseEntity<List<EmployeeVO>> responseEntity = permissionFeignClient.getEmployeesByOrgWithNameOrEmpCode(orgId, keyword);
                if (responseEntity != null
                        && (responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                        && CollectionUtils.isNotEmpty(responseEntity.getBody())) {
                    EmployeeVO employeeVO = responseEntity.getBody().stream().findFirst().get();
                    if(keyword.equals(employeeVO.getEmpCode())){
                        bucket.set( employeeVO, 8, TimeUnit.HOURS);
                    }else{
                        //防止多次访问接口
                        bucket.set( null, 8, TimeUnit.HOURS);
                    }
                    logger.info("matchAbleEmpCodeAndOrg|employeeVO:{}..", responseEntity.getBody());
                }else{
                    //防止多次访问接口
                    bucket.set( null, 8, TimeUnit.HOURS);
                }

            }
            return bucket.get();
        } catch (Exception e) {
            logger.error("matchAbleEmpCodeAndOrg|e.", e);
            throw e;
        }
    }

    @Override
    public List<OrgSimpleDTO> getUserOrgChildByOrgId(long userId, long orgId, Integer type) {
        List<OrgSimpleDTO> results = Collections.emptyList();
        try {
            List<OrgTreeSimpleDTO> orgTreeSimpleDTOList = listUserDataScopeTreesByOrgIdAndTypes(userId, orgId, Collections.singletonList(type));
            if(CollectionUtils.isEmpty(orgTreeSimpleDTOList)) {
                return null;
            }
            List<OrgTreeSimpleDTO> childrenOrgTreeSimpleDTOList = orgTreeSimpleDTOList.stream().findFirst().get().getChildren();
            if(CollectionUtils.isEmpty(childrenOrgTreeSimpleDTOList)) {
                return null;
            }
            results = childrenOrgTreeSimpleDTOList.stream().filter(v->v.getType().equals(type)).filter(v->StringUtils.isNotBlank(v.getSapcode())).map(v->{
                OrgSimpleDTO orgSimpleDTO = new OrgSimpleDTO();
                orgSimpleDTO.setId(v.getId());
                orgSimpleDTO.setName(v.getName());
                orgSimpleDTO.setShortName(v.getShortName());
                orgSimpleDTO.setOrgPath(v.getOrgPath());
                orgSimpleDTO.setType(v.getType());
                orgSimpleDTO.setParentId(v.getParentId());
                orgSimpleDTO.setOutId(v.getOutId());
                orgSimpleDTO.setSapcode(v.getSapcode());
                return orgSimpleDTO;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            logger.warn("getUserOrgChildByOrgId|e.", e);
            return null;
        }
        return results;
    }

    /**
     * @Description 根据门店ID获取店长用户数据
     * @Param storeId
     * @Return java.util.List<com.cowell.permission.dto.EmployeeDetailDTO>
     * <AUTHOR>
     * @Date 2021/3/18 16:13
     */
    @Override
    public List<EmployeeDetailWithWxDTO> getSalesclerkMastersByStoreId(Long storeOrgId) {
        logger.info("StockBacklogServiceImpl#getSalesclerkMastersByStoreId 入参 storeOrgId:{}", storeOrgId);
        // 拼装参数
        com.cowell.permission.dto.QueryEmployeeDTO queryEmployeeDTO = new com.cowell.permission.dto.QueryEmployeeDTO();
        queryEmployeeDTO.setIdType(1);
        queryEmployeeDTO.setOrgId(storeOrgId);
        queryEmployeeDTO.setQueryScope(1);
        queryEmployeeDTO.setRoleCodes(new String[]{Constants.DZ_ROLE_CODE});
        // 根据组织ID和角色编码获取相关用户数据
        logger.info("开始执行PermissionServiceImpl#getEmployeesByOrgIdAndRole方法,queryEmployeeDTO={}", queryEmployeeDTO);
        List<EmployeeDetailWithWxDTO> employeeDetailDTOList = Lists.newArrayList();
        try {
            ResponseEntity<List<EmployeeDetailWithWxDTO>> responseEntity =  permissionFeignClient.getEmployeesByOrgIdAndRole(queryEmployeeDTO);
            if (responseEntity == null || CollectionUtils.isEmpty(responseEntity.getBody())) {
                throw new BusinessErrorException("调用权限根据用户empCodes和状态查询用户信息失败");
            }
            employeeDetailDTOList = responseEntity.getBody();
        } catch (Exception e) {
            logger.warn("请求异常[PermissionServiceImpl#getEmployeesByOrgIdAndRole方法queryEmployeeDTO={}]", queryEmployeeDTO, e);
        }
        employeeDetailDTOList = employeeDetailDTOList.parallelStream()
                .filter(employeeDetailDTO -> employeeDetailDTO.getUserId() != null && !org.springframework.util.StringUtils.isEmpty(employeeDetailDTO.getEmpCode()))
                .collect(Collectors.toList());
        logger.info("StockBacklogServiceImpl#getSalesclerkMastersByStoreId 出参 employeeDetailDTOList:{}", JSON.toJSONString(employeeDetailDTOList));
        return employeeDetailDTOList;
    }

}

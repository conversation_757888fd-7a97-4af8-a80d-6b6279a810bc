package com.cowell.iscm.service;

import com.cowell.iscm.entity.IscmRecalculationRecord;
import com.cowell.iscm.service.dto.CompanyCommonDTO;
import com.cowell.iscm.service.dto.StoreCommonDTO;
import com.cowell.iscm.service.dto.TocCallbackParam;
import com.cowell.iscm.service.dto.applyParam.IscmRecalculationWarnRecordDTO;
import com.cowell.iscm.service.dto.applyParam.StoreApplyParamDTO;
import com.cowell.iscm.service.dto.applyParam.WarnReportDTO;
import com.cowell.iscm.service.dto.applyParam.WarnReportParam;
import com.cowell.iscm.service.feign.response.PageResponse;
import com.cowell.permission.dto.OrgDTO;
import com.cowell.permission.vo.OrgVO;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface RecalculationStoreApplyService {
    void recalculationByCompanyCode(String companyCode) throws Exception;

    void createRecalculationDelayJob() throws Exception;

    void deleteByCompanyCodeAndApplyDate(String companyCode, String applyDate) throws Exception;

    void pushRecalculationData(List<String> applyNos) throws Exception;

    void tocCallbackRecalculation(TocCallbackParam param) throws Exception;

    Boolean deleteCache(String companyCode, String applyDate) throws Exception;

    Long deleteRecalculationRecord(Integer days) throws Exception;

    void reRecalculationByCompanyCode(String companyCode, Boolean forceReAble) throws Exception;

    Long deleteRecalculationLog(Integer days) throws Exception;

    void recalculationByCompanyCodeAndStoreCodes(String companyCode, String storeCodes) throws Exception;

    List<WarnReportDTO> getStoreApplyRecord(WarnReportParam param) throws Exception;

    List<CompanyCommonDTO> getWarnReportComList() throws Exception;

    void recalculationByWarnReport(Long warnReportId) throws Exception;

    PageResponse<List<IscmRecalculationWarnRecordDTO>> getRecalculationWarnReport(String reportDateStart, String reportDateEnd, String companyCode, Integer page, Integer pageSize) throws Exception;

    List<StoreCommonDTO> getWarnReportStoreList(String companyCode) throws Exception;

    IscmRecalculationWarnRecordDTO getRecalculationWarnReportByCompanyCode(String companyCode) throws Exception;

    void exportRecalculationWarnReport(String reportDateStart, String reportDateEnd, String companyCode, HttpServletResponse response) throws Exception;

    void exportStoreApplyReport(WarnReportParam param, HttpServletResponse response) throws Exception;

    void pushToHd(String companyCode, String applyNo) throws Exception;

    /**
     * 根据企业id取门店白名单
     * @param companyOrgId
     * @return
     * @throws Exception
     */
    Map<String, Integer> getWhiteMap(Long companyOrgId, String storeCode);

    void pushToWmsByCompany(String companyCode, String storeCode);

    /**
     * 监控有没有生成数据
     */
    void monitorNonGen();

    /**
     * 手工请货
     * @param platform
     * @param company
     * @param storeCode
     * @param autoApplyList
     * @param specialOnceLimit
     * @param specialThirtyDaysLimit
     * @return
     * @throws Exception
     */
    List<IscmRecalculationRecord> recalculationManualByStore(Optional<OrgDTO> platform, OrgVO company, String storeCode, StoreApplyParamDTO autoApplyList, BigDecimal specialOnceLimit, BigDecimal specialThirtyDaysLimit) throws Exception;
}

package com.cowell.iscm.service.dto.returnWarehouse;

import com.cowell.iscm.entity.IscmStoreReturnExecuteOrderDetail;
import com.cowell.iscm.enums.StoreReturnExecuteProcessStatusEnum;
import com.cowell.iscm.utils.DateUtils;
import com.cowell.iscm.utils.serializer.DecimalSerializer;
import com.cowell.iscm.utils.serializer.NullNumberSerializer;
import com.cowell.iscm.utils.serializer.NullStringSerializer;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.StringJoiner;

/**
 * 门店退仓执行明细
 */
@ApiModel("门店退仓执行明细列表对象")
public class StoreReturnExecuteOrderDetailListDTO implements Serializable {
    private static final long serialVersionUID = 823347689372042497L;

    /**
     * 主键
     */
    private Long id;
    /**
     * 退仓单号
     */
    @ApiModelProperty(value = "退仓单号")
    private String returnOrderNo;

    /**
     * 平台orgId
     */
    @ApiModelProperty(value = "平台orgId")
    private Long platformOrgId;

    /**
     * 平台名称
     */
    @ApiModelProperty(value = "平台名称")
    @JsonSerialize(nullsUsing = NullStringSerializer.class)
    private String platformOrgName;

    /**
     * 公司orgId
     */
    @ApiModelProperty(value = "公司orgId")
    private Long companyOrgId;

    /**
     * 公司MDM编码
     */
    @ApiModelProperty(value = "公司MDM编码")
    @JsonSerialize(nullsUsing = NullStringSerializer.class)
    private String companyCode;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    @JsonSerialize(nullsUsing = NullStringSerializer.class)
    private String companyName;

    /**
     * 门店orgID
     */
    @ApiModelProperty(value = "门店orgID")
    private Long storeOrgId;

    /**
     * 门店MDM编码
     */
    @ApiModelProperty(value = "门店MDM编码")
    @JsonSerialize(nullsUsing = NullStringSerializer.class)
    private String storeCode;

    /**
     * 门店名称
     */
    @ApiModelProperty(value = "门店名称")
    @JsonSerialize(nullsUsing = NullStringSerializer.class)
    private String storeName;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    @JsonSerialize(nullsUsing = NullStringSerializer.class)
    private String warehouseCode;

    /**
     * 仓库名称
     */
    @ApiModelProperty(value = "仓库名称")
    @JsonSerialize(nullsUsing = NullStringSerializer.class)
    private String warehouseName;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    @JsonSerialize(nullsUsing = NullStringSerializer.class)
    private String goodsNo;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    @JsonSerialize(nullsUsing = NullStringSerializer.class)
    private String goodsName;

    /**
     * 商品描述
     */
    @ApiModelProperty(value = "商品描述")
    @JsonSerialize(nullsUsing = NullStringSerializer.class)
    private String description;

    /**
     * 批号
     */
    @ApiModelProperty(value = "生产批号")
    @JsonSerialize(nullsUsing = NullStringSerializer.class)
    private String batchNo;

    /**
     * 有效期至
     */
    @ApiModelProperty(value = "有效期至")
    @JsonSerialize(nullsUsing = NullStringSerializer.class)
    private String validityDate;

    /**
     * 生产日期
     */
    @ApiModelProperty(value = "生产日期")
    @JsonSerialize(nullsUsing = NullStringSerializer.class)
    private String produceDate;

    /**
     * 失效天数
     */
    @ApiModelProperty(value = "失效天数")
    private Integer validityDays;

    /**
     * 商品库存
     */
    @ApiModelProperty(value = "商品库存")
    @JsonSerialize(nullsUsing = NullStringSerializer.class)
    @JsonIgnore
    private BigDecimal goodsStock;

    /**
     * 实时批号库存
     */
    @ApiModelProperty(value = "实时批号库存")
    @JsonSerialize(nullsUsing = NullStringSerializer.class)
    private BigDecimal batchStock;

    /**
     * 确定退仓数量
     */
    @ApiModelProperty(value = "确定退仓数量")
    @JsonSerialize(nullsUsing = NullNumberSerializer.class, using = DecimalSerializer.class)
    private BigDecimal returnQuantity;

    /**
     * 退仓成本金额
     */
    @ApiModelProperty(value = "退仓成本金额")
    @JsonSerialize(nullsUsing = NullNumberSerializer.class, using = DecimalSerializer.class)
    private BigDecimal returnCostAmount;

    /**
     * 下发退仓数量
     */
    @ApiModelProperty(value = "下发退仓数量")
    @JsonSerialize(nullsUsing = NullNumberSerializer.class, using = DecimalSerializer.class)
    private BigDecimal issueReturnQuantity;

    /**
     * 下发退仓金额
     */
    @ApiModelProperty(value = "下发退仓金额")
    @JsonSerialize(nullsUsing = NullNumberSerializer.class, using = DecimalSerializer.class)
    private BigDecimal issueReturnAmount;

    /**
     * pos门店退仓单号
     */
    @ApiModelProperty(value = "pos门店退仓单号")
    @JsonSerialize(nullsUsing = NullStringSerializer.class)
    private String posReturnOrderNo;

    /**
     * 下发人ID
     */
    @ApiModelProperty(value = "下发人ID")
    private Long issueBy;

    /**
     * 下发人
     */
    @ApiModelProperty(value = "下发人")
    @JsonSerialize(nullsUsing = NullStringSerializer.class)
    private String issueName;

    /**
     * 下发时间
     */
    @ApiModelProperty(value = "下发时间")
    @JsonSerialize(nullsUsing = NullStringSerializer.class)
    private String gmtIssue;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonSerialize(nullsUsing = NullStringSerializer.class)
    private String gmtCreate;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonSerialize(nullsUsing = NullStringSerializer.class)
    private String gmtUpdate;

    /**
     * 创建人ID
     */
    @ApiModelProperty(value = "创建人ID")
    private Long createdBy;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @JsonSerialize(nullsUsing = NullStringSerializer.class)
    private String createdName;

    /**
     * 更新人ID
     */
    @ApiModelProperty(value = "更新人ID")
    private Long updatedBy;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    @JsonSerialize(nullsUsing = NullStringSerializer.class)
    private String updatedName;

    /**
     * 处理状态
     */
    @ApiModelProperty(value = "处理状态(0:未审批,1:审批中,2:审批拒绝,3:自动作废,4:已删除,5:下发进行中,6:下发完成,7:下发失败)")
    private Byte processStatus;

    /**
     * 处理状态
     */
    @ApiModelProperty(value = "处理状态")
    @JsonSerialize(nullsUsing = NullStringSerializer.class)
    private String processStatusDesc;
    @ApiModelProperty(value = "来源")
    @JsonSerialize(nullsUsing = NullStringSerializer.class)
    private String returnSource;

    @ApiModelProperty(value = "退仓原因类型")
    @JsonSerialize(nullsUsing = NullStringSerializer.class)
    private String returnReasonType;

    @ApiModelProperty(value = "退仓原因")
    @JsonSerialize(nullsUsing = NullStringSerializer.class)
    private String returnReason;

    @ApiModelProperty(value = "数据版本")
    @JsonSerialize(nullsUsing = NullStringSerializer.class)
    private String dataVersion;

    @ApiModelProperty(value = "数据来源ID")
    @JsonSerialize(nullsUsing = NullStringSerializer.class)
    private Long sourceId;

    @ApiModelProperty(value = "不良库存类型")
    @JsonSerialize(nullsUsing = NullStringSerializer.class)
    private String stockType;

    @ApiModelProperty(value = "采购经理处理意见")
    @JsonSerialize(nullsUsing = NullStringSerializer.class)
    private Integer disposalOpinion;

    @ApiModelProperty(value = "采购经理处理意见")
    @JsonSerialize(nullsUsing = NullStringSerializer.class)
    private String disposalOpinionDesc;

    @ApiModelProperty(value = "处理数量")
    @JsonSerialize(nullsUsing = NullStringSerializer.class)
    private String handleQuantity;

    @ApiModelProperty(value = "海典自动过审级别")
    private Integer autoAuditLevel;

    @ApiModelProperty(value = "海典单据修改控制标记")
    private Integer modifyLevel;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getReturnOrderNo() {
        return returnOrderNo;
    }

    public void setReturnOrderNo(String returnOrderNo) {
        this.returnOrderNo = returnOrderNo;
    }

    public Long getPlatformOrgId() {
        return platformOrgId;
    }

    public void setPlatformOrgId(Long platformOrgId) {
        this.platformOrgId = platformOrgId;
    }

    public String getPlatformOrgName() {
        return platformOrgName;
    }

    public void setPlatformOrgName(String platformOrgName) {
        this.platformOrgName = platformOrgName;
    }

    public Long getCompanyOrgId() {
        return companyOrgId;
    }

    public void setCompanyOrgId(Long companyOrgId) {
        this.companyOrgId = companyOrgId;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Long getStoreOrgId() {
        return storeOrgId;
    }

    public void setStoreOrgId(Long storeOrgId) {
        this.storeOrgId = storeOrgId;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getValidityDate() {
        return validityDate;
    }

    public void setValidityDate(String validityDate) {
        this.validityDate = validityDate;
    }

    public String getProduceDate() {
        return produceDate;
    }

    public void setProduceDate(String produceDate) {
        this.produceDate = produceDate;
    }

    public Integer getValidityDays() {
        return validityDays;
    }

    public void setValidityDays(Integer validityDays) {
        this.validityDays = validityDays;
    }

    public BigDecimal getGoodsStock() {
        return goodsStock;
    }

    public void setGoodsStock(BigDecimal goodsStock) {
        this.goodsStock = goodsStock;
    }

    public BigDecimal getBatchStock() {
        return batchStock;
    }

    public void setBatchStock(BigDecimal batchStock) {
        this.batchStock = batchStock;
    }

    public BigDecimal getReturnQuantity() {
        return returnQuantity;
    }

    public void setReturnQuantity(BigDecimal returnQuantity) {
        this.returnQuantity = returnQuantity;
    }

    public BigDecimal getReturnCostAmount() {
        return returnCostAmount;
    }

    public void setReturnCostAmount(BigDecimal returnCostAmount) {
        this.returnCostAmount = returnCostAmount;
    }

    public BigDecimal getIssueReturnQuantity() {
        return issueReturnQuantity;
    }

    public void setIssueReturnQuantity(BigDecimal issueReturnQuantity) {
        this.issueReturnQuantity = issueReturnQuantity;
    }

    public BigDecimal getIssueReturnAmount() {
        return issueReturnAmount;
    }

    public void setIssueReturnAmount(BigDecimal issueReturnAmount) {
        this.issueReturnAmount = issueReturnAmount;
    }

    public String getPosReturnOrderNo() {
        return posReturnOrderNo;
    }

    public void setPosReturnOrderNo(String posReturnOrderNo) {
        this.posReturnOrderNo = posReturnOrderNo;
    }

    public Long getIssueBy() {
        return issueBy;
    }

    public void setIssueBy(Long issueBy) {
        this.issueBy = issueBy;
    }

    public String getIssueName() {
        return issueName;
    }

    public void setIssueName(String issueName) {
        this.issueName = issueName;
    }

    public String getGmtIssue() {
        return gmtIssue;
    }

    public void setGmtIssue(String gmtIssue) {
        this.gmtIssue = gmtIssue;
    }

    public String getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(String gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public String getGmtUpdate() {
        return gmtUpdate;
    }

    public void setGmtUpdate(String gmtUpdate) {
        this.gmtUpdate = gmtUpdate;
    }

    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }

    public Long getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Long updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getUpdatedName() {
        return updatedName;
    }

    public void setUpdatedName(String updatedName) {
        this.updatedName = updatedName;
    }

    public Byte getProcessStatus() {
        return processStatus;
    }

    public void setProcessStatus(Byte processStatus) {
        this.processStatus = processStatus;
    }

    public String getProcessStatusDesc() {
        return processStatusDesc;
    }

    public void setProcessStatusDesc(String processStatusDesc) {
        this.processStatusDesc = processStatusDesc;
    }

    public String getReturnSource() {
        return returnSource;
    }

    public void setReturnSource(String returnSource) {
        this.returnSource = returnSource;
    }

    public String getReturnReasonType() {
        return returnReasonType;
    }

    public void setReturnReasonType(String returnReasonType) {
        this.returnReasonType = returnReasonType;
    }

    public String getReturnReason() {
        return returnReason;
    }

    public void setReturnReason(String returnReason) {
        this.returnReason = returnReason;
    }

    public String getDataVersion() {
        return dataVersion;
    }

    public void setDataVersion(String dataVersion) {
        this.dataVersion = dataVersion;
    }

    public Long getSourceId() {
        return sourceId;
    }

    public void setSourceId(Long sourceId) {
        this.sourceId = sourceId;
    }

    public String getStockType() {
        return stockType;
    }

    public void setStockType(String stockType) {
        this.stockType = stockType;
    }

    public Integer getDisposalOpinion() {
        return disposalOpinion;
    }

    public void setDisposalOpinion(Integer disposalOpinion) {
        this.disposalOpinion = disposalOpinion;
    }

    public String getDisposalOpinionDesc() {
        return disposalOpinionDesc;
    }

    public void setDisposalOpinionDesc(String disposalOpinionDesc) {
        this.disposalOpinionDesc = disposalOpinionDesc;
    }

    public String getHandleQuantity() {
        return handleQuantity;
    }

    public void setHandleQuantity(String handleQuantity) {
        this.handleQuantity = handleQuantity;
    }

    public Integer getAutoAuditLevel() {
        return autoAuditLevel;
    }

    public void setAutoAuditLevel(Integer autoAuditLevel) {
        this.autoAuditLevel = autoAuditLevel;
    }

    public Integer getModifyLevel() {
        return modifyLevel;
    }

    public void setModifyLevel(Integer modifyLevel) {
        this.modifyLevel = modifyLevel;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", StoreReturnExecuteOrderDetailListDTO.class.getSimpleName() + "[", "]")
                .add("id=" + id)
                .add("returnOrderNo=" + returnOrderNo)
                .add("platformOrgId=" + platformOrgId)
                .add("platformOrgName='" + platformOrgName + "'")
                .add("companyOrgId=" + companyOrgId)
                .add("companyCode='" + companyCode + "'")
                .add("companyName='" + companyName + "'")
                .add("storeOrgId=" + storeOrgId)
                .add("storeCode='" + storeCode + "'")
                .add("storeName='" + storeName + "'")
                .add("warehouseCode='" + warehouseCode + "'")
                .add("warehouseName='" + warehouseName + "'")
                .add("goodsNo='" + goodsNo + "'")
                .add("goodsName='" + goodsName + "'")
                .add("description='" + description + "'")
                .add("batchNo='" + batchNo + "'")
                .add("validityDate='" + validityDate + "'")
                .add("produceDate='" + produceDate + "'")
                .add("validityDays=" + validityDays)
                .add("goodsStock=" + goodsStock)
                .add("batchStock=" + batchStock)
                .add("returnQuantity=" + returnQuantity)
                .add("returnCostAmount=" + returnCostAmount)
                .add("issueReturnQuantity=" + issueReturnQuantity)
                .add("issueReturnAmount=" + issueReturnAmount)
                .add("posReturnOrderNo='" + posReturnOrderNo + "'")
                .add("issueBy=" + issueBy)
                .add("issueName='" + issueName + "'")
                .add("gmtIssue='" + gmtIssue + "'")
                .add("gmtCreate='" + gmtCreate + "'")
                .add("gmtUpdate='" + gmtUpdate + "'")
                .add("createdBy=" + createdBy)
                .add("createdName='" + createdName + "'")
                .add("updatedBy=" + updatedBy)
                .add("updatedName='" + updatedName + "'")
                .add("processStatus=" + processStatus)
                .add("processStatusDesc='" + processStatusDesc + "'")
                .add("returnSource='" + returnSource + "'")
                .add("returnReasonType='" + returnReasonType + "'")
                .add("returnReason='" + returnReason + "'")
                .add("autoAuditLevel='" + autoAuditLevel + "'")
                .add("modifyLevel='" + modifyLevel + "'")
                .toString();
    }

    public void transferToDTO(IscmStoreReturnExecuteOrderDetail order) {
        BeanUtils.copyProperties(order, this);
        // 退仓成本金额 = return_quantity * (cost_amount/register_quantity)
        this.setReturnCostAmount(StoreReturnExecuteOrderQty.genReturnCostAmount(order));
        this.setGmtCreate(DateUtils.conventDateStrByPattern(order.getGmtCreate(), DateUtils.DATE_PATTERN_DOT));
        this.setGmtUpdate(DateUtils.conventDateStrByPattern(order.getGmtUpdate(), DateUtils.DATE_PATTERN_DOT));
        this.setGmtIssue(DateUtils.conventDateStrByPattern(order.getGmtIssue(), DateUtils.DATE_PATTERN_DOT));
        this.setProcessStatusDesc(StoreReturnExecuteProcessStatusEnum.getNameByCode(order.getProcessStatus()));
    }
}

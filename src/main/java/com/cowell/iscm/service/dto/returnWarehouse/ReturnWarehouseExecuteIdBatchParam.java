package com.cowell.iscm.service.dto.returnWarehouse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

/**
 * id 参数
 *
 * <AUTHOR>
 */
@ApiModel("批量Id入参对象")
public class ReturnWarehouseExecuteIdBatchParam implements Serializable {
    private static final long serialVersionUID = 3430280539922467785L;

    @ApiModelProperty(value = "月份[yyyyMM]", required = true, example = "202111")
    private Integer month;

    @ApiModelProperty(value = "ids", required = true)
    private List<Long> ids;

    @ApiModelProperty(value = "处理状态(0:未审批,1:审批中,2:审批拒绝,3:自动作废,4:已删除,5:下发进行中,6:下发完成,7:下发失败)")
    private List<Byte> processStatusList;

    private List<Long> storeOrgIds;

    private List<String> orderNos;

    public ReturnWarehouseExecuteIdBatchParam() {
    }

    public ReturnWarehouseExecuteIdBatchParam(Integer month, List<Long> ids) {
        this.month = month;
        this.ids = ids;
    }

    public Integer getMonth() {
        return month;
    }

    public void setMonth(Integer month) {
        this.month = month;
    }

    public List<Long> getIds() {
        return ids;
    }

    public void setIds(List<Long> ids) {
        this.ids = ids;
    }

    public List<Byte> getProcessStatusList() {
        return processStatusList;
    }

    public void setProcessStatusList(List<Byte> processStatusList) {
        this.processStatusList = processStatusList;
    }

    public List<Long> getStoreOrgIds() {
        return storeOrgIds;
    }

    public void setStoreOrgIds(List<Long> storeOrgIds) {
        this.storeOrgIds = storeOrgIds;
    }

    public List<String> getOrderNos() {
        return orderNos;
    }

    public void setOrderNos(List<String> orderNos) {
        this.orderNos = orderNos;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", ReturnWarehouseExecuteIdBatchParam.class.getSimpleName() + "[", "]")
                .add("month=" + month)
                .add("ids=" + ids)
                .add("processStatusList=" + processStatusList)
                .add("storeOrgIds=" + storeOrgIds)
                .add("orderNos=" + orderNos)
                .toString();
    }
}

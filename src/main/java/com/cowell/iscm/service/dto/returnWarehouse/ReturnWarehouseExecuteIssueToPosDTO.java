package com.cowell.iscm.service.dto.returnWarehouse;

import com.cowell.iscm.utils.serializer.NullNumberSerializer;
import com.cowell.iscm.utils.serializer.NullStringSerializer;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.StringJoiner;

/**
 * 退仓执行下发传给POS的数据传输对象
 *
 * <AUTHOR>
 */
@ApiModel("退仓执行下发传给POS的数据传输对象")
public class ReturnWarehouseExecuteIssueToPosDTO implements Serializable {
    private static final long serialVersionUID = -6551189395695871984L;

    @ApiModelProperty(value = "退仓单号")
    @JsonSerialize(nullsUsing = NullStringSerializer.class)
    @JsonProperty("ISCMBILLNO")
    private String orderNo;

    @ApiModelProperty(value = "行号")
    @JsonSerialize(nullsUsing = NullStringSerializer.class)
    @JsonProperty("ISCMROWNO")
    private String rowNo;

    @ApiModelProperty(value = "退仓公司")
    @JsonSerialize(nullsUsing = NullStringSerializer.class)
    @JsonProperty("COMPID")
    private String companyCode;

    @ApiModelProperty(value = "退仓门店")
    @JsonSerialize(nullsUsing = NullStringSerializer.class)
    @JsonProperty("BUSNO")
    private String storeCode;

    @ApiModelProperty(value = "接收仓库")
    @JsonSerialize(nullsUsing = NullStringSerializer.class)
    @JsonProperty("OBJBUSNO")
    private String warehouseCode;

    @ApiModelProperty(value = "商品编码")
    @JsonSerialize(nullsUsing = NullStringSerializer.class)
    @JsonProperty("WARECODE")
    private String goodsNo;

    @ApiModelProperty(value = "批号")
    @JsonSerialize(nullsUsing = NullStringSerializer.class)
    @JsonProperty("MAKENO")
    private String batchNo;

    @ApiModelProperty(value = "下发退仓数量")
    @JsonSerialize(nullsUsing = NullNumberSerializer.class)
    @JsonProperty("WAREQTY")
    private BigDecimal issueReturnQuantity;

    @ApiModelProperty(value = "失效天数")
    @JsonSerialize(nullsUsing = NullNumberSerializer.class)
    @JsonProperty("DAYS")
    private Integer validityDays;

    @ApiModelProperty(value = "退仓申请原因")
    @JsonProperty("remark")
    private String returnReasonType;

    @ApiModelProperty(value = "海典自动过审级别")
    @JsonProperty("autoAuditLevel")
    private Integer autoAuditLevel;

    @ApiModelProperty(value = "海典单据修改控制标记")
    @JsonProperty("modifyLevel")
    private Integer modifyLevel;

    @ApiModelProperty(value = "处理数量")
    @JsonProperty("handleQuantity")
    private Integer handleQuantity;


    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getRowNo() {
        return rowNo;
    }

    public void setRowNo(String rowNo) {
        this.rowNo = rowNo;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public BigDecimal getIssueReturnQuantity() {
        return issueReturnQuantity;
    }

    public void setIssueReturnQuantity(BigDecimal issueReturnQuantity) {
        this.issueReturnQuantity = issueReturnQuantity;
    }

    public Integer getValidityDays() {
        return validityDays;
    }

    public void setValidityDays(Integer validityDays) {
        this.validityDays = validityDays;
    }

    public String getReturnReasonType() {
        return returnReasonType;
    }

    public void setReturnReasonType(String returnReasonType) {
        this.returnReasonType = returnReasonType;
    }

    public Integer getAutoAuditLevel() {
        return autoAuditLevel;
    }

    public void setAutoAuditLevel(Integer autoAuditLevel) {
        this.autoAuditLevel = autoAuditLevel;
    }

    public Integer getModifyLevel() {
        return modifyLevel;
    }

    public void setModifyLevel(Integer modifyLevel) {
        this.modifyLevel = modifyLevel;
    }

    public Integer getHandleQuantity() {
        return handleQuantity;
    }

    public void setHandleQuantity(Integer handleQuantity) {
        this.handleQuantity = handleQuantity;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", ReturnWarehouseExecuteIssueToPosDTO.class.getSimpleName() + "[", "]")
                .add("orderNo='" + orderNo + "'")
                .add("rowNo='" + rowNo + "'")
                .add("companyCode='" + companyCode + "'")
                .add("storeCode='" + storeCode + "'")
                .add("warehouseCode='" + warehouseCode + "'")
                .add("goodsNo='" + goodsNo + "'")
                .add("batchNo='" + batchNo + "'")
                .add("issueReturnQuantity=" + issueReturnQuantity)
                .add("validityDays=" + validityDays)
                .add("returnReasonType=" + returnReasonType)
                .add("autoAuditLevel=" + autoAuditLevel)
                .add("modifyLevel=" + modifyLevel)
                .toString();
    }
}

package com.cowell.iscm.service.dto.applyParam;

import com.cowell.iscm.rest.errors.ErrorCodeEnum;
import io.swagger.annotations.ApiModelProperty;

import java.util.StringJoiner;

/**
 * @ClassName CommonResult
 * <AUTHOR>
 * @Date 2020/10/12 17:58
 **/
public class CommonRes<T> {
    @ApiModelProperty("状态")
    private Integer status ;

    @ApiModelProperty("消息")
    private String msg ;

    @ApiModelProperty("数据")
    private T data ;

    public static final CommonRes OK_CREATE = new CommonRes(0, "操作成功") ;

    public CommonRes() {
    }

    public CommonRes(Integer status, String msg, T data) {
        this.status = status;
        this.msg = msg;
        this.data = data;
    }

    public CommonRes(Integer status, String msg) {
        this.status = status;
        this.msg = msg;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public static CommonRes OK(){
        CommonRes commonResult = new CommonRes() ;
        commonResult.setStatus(0);
        commonResult.setMsg("操作成功");
        return commonResult ;
    }


    public static CommonRes ERROR(String errorMsg){
        CommonRes commonResult = new CommonRes() ;
        commonResult.setStatus(1);
        commonResult.setMsg(errorMsg);
        return commonResult ;
    }

    public static CommonRes ERROR(ErrorCodeEnum errorCodeEnum){
        CommonRes commonResult = new CommonRes() ;
        commonResult.setStatus(Integer.valueOf(errorCodeEnum.getCode()));
        commonResult.setMsg(errorCodeEnum.getMsg());
        return commonResult;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", CommonRes.class.getSimpleName() + "[", "]")
                .add("status=" + status)
                .add("msg='" + msg + "'")
                .add("data=" + data)
                .toString();
    }
}
